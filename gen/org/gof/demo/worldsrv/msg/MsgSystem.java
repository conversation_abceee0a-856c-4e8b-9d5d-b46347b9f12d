// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: msg.system.proto

// Protobuf Java Version: 3.25.3
package org.gof.demo.worldsrv.msg;

public final class MsgSystem {
  private MsgSystem() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface system_gm_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.system_gm_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string content = 1;</code>
     * @return The content.
     */
    java.lang.String getContent();
    /**
     * <code>string content = 1;</code>
     * @return The bytes for content.
     */
    com.google.protobuf.ByteString
        getContentBytes();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.system_gm_c2s}
   */
  public static final class system_gm_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.system_gm_c2s)
      system_gm_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use system_gm_c2s.newBuilder() to construct.
    private system_gm_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private system_gm_c2s() {
      content_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new system_gm_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_system_gm_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_system_gm_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s.class, org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s.Builder.class);
    }

    public static final int CONTENT_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object content_ = "";
    /**
     * <code>string content = 1;</code>
     * @return The content.
     */
    @java.lang.Override
    public java.lang.String getContent() {
      java.lang.Object ref = content_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        content_ = s;
        return s;
      }
    }
    /**
     * <code>string content = 1;</code>
     * @return The bytes for content.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getContentBytes() {
      java.lang.Object ref = content_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        content_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(content_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, content_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(content_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, content_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s other = (org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s) obj;

      if (!getContent()
          .equals(other.getContent())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CONTENT_FIELD_NUMBER;
      hash = (53 * hash) + getContent().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.system_gm_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.system_gm_c2s)
        org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_system_gm_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_system_gm_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s.class, org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        content_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_system_gm_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s build() {
        org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s result = new org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.content_ = content_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s.getDefaultInstance()) return this;
        if (!other.getContent().isEmpty()) {
          content_ = other.content_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                content_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object content_ = "";
      /**
       * <code>string content = 1;</code>
       * @return The content.
       */
      public java.lang.String getContent() {
        java.lang.Object ref = content_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          content_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string content = 1;</code>
       * @return The bytes for content.
       */
      public com.google.protobuf.ByteString
          getContentBytes() {
        java.lang.Object ref = content_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          content_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string content = 1;</code>
       * @param value The content to set.
       * @return This builder for chaining.
       */
      public Builder setContent(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        content_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string content = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearContent() {
        content_ = getDefaultInstance().getContent();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string content = 1;</code>
       * @param value The bytes for content to set.
       * @return This builder for chaining.
       */
      public Builder setContentBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        content_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.system_gm_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.system_gm_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<system_gm_c2s>
        PARSER = new com.google.protobuf.AbstractParser<system_gm_c2s>() {
      @java.lang.Override
      public system_gm_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<system_gm_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<system_gm_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSystem.system_gm_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface system_gm_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.system_gm_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 code = 1;</code>
     * @return The code.
     */
    int getCode();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.system_gm_s2c}
   */
  public static final class system_gm_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.system_gm_s2c)
      system_gm_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use system_gm_s2c.newBuilder() to construct.
    private system_gm_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private system_gm_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new system_gm_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_system_gm_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_system_gm_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c.class, org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>uint32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeUInt32(1, code_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, code_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c other = (org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c) obj;

      if (getCode()
          != other.getCode()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.system_gm_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.system_gm_s2c)
        org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_system_gm_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_system_gm_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c.class, org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_system_gm_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c build() {
        org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c result = new org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>uint32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>uint32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.system_gm_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.system_gm_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<system_gm_s2c>
        PARSER = new com.google.protobuf.AbstractParser<system_gm_s2c>() {
      @java.lang.Override
      public system_gm_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<system_gm_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<system_gm_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSystem.system_gm_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface system_marquee_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.system_marquee_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <code>uint32 tips = 2;</code>
     * @return The tips.
     */
    int getTips();

    /**
     * <code>uint32 cfg_id = 3;</code>
     * @return The cfgId.
     */
    int getCfgId();

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_lang_info content = 4;</code>
     * @return Whether the content field is set.
     */
    boolean hasContent();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_lang_info content = 4;</code>
     * @return The content.
     */
    org.gof.demo.worldsrv.msg.Define.p_lang_info getContent();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_lang_info content = 4;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_lang_infoOrBuilder getContentOrBuilder();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.system_marquee_s2c}
   */
  public static final class system_marquee_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.system_marquee_s2c)
      system_marquee_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use system_marquee_s2c.newBuilder() to construct.
    private system_marquee_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private system_marquee_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new system_marquee_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_system_marquee_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_system_marquee_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c.class, org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c.Builder.class);
    }

    private int bitField0_;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_ = 0;
    /**
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int TIPS_FIELD_NUMBER = 2;
    private int tips_ = 0;
    /**
     * <code>uint32 tips = 2;</code>
     * @return The tips.
     */
    @java.lang.Override
    public int getTips() {
      return tips_;
    }

    public static final int CFG_ID_FIELD_NUMBER = 3;
    private int cfgId_ = 0;
    /**
     * <code>uint32 cfg_id = 3;</code>
     * @return The cfgId.
     */
    @java.lang.Override
    public int getCfgId() {
      return cfgId_;
    }

    public static final int CONTENT_FIELD_NUMBER = 4;
    private org.gof.demo.worldsrv.msg.Define.p_lang_info content_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_lang_info content = 4;</code>
     * @return Whether the content field is set.
     */
    @java.lang.Override
    public boolean hasContent() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_lang_info content = 4;</code>
     * @return The content.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_lang_info getContent() {
      return content_ == null ? org.gof.demo.worldsrv.msg.Define.p_lang_info.getDefaultInstance() : content_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_lang_info content = 4;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_lang_infoOrBuilder getContentOrBuilder() {
      return content_ == null ? org.gof.demo.worldsrv.msg.Define.p_lang_info.getDefaultInstance() : content_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeUInt32(1, type_);
      }
      if (tips_ != 0) {
        output.writeUInt32(2, tips_);
      }
      if (cfgId_ != 0) {
        output.writeUInt32(3, cfgId_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(4, getContent());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, type_);
      }
      if (tips_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, tips_);
      }
      if (cfgId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, cfgId_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getContent());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c other = (org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c) obj;

      if (getType()
          != other.getType()) return false;
      if (getTips()
          != other.getTips()) return false;
      if (getCfgId()
          != other.getCfgId()) return false;
      if (hasContent() != other.hasContent()) return false;
      if (hasContent()) {
        if (!getContent()
            .equals(other.getContent())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + TIPS_FIELD_NUMBER;
      hash = (53 * hash) + getTips();
      hash = (37 * hash) + CFG_ID_FIELD_NUMBER;
      hash = (53 * hash) + getCfgId();
      if (hasContent()) {
        hash = (37 * hash) + CONTENT_FIELD_NUMBER;
        hash = (53 * hash) + getContent().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.system_marquee_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.system_marquee_s2c)
        org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_system_marquee_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_system_marquee_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c.class, org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getContentFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        type_ = 0;
        tips_ = 0;
        cfgId_ = 0;
        content_ = null;
        if (contentBuilder_ != null) {
          contentBuilder_.dispose();
          contentBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_system_marquee_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c build() {
        org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c result = new org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.type_ = type_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.tips_ = tips_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.cfgId_ = cfgId_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.content_ = contentBuilder_ == null
              ? content_
              : contentBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getTips() != 0) {
          setTips(other.getTips());
        }
        if (other.getCfgId() != 0) {
          setCfgId(other.getCfgId());
        }
        if (other.hasContent()) {
          mergeContent(other.getContent());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                type_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                tips_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                cfgId_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                input.readMessage(
                    getContentFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <code>uint32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>uint32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {

        type_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private int tips_ ;
      /**
       * <code>uint32 tips = 2;</code>
       * @return The tips.
       */
      @java.lang.Override
      public int getTips() {
        return tips_;
      }
      /**
       * <code>uint32 tips = 2;</code>
       * @param value The tips to set.
       * @return This builder for chaining.
       */
      public Builder setTips(int value) {

        tips_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 tips = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTips() {
        bitField0_ = (bitField0_ & ~0x00000002);
        tips_ = 0;
        onChanged();
        return this;
      }

      private int cfgId_ ;
      /**
       * <code>uint32 cfg_id = 3;</code>
       * @return The cfgId.
       */
      @java.lang.Override
      public int getCfgId() {
        return cfgId_;
      }
      /**
       * <code>uint32 cfg_id = 3;</code>
       * @param value The cfgId to set.
       * @return This builder for chaining.
       */
      public Builder setCfgId(int value) {

        cfgId_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 cfg_id = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearCfgId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        cfgId_ = 0;
        onChanged();
        return this;
      }

      private org.gof.demo.worldsrv.msg.Define.p_lang_info content_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_lang_info, org.gof.demo.worldsrv.msg.Define.p_lang_info.Builder, org.gof.demo.worldsrv.msg.Define.p_lang_infoOrBuilder> contentBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_lang_info content = 4;</code>
       * @return Whether the content field is set.
       */
      public boolean hasContent() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_lang_info content = 4;</code>
       * @return The content.
       */
      public org.gof.demo.worldsrv.msg.Define.p_lang_info getContent() {
        if (contentBuilder_ == null) {
          return content_ == null ? org.gof.demo.worldsrv.msg.Define.p_lang_info.getDefaultInstance() : content_;
        } else {
          return contentBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_lang_info content = 4;</code>
       */
      public Builder setContent(org.gof.demo.worldsrv.msg.Define.p_lang_info value) {
        if (contentBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          content_ = value;
        } else {
          contentBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_lang_info content = 4;</code>
       */
      public Builder setContent(
          org.gof.demo.worldsrv.msg.Define.p_lang_info.Builder builderForValue) {
        if (contentBuilder_ == null) {
          content_ = builderForValue.build();
        } else {
          contentBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_lang_info content = 4;</code>
       */
      public Builder mergeContent(org.gof.demo.worldsrv.msg.Define.p_lang_info value) {
        if (contentBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
            content_ != null &&
            content_ != org.gof.demo.worldsrv.msg.Define.p_lang_info.getDefaultInstance()) {
            getContentBuilder().mergeFrom(value);
          } else {
            content_ = value;
          }
        } else {
          contentBuilder_.mergeFrom(value);
        }
        if (content_ != null) {
          bitField0_ |= 0x00000008;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_lang_info content = 4;</code>
       */
      public Builder clearContent() {
        bitField0_ = (bitField0_ & ~0x00000008);
        content_ = null;
        if (contentBuilder_ != null) {
          contentBuilder_.dispose();
          contentBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_lang_info content = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_lang_info.Builder getContentBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getContentFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_lang_info content = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_lang_infoOrBuilder getContentOrBuilder() {
        if (contentBuilder_ != null) {
          return contentBuilder_.getMessageOrBuilder();
        } else {
          return content_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_lang_info.getDefaultInstance() : content_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_lang_info content = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_lang_info, org.gof.demo.worldsrv.msg.Define.p_lang_info.Builder, org.gof.demo.worldsrv.msg.Define.p_lang_infoOrBuilder> 
          getContentFieldBuilder() {
        if (contentBuilder_ == null) {
          contentBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_lang_info, org.gof.demo.worldsrv.msg.Define.p_lang_info.Builder, org.gof.demo.worldsrv.msg.Define.p_lang_infoOrBuilder>(
                  getContent(),
                  getParentForChildren(),
                  isClean());
          content_ = null;
        }
        return contentBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.system_marquee_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.system_marquee_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<system_marquee_s2c>
        PARSER = new com.google.protobuf.AbstractParser<system_marquee_s2c>() {
      @java.lang.Override
      public system_marquee_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<system_marquee_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<system_marquee_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSystem.system_marquee_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface server_merge_list_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.server_merge_list_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.server_merge_list_c2s}
   */
  public static final class server_merge_list_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.server_merge_list_c2s)
      server_merge_list_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use server_merge_list_c2s.newBuilder() to construct.
    private server_merge_list_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private server_merge_list_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new server_merge_list_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_server_merge_list_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_server_merge_list_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s.class, org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s other = (org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.server_merge_list_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.server_merge_list_c2s)
        org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_server_merge_list_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_server_merge_list_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s.class, org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_server_merge_list_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s build() {
        org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s result = new org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.server_merge_list_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.server_merge_list_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<server_merge_list_c2s>
        PARSER = new com.google.protobuf.AbstractParser<server_merge_list_c2s>() {
      @java.lang.Override
      public server_merge_list_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<server_merge_list_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<server_merge_list_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface server_merge_list_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.server_merge_list_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 合服列表
     * </pre>
     *
     * <code>repeated uint32 server_list = 1;</code>
     * @return A list containing the serverList.
     */
    java.util.List<java.lang.Integer> getServerListList();
    /**
     * <pre>
     * 合服列表
     * </pre>
     *
     * <code>repeated uint32 server_list = 1;</code>
     * @return The count of serverList.
     */
    int getServerListCount();
    /**
     * <pre>
     * 合服列表
     * </pre>
     *
     * <code>repeated uint32 server_list = 1;</code>
     * @param index The index of the element to return.
     * @return The serverList at the given index.
     */
    int getServerList(int index);

    /**
     * <pre>
     * 关闭活动
     * </pre>
     *
     * <code>repeated uint32 act_merge_id_list = 2;</code>
     * @return A list containing the actMergeIdList.
     */
    java.util.List<java.lang.Integer> getActMergeIdListList();
    /**
     * <pre>
     * 关闭活动
     * </pre>
     *
     * <code>repeated uint32 act_merge_id_list = 2;</code>
     * @return The count of actMergeIdList.
     */
    int getActMergeIdListCount();
    /**
     * <pre>
     * 关闭活动
     * </pre>
     *
     * <code>repeated uint32 act_merge_id_list = 2;</code>
     * @param index The index of the element to return.
     * @return The actMergeIdList at the given index.
     */
    int getActMergeIdList(int index);

    /**
     * <pre>
     * 活动关闭时间范围1
     * </pre>
     *
     * <code>uint64 act_close_time1 = 3;</code>
     * @return The actCloseTime1.
     */
    long getActCloseTime1();

    /**
     * <pre>
     * 活动关闭时间范围2
     * </pre>
     *
     * <code>uint64 act_close_time2 = 4;</code>
     * @return The actCloseTime2.
     */
    long getActCloseTime2();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.server_merge_list_s2c}
   */
  public static final class server_merge_list_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.server_merge_list_s2c)
      server_merge_list_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use server_merge_list_s2c.newBuilder() to construct.
    private server_merge_list_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private server_merge_list_s2c() {
      serverList_ = emptyIntList();
      actMergeIdList_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new server_merge_list_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_server_merge_list_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_server_merge_list_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c.class, org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c.Builder.class);
    }

    public static final int SERVER_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList serverList_ =
        emptyIntList();
    /**
     * <pre>
     * 合服列表
     * </pre>
     *
     * <code>repeated uint32 server_list = 1;</code>
     * @return A list containing the serverList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getServerListList() {
      return serverList_;
    }
    /**
     * <pre>
     * 合服列表
     * </pre>
     *
     * <code>repeated uint32 server_list = 1;</code>
     * @return The count of serverList.
     */
    public int getServerListCount() {
      return serverList_.size();
    }
    /**
     * <pre>
     * 合服列表
     * </pre>
     *
     * <code>repeated uint32 server_list = 1;</code>
     * @param index The index of the element to return.
     * @return The serverList at the given index.
     */
    public int getServerList(int index) {
      return serverList_.getInt(index);
    }
    private int serverListMemoizedSerializedSize = -1;

    public static final int ACT_MERGE_ID_LIST_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList actMergeIdList_ =
        emptyIntList();
    /**
     * <pre>
     * 关闭活动
     * </pre>
     *
     * <code>repeated uint32 act_merge_id_list = 2;</code>
     * @return A list containing the actMergeIdList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getActMergeIdListList() {
      return actMergeIdList_;
    }
    /**
     * <pre>
     * 关闭活动
     * </pre>
     *
     * <code>repeated uint32 act_merge_id_list = 2;</code>
     * @return The count of actMergeIdList.
     */
    public int getActMergeIdListCount() {
      return actMergeIdList_.size();
    }
    /**
     * <pre>
     * 关闭活动
     * </pre>
     *
     * <code>repeated uint32 act_merge_id_list = 2;</code>
     * @param index The index of the element to return.
     * @return The actMergeIdList at the given index.
     */
    public int getActMergeIdList(int index) {
      return actMergeIdList_.getInt(index);
    }
    private int actMergeIdListMemoizedSerializedSize = -1;

    public static final int ACT_CLOSE_TIME1_FIELD_NUMBER = 3;
    private long actCloseTime1_ = 0L;
    /**
     * <pre>
     * 活动关闭时间范围1
     * </pre>
     *
     * <code>uint64 act_close_time1 = 3;</code>
     * @return The actCloseTime1.
     */
    @java.lang.Override
    public long getActCloseTime1() {
      return actCloseTime1_;
    }

    public static final int ACT_CLOSE_TIME2_FIELD_NUMBER = 4;
    private long actCloseTime2_ = 0L;
    /**
     * <pre>
     * 活动关闭时间范围2
     * </pre>
     *
     * <code>uint64 act_close_time2 = 4;</code>
     * @return The actCloseTime2.
     */
    @java.lang.Override
    public long getActCloseTime2() {
      return actCloseTime2_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (getServerListList().size() > 0) {
        output.writeUInt32NoTag(10);
        output.writeUInt32NoTag(serverListMemoizedSerializedSize);
      }
      for (int i = 0; i < serverList_.size(); i++) {
        output.writeUInt32NoTag(serverList_.getInt(i));
      }
      if (getActMergeIdListList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(actMergeIdListMemoizedSerializedSize);
      }
      for (int i = 0; i < actMergeIdList_.size(); i++) {
        output.writeUInt32NoTag(actMergeIdList_.getInt(i));
      }
      if (actCloseTime1_ != 0L) {
        output.writeUInt64(3, actCloseTime1_);
      }
      if (actCloseTime2_ != 0L) {
        output.writeUInt64(4, actCloseTime2_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < serverList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(serverList_.getInt(i));
        }
        size += dataSize;
        if (!getServerListList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        serverListMemoizedSerializedSize = dataSize;
      }
      {
        int dataSize = 0;
        for (int i = 0; i < actMergeIdList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(actMergeIdList_.getInt(i));
        }
        size += dataSize;
        if (!getActMergeIdListList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        actMergeIdListMemoizedSerializedSize = dataSize;
      }
      if (actCloseTime1_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(3, actCloseTime1_);
      }
      if (actCloseTime2_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(4, actCloseTime2_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c other = (org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c) obj;

      if (!getServerListList()
          .equals(other.getServerListList())) return false;
      if (!getActMergeIdListList()
          .equals(other.getActMergeIdListList())) return false;
      if (getActCloseTime1()
          != other.getActCloseTime1()) return false;
      if (getActCloseTime2()
          != other.getActCloseTime2()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getServerListCount() > 0) {
        hash = (37 * hash) + SERVER_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getServerListList().hashCode();
      }
      if (getActMergeIdListCount() > 0) {
        hash = (37 * hash) + ACT_MERGE_ID_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getActMergeIdListList().hashCode();
      }
      hash = (37 * hash) + ACT_CLOSE_TIME1_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getActCloseTime1());
      hash = (37 * hash) + ACT_CLOSE_TIME2_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getActCloseTime2());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.server_merge_list_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.server_merge_list_s2c)
        org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_server_merge_list_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_server_merge_list_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c.class, org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        serverList_ = emptyIntList();
        actMergeIdList_ = emptyIntList();
        actCloseTime1_ = 0L;
        actCloseTime2_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_server_merge_list_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c build() {
        org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c result = new org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          serverList_.makeImmutable();
          result.serverList_ = serverList_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          actMergeIdList_.makeImmutable();
          result.actMergeIdList_ = actMergeIdList_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.actCloseTime1_ = actCloseTime1_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.actCloseTime2_ = actCloseTime2_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c.getDefaultInstance()) return this;
        if (!other.serverList_.isEmpty()) {
          if (serverList_.isEmpty()) {
            serverList_ = other.serverList_;
            serverList_.makeImmutable();
            bitField0_ |= 0x00000001;
          } else {
            ensureServerListIsMutable();
            serverList_.addAll(other.serverList_);
          }
          onChanged();
        }
        if (!other.actMergeIdList_.isEmpty()) {
          if (actMergeIdList_.isEmpty()) {
            actMergeIdList_ = other.actMergeIdList_;
            actMergeIdList_.makeImmutable();
            bitField0_ |= 0x00000002;
          } else {
            ensureActMergeIdListIsMutable();
            actMergeIdList_.addAll(other.actMergeIdList_);
          }
          onChanged();
        }
        if (other.getActCloseTime1() != 0L) {
          setActCloseTime1(other.getActCloseTime1());
        }
        if (other.getActCloseTime2() != 0L) {
          setActCloseTime2(other.getActCloseTime2());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                int v = input.readUInt32();
                ensureServerListIsMutable();
                serverList_.addInt(v);
                break;
              } // case 8
              case 10: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureServerListIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  serverList_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 10
              case 16: {
                int v = input.readUInt32();
                ensureActMergeIdListIsMutable();
                actMergeIdList_.addInt(v);
                break;
              } // case 16
              case 18: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureActMergeIdListIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  actMergeIdList_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 18
              case 24: {
                actCloseTime1_ = input.readUInt64();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                actCloseTime2_ = input.readUInt64();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.IntList serverList_ = emptyIntList();
      private void ensureServerListIsMutable() {
        if (!serverList_.isModifiable()) {
          serverList_ = makeMutableCopy(serverList_);
        }
        bitField0_ |= 0x00000001;
      }
      /**
       * <pre>
       * 合服列表
       * </pre>
       *
       * <code>repeated uint32 server_list = 1;</code>
       * @return A list containing the serverList.
       */
      public java.util.List<java.lang.Integer>
          getServerListList() {
        serverList_.makeImmutable();
        return serverList_;
      }
      /**
       * <pre>
       * 合服列表
       * </pre>
       *
       * <code>repeated uint32 server_list = 1;</code>
       * @return The count of serverList.
       */
      public int getServerListCount() {
        return serverList_.size();
      }
      /**
       * <pre>
       * 合服列表
       * </pre>
       *
       * <code>repeated uint32 server_list = 1;</code>
       * @param index The index of the element to return.
       * @return The serverList at the given index.
       */
      public int getServerList(int index) {
        return serverList_.getInt(index);
      }
      /**
       * <pre>
       * 合服列表
       * </pre>
       *
       * <code>repeated uint32 server_list = 1;</code>
       * @param index The index to set the value at.
       * @param value The serverList to set.
       * @return This builder for chaining.
       */
      public Builder setServerList(
          int index, int value) {

        ensureServerListIsMutable();
        serverList_.setInt(index, value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 合服列表
       * </pre>
       *
       * <code>repeated uint32 server_list = 1;</code>
       * @param value The serverList to add.
       * @return This builder for chaining.
       */
      public Builder addServerList(int value) {

        ensureServerListIsMutable();
        serverList_.addInt(value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 合服列表
       * </pre>
       *
       * <code>repeated uint32 server_list = 1;</code>
       * @param values The serverList to add.
       * @return This builder for chaining.
       */
      public Builder addAllServerList(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureServerListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, serverList_);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 合服列表
       * </pre>
       *
       * <code>repeated uint32 server_list = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearServerList() {
        serverList_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList actMergeIdList_ = emptyIntList();
      private void ensureActMergeIdListIsMutable() {
        if (!actMergeIdList_.isModifiable()) {
          actMergeIdList_ = makeMutableCopy(actMergeIdList_);
        }
        bitField0_ |= 0x00000002;
      }
      /**
       * <pre>
       * 关闭活动
       * </pre>
       *
       * <code>repeated uint32 act_merge_id_list = 2;</code>
       * @return A list containing the actMergeIdList.
       */
      public java.util.List<java.lang.Integer>
          getActMergeIdListList() {
        actMergeIdList_.makeImmutable();
        return actMergeIdList_;
      }
      /**
       * <pre>
       * 关闭活动
       * </pre>
       *
       * <code>repeated uint32 act_merge_id_list = 2;</code>
       * @return The count of actMergeIdList.
       */
      public int getActMergeIdListCount() {
        return actMergeIdList_.size();
      }
      /**
       * <pre>
       * 关闭活动
       * </pre>
       *
       * <code>repeated uint32 act_merge_id_list = 2;</code>
       * @param index The index of the element to return.
       * @return The actMergeIdList at the given index.
       */
      public int getActMergeIdList(int index) {
        return actMergeIdList_.getInt(index);
      }
      /**
       * <pre>
       * 关闭活动
       * </pre>
       *
       * <code>repeated uint32 act_merge_id_list = 2;</code>
       * @param index The index to set the value at.
       * @param value The actMergeIdList to set.
       * @return This builder for chaining.
       */
      public Builder setActMergeIdList(
          int index, int value) {

        ensureActMergeIdListIsMutable();
        actMergeIdList_.setInt(index, value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 关闭活动
       * </pre>
       *
       * <code>repeated uint32 act_merge_id_list = 2;</code>
       * @param value The actMergeIdList to add.
       * @return This builder for chaining.
       */
      public Builder addActMergeIdList(int value) {

        ensureActMergeIdListIsMutable();
        actMergeIdList_.addInt(value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 关闭活动
       * </pre>
       *
       * <code>repeated uint32 act_merge_id_list = 2;</code>
       * @param values The actMergeIdList to add.
       * @return This builder for chaining.
       */
      public Builder addAllActMergeIdList(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureActMergeIdListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, actMergeIdList_);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 关闭活动
       * </pre>
       *
       * <code>repeated uint32 act_merge_id_list = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearActMergeIdList() {
        actMergeIdList_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private long actCloseTime1_ ;
      /**
       * <pre>
       * 活动关闭时间范围1
       * </pre>
       *
       * <code>uint64 act_close_time1 = 3;</code>
       * @return The actCloseTime1.
       */
      @java.lang.Override
      public long getActCloseTime1() {
        return actCloseTime1_;
      }
      /**
       * <pre>
       * 活动关闭时间范围1
       * </pre>
       *
       * <code>uint64 act_close_time1 = 3;</code>
       * @param value The actCloseTime1 to set.
       * @return This builder for chaining.
       */
      public Builder setActCloseTime1(long value) {

        actCloseTime1_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 活动关闭时间范围1
       * </pre>
       *
       * <code>uint64 act_close_time1 = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearActCloseTime1() {
        bitField0_ = (bitField0_ & ~0x00000004);
        actCloseTime1_ = 0L;
        onChanged();
        return this;
      }

      private long actCloseTime2_ ;
      /**
       * <pre>
       * 活动关闭时间范围2
       * </pre>
       *
       * <code>uint64 act_close_time2 = 4;</code>
       * @return The actCloseTime2.
       */
      @java.lang.Override
      public long getActCloseTime2() {
        return actCloseTime2_;
      }
      /**
       * <pre>
       * 活动关闭时间范围2
       * </pre>
       *
       * <code>uint64 act_close_time2 = 4;</code>
       * @param value The actCloseTime2 to set.
       * @return This builder for chaining.
       */
      public Builder setActCloseTime2(long value) {

        actCloseTime2_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 活动关闭时间范围2
       * </pre>
       *
       * <code>uint64 act_close_time2 = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearActCloseTime2() {
        bitField0_ = (bitField0_ & ~0x00000008);
        actCloseTime2_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.server_merge_list_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.server_merge_list_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<server_merge_list_s2c>
        PARSER = new com.google.protobuf.AbstractParser<server_merge_list_s2c>() {
      @java.lang.Override
      public server_merge_list_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<server_merge_list_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<server_merge_list_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSystem.server_merge_list_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface server_cross_join_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.server_cross_join_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 跨服功能id
     * </pre>
     *
     * <code>int32 funcId = 1;</code>
     * @return The funcId.
     */
    int getFuncId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.server_cross_join_c2s}
   */
  public static final class server_cross_join_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.server_cross_join_c2s)
      server_cross_join_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use server_cross_join_c2s.newBuilder() to construct.
    private server_cross_join_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private server_cross_join_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new server_cross_join_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_server_cross_join_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_server_cross_join_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s.class, org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s.Builder.class);
    }

    public static final int FUNCID_FIELD_NUMBER = 1;
    private int funcId_ = 0;
    /**
     * <pre>
     * 跨服功能id
     * </pre>
     *
     * <code>int32 funcId = 1;</code>
     * @return The funcId.
     */
    @java.lang.Override
    public int getFuncId() {
      return funcId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (funcId_ != 0) {
        output.writeInt32(1, funcId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (funcId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, funcId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s other = (org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s) obj;

      if (getFuncId()
          != other.getFuncId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + FUNCID_FIELD_NUMBER;
      hash = (53 * hash) + getFuncId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.server_cross_join_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.server_cross_join_c2s)
        org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_server_cross_join_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_server_cross_join_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s.class, org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        funcId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_server_cross_join_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s build() {
        org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s result = new org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.funcId_ = funcId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s.getDefaultInstance()) return this;
        if (other.getFuncId() != 0) {
          setFuncId(other.getFuncId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                funcId_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int funcId_ ;
      /**
       * <pre>
       * 跨服功能id
       * </pre>
       *
       * <code>int32 funcId = 1;</code>
       * @return The funcId.
       */
      @java.lang.Override
      public int getFuncId() {
        return funcId_;
      }
      /**
       * <pre>
       * 跨服功能id
       * </pre>
       *
       * <code>int32 funcId = 1;</code>
       * @param value The funcId to set.
       * @return This builder for chaining.
       */
      public Builder setFuncId(int value) {

        funcId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 跨服功能id
       * </pre>
       *
       * <code>int32 funcId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearFuncId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        funcId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.server_cross_join_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.server_cross_join_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<server_cross_join_c2s>
        PARSER = new com.google.protobuf.AbstractParser<server_cross_join_c2s>() {
      @java.lang.Override
      public server_cross_join_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<server_cross_join_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<server_cross_join_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface server_cross_join_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.server_cross_join_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 合服列表
     * </pre>
     *
     * <code>repeated uint32 server_list = 1;</code>
     * @return A list containing the serverList.
     */
    java.util.List<java.lang.Integer> getServerListList();
    /**
     * <pre>
     * 合服列表
     * </pre>
     *
     * <code>repeated uint32 server_list = 1;</code>
     * @return The count of serverList.
     */
    int getServerListCount();
    /**
     * <pre>
     * 合服列表
     * </pre>
     *
     * <code>repeated uint32 server_list = 1;</code>
     * @param index The index of the element to return.
     * @return The serverList at the given index.
     */
    int getServerList(int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.server_cross_join_s2c}
   */
  public static final class server_cross_join_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.server_cross_join_s2c)
      server_cross_join_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use server_cross_join_s2c.newBuilder() to construct.
    private server_cross_join_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private server_cross_join_s2c() {
      serverList_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new server_cross_join_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_server_cross_join_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_server_cross_join_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c.class, org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c.Builder.class);
    }

    public static final int SERVER_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList serverList_ =
        emptyIntList();
    /**
     * <pre>
     * 合服列表
     * </pre>
     *
     * <code>repeated uint32 server_list = 1;</code>
     * @return A list containing the serverList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getServerListList() {
      return serverList_;
    }
    /**
     * <pre>
     * 合服列表
     * </pre>
     *
     * <code>repeated uint32 server_list = 1;</code>
     * @return The count of serverList.
     */
    public int getServerListCount() {
      return serverList_.size();
    }
    /**
     * <pre>
     * 合服列表
     * </pre>
     *
     * <code>repeated uint32 server_list = 1;</code>
     * @param index The index of the element to return.
     * @return The serverList at the given index.
     */
    public int getServerList(int index) {
      return serverList_.getInt(index);
    }
    private int serverListMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (getServerListList().size() > 0) {
        output.writeUInt32NoTag(10);
        output.writeUInt32NoTag(serverListMemoizedSerializedSize);
      }
      for (int i = 0; i < serverList_.size(); i++) {
        output.writeUInt32NoTag(serverList_.getInt(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < serverList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(serverList_.getInt(i));
        }
        size += dataSize;
        if (!getServerListList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        serverListMemoizedSerializedSize = dataSize;
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c other = (org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c) obj;

      if (!getServerListList()
          .equals(other.getServerListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getServerListCount() > 0) {
        hash = (37 * hash) + SERVER_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getServerListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.server_cross_join_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.server_cross_join_s2c)
        org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_server_cross_join_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_server_cross_join_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c.class, org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        serverList_ = emptyIntList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSystem.internal_static_org_gof_demo_worldsrv_msg_server_cross_join_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c build() {
        org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c result = new org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          serverList_.makeImmutable();
          result.serverList_ = serverList_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c.getDefaultInstance()) return this;
        if (!other.serverList_.isEmpty()) {
          if (serverList_.isEmpty()) {
            serverList_ = other.serverList_;
            serverList_.makeImmutable();
            bitField0_ |= 0x00000001;
          } else {
            ensureServerListIsMutable();
            serverList_.addAll(other.serverList_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                int v = input.readUInt32();
                ensureServerListIsMutable();
                serverList_.addInt(v);
                break;
              } // case 8
              case 10: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureServerListIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  serverList_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.IntList serverList_ = emptyIntList();
      private void ensureServerListIsMutable() {
        if (!serverList_.isModifiable()) {
          serverList_ = makeMutableCopy(serverList_);
        }
        bitField0_ |= 0x00000001;
      }
      /**
       * <pre>
       * 合服列表
       * </pre>
       *
       * <code>repeated uint32 server_list = 1;</code>
       * @return A list containing the serverList.
       */
      public java.util.List<java.lang.Integer>
          getServerListList() {
        serverList_.makeImmutable();
        return serverList_;
      }
      /**
       * <pre>
       * 合服列表
       * </pre>
       *
       * <code>repeated uint32 server_list = 1;</code>
       * @return The count of serverList.
       */
      public int getServerListCount() {
        return serverList_.size();
      }
      /**
       * <pre>
       * 合服列表
       * </pre>
       *
       * <code>repeated uint32 server_list = 1;</code>
       * @param index The index of the element to return.
       * @return The serverList at the given index.
       */
      public int getServerList(int index) {
        return serverList_.getInt(index);
      }
      /**
       * <pre>
       * 合服列表
       * </pre>
       *
       * <code>repeated uint32 server_list = 1;</code>
       * @param index The index to set the value at.
       * @param value The serverList to set.
       * @return This builder for chaining.
       */
      public Builder setServerList(
          int index, int value) {

        ensureServerListIsMutable();
        serverList_.setInt(index, value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 合服列表
       * </pre>
       *
       * <code>repeated uint32 server_list = 1;</code>
       * @param value The serverList to add.
       * @return This builder for chaining.
       */
      public Builder addServerList(int value) {

        ensureServerListIsMutable();
        serverList_.addInt(value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 合服列表
       * </pre>
       *
       * <code>repeated uint32 server_list = 1;</code>
       * @param values The serverList to add.
       * @return This builder for chaining.
       */
      public Builder addAllServerList(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureServerListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, serverList_);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 合服列表
       * </pre>
       *
       * <code>repeated uint32 server_list = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearServerList() {
        serverList_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.server_cross_join_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.server_cross_join_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<server_cross_join_s2c>
        PARSER = new com.google.protobuf.AbstractParser<server_cross_join_s2c>() {
      @java.lang.Override
      public server_cross_join_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<server_cross_join_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<server_cross_join_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSystem.server_cross_join_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_system_gm_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_system_gm_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_system_gm_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_system_gm_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_system_marquee_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_system_marquee_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_server_merge_list_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_server_merge_list_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_server_merge_list_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_server_merge_list_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_server_cross_join_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_server_cross_join_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_server_cross_join_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_server_cross_join_s2c_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\020msg.system.proto\022\031org.gof.demo.worldsr" +
      "v.msg\032\roptions.proto\032\014define.proto\"\'\n\rsy" +
      "stem_gm_c2s\022\017\n\007content\030\001 \001(\t:\005\210\303\032\201.\"$\n\rs" +
      "ystem_gm_s2c\022\014\n\004code\030\001 \001(\r:\005\210\303\032\201.\"\200\001\n\022sy" +
      "stem_marquee_s2c\022\014\n\004type\030\001 \001(\r\022\014\n\004tips\030\002" +
      " \001(\r\022\016\n\006cfg_id\030\003 \001(\r\0227\n\007content\030\004 \001(\0132&." +
      "org.gof.demo.worldsrv.msg.p_lang_info:\005\210" +
      "\303\032\202.\"\036\n\025server_merge_list_c2s:\005\210\303\032\203.\"\200\001\n" +
      "\025server_merge_list_s2c\022\023\n\013server_list\030\001 " +
      "\003(\r\022\031\n\021act_merge_id_list\030\002 \003(\r\022\027\n\017act_cl" +
      "ose_time1\030\003 \001(\004\022\027\n\017act_close_time2\030\004 \001(\004" +
      ":\005\210\303\032\203.\".\n\025server_cross_join_c2s\022\016\n\006func" +
      "Id\030\001 \001(\005:\005\210\303\032\204.\"3\n\025server_cross_join_s2c" +
      "\022\023\n\013server_list\030\001 \003(\r:\005\210\303\032\204.b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          org.gof.demo.worldsrv.msg.Options.getDescriptor(),
          org.gof.demo.worldsrv.msg.Define.getDescriptor(),
        });
    internal_static_org_gof_demo_worldsrv_msg_system_gm_c2s_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_org_gof_demo_worldsrv_msg_system_gm_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_system_gm_c2s_descriptor,
        new java.lang.String[] { "Content", });
    internal_static_org_gof_demo_worldsrv_msg_system_gm_s2c_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_org_gof_demo_worldsrv_msg_system_gm_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_system_gm_s2c_descriptor,
        new java.lang.String[] { "Code", });
    internal_static_org_gof_demo_worldsrv_msg_system_marquee_s2c_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_org_gof_demo_worldsrv_msg_system_marquee_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_system_marquee_s2c_descriptor,
        new java.lang.String[] { "Type", "Tips", "CfgId", "Content", });
    internal_static_org_gof_demo_worldsrv_msg_server_merge_list_c2s_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_org_gof_demo_worldsrv_msg_server_merge_list_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_server_merge_list_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_server_merge_list_s2c_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_org_gof_demo_worldsrv_msg_server_merge_list_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_server_merge_list_s2c_descriptor,
        new java.lang.String[] { "ServerList", "ActMergeIdList", "ActCloseTime1", "ActCloseTime2", });
    internal_static_org_gof_demo_worldsrv_msg_server_cross_join_c2s_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_org_gof_demo_worldsrv_msg_server_cross_join_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_server_cross_join_c2s_descriptor,
        new java.lang.String[] { "FuncId", });
    internal_static_org_gof_demo_worldsrv_msg_server_cross_join_s2c_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_org_gof_demo_worldsrv_msg_server_cross_join_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_server_cross_join_s2c_descriptor,
        new java.lang.String[] { "ServerList", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(org.gof.demo.worldsrv.msg.Options.msgid);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    org.gof.demo.worldsrv.msg.Options.getDescriptor();
    org.gof.demo.worldsrv.msg.Define.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
