##############
#  实例数量  #
##############
#跨服服务器启动数量
node.startup.num.cross=1

http.port1=13201

##################
#   admin-cfg    #
##################
node.addr.admin0=tcp://127.0.0.1:13000
serv.node.adminCenter=admin0

##############
#    node    #
##############
#启动的nodeID
node.start.id=cross0
# node地址
node.addr.cross0=tcp://127.0.0.1:13100
http.serverlist.server=http://192.168.0.79/openapi/gameServer/Serializable
##############
#    port    #
##############

#Http服务实例数
port.startup.num.http=3
port.startwith.http0=cross0
port.startwith.http1=cross0
port.startwith.http2=cross0
#http异步请求Port
port.startup.num.httpAsync=3

# 中心服逻辑Port
port.startup.num.game=5
port.startwith.game0=cross0
port.startwith.game1=cross0
port.startwith.game2=cross0
port.startwith.game3=cross0
port.startwith.game4=cross0

# 数据库缓存和操作分配服务
port.startup.num.dbPart=3
port.startwith.dbPart0=cross0
port.startwith.dbPart1=cross0
port.startwith.dbPart2=cross0

# 数据库执行服务DBLine
port.startup.num.dbLine=5
port.startwith.dbLine0=cross0
port.startwith.dbLine1=cross0
port.startwith.dbLine2=cross0
port.startwith.dbLine3=cross0
port.startwith.dbLine4=cross0
# ID分配服务
port.startwith.idAllot=cross0

##############
#    serv    #
##############

# id服务
serv.startwith.idAllot=idAllot
serv.startwith.PF=game0
serv.startwith.gm=game0

serv.startwith.serverSelect=game1
#竞技场
serv.startwith.arenaRanked=game1
serv.startwith.guildLeague=game1
serv.startwith.arenaCross=game1
#分配服务
serv.startwith.matchAllot=game2
serv.startwith.activityCross=game2
serv.startwith.rankCross=game2
serv.startwith.blackMarket=game3
#arenaNew是test用
serv.startwith.arenaNew=game0
serv.startwith.crossWar=game3
serv.startwith.kungFuRace=game4

##############
#    other    #
##############
http.gm.ip=*************
http.gm.port=8082