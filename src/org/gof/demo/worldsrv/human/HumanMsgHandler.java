package org.gof.demo.worldsrv.human;

import org.gof.core.support.observer.MsgReceiver;
import org.gof.demo.seam.msg.MsgParam;
import org.gof.demo.worldsrv.appearance.AppearanceManager;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.jobs.JobsManager;
import org.gof.demo.worldsrv.msg.*;
import org.gof.demo.worldsrv.support.Log;

public class HumanMsgHandler {
	

//	/**
//	 * 初始化玩家详细信息
//	 * @param param
//	 */
//	@MsgReceiver(CSInitData.class)
//	public void onCSInitData(MsgParam param){
//		HumanObject humanObj = param.getHumanObject();
//		HumanManager.inst().sendInitDataToClient(humanObj);
//	}
//
//
//	/**
//	 * 获取服务器的当前时间(单位:毫秒)
//	 * @param param
//	 */
//	@MsgReceiver(CSServerTime.class)
//	public void onCSServerTime(MsgParam param){
//		HumanObject humanObj = param.getHumanObject();
//		HumanManager.inst().sendServerTimeToClientSync(humanObj);
//	}
//
//	@MsgReceiver(MsgCommon.CSModUnlock.class)
//	public void onCSModUnlock(MsgParam param){
//		HumanObject humanObj = param.getHumanObject();
//		MsgCommon.CSModUnlock msg = param.getMsg();
//		HumanManager.inst().modUnlock(humanObj, msg.getMod());
//
//	}
//
//	@MsgReceiver(MsgCommon.CSPullBornPoint.class)
//	public void onCSPullBornPoint(MsgParam param){
//		HumanObject humanObj = param.getHumanObject();
//		HumanManager.inst().pullBornPoint(humanObj);
//	}
//
//
//
//	/**
//	 * 客户端设置（纯客户端专用）
//	 * <AUTHOR>
//	 * @Date 2023/6/15
//	 * @Param
//	 */
//	@MsgReceiver(MsgCommon.CSClientSetInfo.class)
//	public void onCSClientSetInfo(MsgParam param) {
//		HumanObject humanObj = param.getHumanObject();
//		MsgCommon.CSClientSetInfo msg = param.getMsg();
//		HumanManager.inst().onCSClientSetInfo(humanObj, msg.getClientJSON(), msg.getClientJSONKey());
//	}
//
//
//
//	@MsgReceiver(MsgCommon.CSHumanLogout.class)
//	public void onCSHumanLogout(MsgParam param) {
//		HumanObject humanObj = param.getHumanObject();
//		MsgCommon.CSHumanLogout msg = param.getMsg();
//		HumanManager.inst().onCSHumanLogout(humanObj, msg.getIsKick());
//	}
    /**
     * 处理角色信息
     * @param param
     */
    @MsgReceiver(MsgRole.role_info_c2s.class)
    public void _msg_role_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_info_c2s msg = param.getMsg();
//        HumanManager.inst()._msg_role_info_c2s(humanObj);
    }

    /**
     * 处理角色信息变更
     * @param param
     */
    @MsgReceiver(MsgRole.role_info_change_c2s.class)
    public void role_info_change_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_info_change_c2s msg = param.getMsg();
        //HumanManager.inst().handleRoleInfoChangeC2S(humanObj, msg);
    }


    /**
     * 处理角色改变职业
     * @param param
     */
    @MsgReceiver(MsgRole.role_change_job_c2s.class)
    public void role_change_job_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_change_job_c2s msg = param.getMsg();
        JobsManager.inst()._msg_role_change_job_c2s(humanObj, msg.getJob());
    }

    /**
     * 处理角色总SP信息
     * @param param
     */
    @MsgReceiver(MsgRole.role_total_sp_info_c2s.class)
    public void role_total_sp_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_total_sp_info_c2s msg = param.getMsg();
        HumanManager.inst().sendMsg_role_total_sp_info_s2c(humanObj, msg.getType());
    }

    /**
     * 处理角色职业形象
     * @param param
     */
    @MsgReceiver(MsgRole.role_job_figure_c2s.class)
    public void role_job_figure_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_job_figure_c2s msg = param.getMsg();
        HumanManager.inst().role_job_figure_c2s(humanObj, msg.getJob());
    }

    /**
     * 处理角色改变名称
     * @param param
     */
    @MsgReceiver(MsgRole.role_rename_c2s.class)
    public void role_rename_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_rename_c2s msg = param.getMsg();
        HumanManager.inst().role_rename_c2s(humanObj,msg.getName());
    }

    /**
     * 处理角色其它信息
     * @param param
     */
    @MsgReceiver(MsgRole.role_others_c2s.class)
    public void role_others_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_others_c2s msg = param.getMsg();
        int source = msg.getSource();
        if (msg.getRoleIdCount() > 0) {
            HumanManager.inst().role_others_c2s(humanObj, msg.getRoleId(0), source);
        }
    }

    /**
     * 处理角色设置信息
     * @param param
     */
    @MsgReceiver(MsgRole.role_setting_info_c2s.class)
    public void role_setting_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        HumanManager.inst().role_setting_info_c2s(humanObj);
    }

    /**
     * 处理角色设置设置
     * @param param
     */
    @MsgReceiver(MsgRole.role_set_setting_c2s.class)
    public void role_set_setting_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_set_setting_c2s msg = param.getMsg();
        HumanManager.inst().role_set_setting_c2s(humanObj,msg.getType(),msg.getValue());
    }

    /**
     * 处理角色设置设置S2C消息
     * @param param
     */
    @MsgReceiver(MsgRole.role_set_setting_s2c.class)
    public void role_set_setting_s2c(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_set_setting_s2c msg = param.getMsg();
        //HumanManager.inst().role_set_setting_s2c(humanObj);
    }

    /**
     * 处理角色改变性别
     * @param param
     */
    @MsgReceiver(MsgRole.role_change_gender_c2s.class)
    public void role_change_gender_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_change_gender_c2s msg = param.getMsg();
        //HumanManager.inst().role_change_gender_c2s(humanObj);
    }

    /**
     * 处理角色皮肤列表
     * @param param
     */
    @MsgReceiver(MsgRole.role_skin_list_c2s.class)
    public void role_skin_list_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        HumanManager.inst().handleRoleSkinListC2S(humanObj);
    }

    /**
     * 处理角色改变皮肤
     * @param param
     */
    @MsgReceiver(MsgRole.role_change_skin_c2s.class)
    public void role_change_skin_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_change_skin_c2s msg = param.getMsg();
        HumanManager.inst().role_change_skin_c2s(humanObj,msg.getId());
    }

    /**
     * 处理角色已使用皮肤列表
     * @param param
     */
    @MsgReceiver(MsgRole.role_used_skin_list_c2s.class)
    public void role_used_skin_list_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        HumanManager.inst().role_used_skin_list_c2s(humanObj);
    }

    /**
     * 处理角色通用加速
     * @param param
     */
    @MsgReceiver(MsgRole.role_common_speed_c2s.class)
    public void role_common_speed_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_common_speed_c2s msg = param.getMsg();
        HumanManager.inst().handleRoleCommonSpeedC2S(humanObj, msg.getType(), msg.getGoodsId(), msg.getUseNum(), msg.getArgs1());
    }

    /**
     * 处理角色七日登录信息
     * @param param
     */
    @MsgReceiver(MsgRole.role_seven_login_info_c2s.class)
    public void role_seven_login_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        HumanManager.inst().role_seven_login_info_c2s(humanObj);
    }

    /**
     * 处理角色七日登录奖励
     * @param param
     */
    @MsgReceiver(MsgRole.role_seven_login_reward_c2s.class)
    public void role_seven_login_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_seven_login_reward_c2s msg = param.getMsg();
        HumanManager.inst().role_seven_login_reward_c2s(humanObj, msg.getDay());
    }

    /**
     * 处理角色改变请求
     * @param param
     */
    @MsgReceiver(MsgRole.role_change_req_c2s.class)
    public void role_change_req_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_change_req_c2s msg = param.getMsg();
        //HumanManager.inst().role_change_req_c2s(humanObj);
    }

    /**
     * 处理角色引导
     * @param param
     */
    @MsgReceiver(MsgRole.role_guide_c2s.class)
    public void role_guide_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_guide_c2s msg = param.getMsg();
        HumanManager.inst()._msg_role_guide_c2s(humanObj, msg.getId());
    }

    /**
     * 处理角色引导保存
     * @param param
     */
    @MsgReceiver(MsgRole.role_guide_save_c2s.class)
    public void role_guide_save_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_guide_save_c2s msg = param.getMsg();
        //HumanManager.inst().role_guide_save_c2s(humanObj);
    }
    /**
     * 处理角色功能预告奖励
     * @param param
     */
    @MsgReceiver(MsgRole.role_preview_reward_c2s.class)
    public void role_preview_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_preview_reward_c2s msg = param.getMsg();
        HumanManager.inst().handleRolePreviewRewardC2S(humanObj, msg.getConfigId());
    }

    /**
     * 处理客户端数据
     * @param param
     */
    @MsgReceiver(MsgRole.client_data_c2s.class)
    public void client_data_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.client_data_c2s msg = param.getMsg();
        HumanManager.inst()._msg_client_data_c2s(humanObj, msg);
    }

    /**
     * 处理角色客户端日志
     * @param param
     */
    @MsgReceiver(MsgRole.role_client_log_c2s.class)
    public void role_client_log_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_client_log_c2s msg = param.getMsg();
        //HumanManager.inst().handleRoleClientLogC2S(humanObj, msg);
    }

    /**
     * 处理角色红点
     * @param param
     */
    @MsgReceiver(MsgRole.role_red_point_c2s.class)
    public void role_red_point_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_red_point_c2s msg = param.getMsg();
        //HumanManager.inst().handleRoleRedPointC2S(humanObj, msg);
    }

    /**
     * 处理角色物品刷新列表
     * @param param
     */
    @MsgReceiver(MsgRole.role_goods_refresh_list_c2s.class)
    public void role_goods_refresh_list_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_goods_refresh_list_c2s msg = param.getMsg();
        //HumanManager.inst().handleRoleGoodsRefreshListC2S(humanObj, msg);
    }

    /**
     * 处理角色解锁皮肤
     * @param param
     */
    @MsgReceiver(MsgRole.role_unlock_skin_c2s.class)
    public void role_unlock_skin_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_unlock_skin_c2s msg = param.getMsg();
        HumanManager.inst().handleRoleUnlockSkinC2S(humanObj, msg.getSkinId());
    }

    /**
     * 处理角色皮肤升级等级
     * @param param
     */
    @MsgReceiver(MsgRole.role_skin_upgrade_lv_c2s.class)
    public void role_skin_upgrade_lv_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_skin_upgrade_lv_c2s msg = param.getMsg();
        HumanManager.inst().handleRoleSkinUpgradeLvC2S(humanObj, msg.getSkinId());
    }

    /**
     * 处理角色申诉
     * @param param
     */
    @MsgReceiver(MsgRole.role_complaint_c2s.class)
    public void role_complaint_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_complaint_c2s msg = param.getMsg();
        //HumanManager.inst().handleRoleComplaintC2S(humanObj, msg);
    }



    /**
     * 处理角色申诉
     * @param param
     */
    @MsgReceiver(MsgRole.role_appeal_c2s.class)
    public void role_appeal_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_appeal_c2s msg = param.getMsg();
        //HumanManager.inst().handleRoleAppealC2S(humanObj, msg);
    }

    /**
     * 处理角色投诉检查
     * @param param
     */
    @MsgReceiver(MsgRole.role_complaint_check_c2s.class)
    public void role_complaint_check_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_complaint_check_c2s msg = param.getMsg();
        //HumanManager.inst().handleRoleComplaintCheckC2S(humanObj, msg);
    }

    /**
     * 处理角色方案信息
     * @param param
     */
    @MsgReceiver(MsgRole.role_plan_info_c2s.class)
    public void role_plan_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        HumanManager.inst().handleRolePlanInfoC2S(humanObj);
    }

    /**
     * 处理角色选择方案
     * @param param
     */
    @MsgReceiver(MsgRole.role_choose_plan_c2s.class)
    public void role_choose_plan_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_choose_plan_c2s msg = param.getMsg();
        HumanManager.inst().handleRoleChoosePlanC2S(humanObj, msg.getPlanId());
    }

    /**
     * 处理角色修改方案名称
     * @param param
     */
    @MsgReceiver(MsgRole.role_change_plan_name_c2s.class)
    public void role_change_plan_name_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_change_plan_name_c2s msg = param.getMsg();
        HumanManager.inst().handleRoleChangePlanNameC2S(humanObj, msg.getPlanId(), msg.getName());
    }

    /**
     * 处理角色更新方案
     * @param param
     */
    @MsgReceiver(MsgRole.role_update_plan_c2s.class)
    public void role_update_plan_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_update_plan_c2s msg = param.getMsg();
        HumanManager.inst().handleRoleUpdatePlanC2S(humanObj, msg.getPlanId(), msg.getUpdateDetailList());
    }

    /**
     * 处理角色设置默认方案
     * @param param
     */
    @MsgReceiver(MsgRole.role_default_plan_info_c2s.class)
    public void role_default_plan_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        HumanManager.inst().handleRoleDefaultPlanInfoC2S(humanObj);
    }

    /**
     * 处理角色更改默认计划
     * @param change_list 更改列表
     */
    @MsgReceiver(MsgRole.role_change_default_plan_c2s.class)
    public void role_change_default_plan_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_change_default_plan_c2s msg = param.getMsg();
        HumanManager.inst().handleRoleChangeDefaultPlanC2S(humanObj, msg.getChangeListList());
    }

    /**
     *  一键设置双人本方案
     */
    @MsgReceiver(MsgRole.role_quick_set_double_chapter_plan_c2s.class)
    public void role_quick_set_double_chapter_plan_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        if (!humanObj.isMsgIdCD(org.gof.demo.worldsrv.msg.MsgIds.home_farm_building_lev_up_c2s, 2)) {
            return;
        }
        HumanManager.inst().handleRoleQuickSetDoubleChapterPlanC2S(humanObj);
    }

    /**
     * 处理客户端日志
     * @param param
     */
    @MsgReceiver(MsgRole.client_log_c2s.class)
    public void client_log_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.client_log_c2s msg = param.getMsg();
        //HumanManager.inst().handleClientLogC2S(humanObj, msg);
    }

    /**
     * 处理注销
     * @param param
     */
    @MsgReceiver(MsgRole.unregister_c2s.class)
    public void unregister_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.unregister_c2s msg = param.getMsg();
        //HumanManager.inst().handleUnregisterC2S(humanObj, msg);
    }

    /**
     * 头像列表
     * @param param
     */
    @MsgReceiver(MsgRole.role_head_list_c2s.class)
    public void role_head_list_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        AppearanceManager.inst().on_role_head_list_c2s(humanObj);
    }

    /**
     * 处理头像红点
     * @param param
     */
    @MsgReceiver(MsgRole.role_head_red_point_c2s.class)
    public void role_head_red_point_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_head_red_point_c2s msg = param.getMsg();
        AppearanceManager.inst().on_role_head_red_point_c2s(humanObj, msg.getCfgId());
    }

    /**
     * 处理角色更换头像
     * @param param
     */
    @MsgReceiver(MsgRole.role_change_head_c2s.class)
    public void role_change_head_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_change_head_c2s msg = param.getMsg();
        AppearanceManager.inst().handleRoleChangeHeadC2S(humanObj, msg.getHeadId());
    }

    /**
     * 处理角色SP比较
     * @param param
     */
    @MsgReceiver(MsgRole.role_sp_cmp_c2s.class)
    public void role_sp_cmp_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRole.role_sp_cmp_c2s msg = param.getMsg();
        HumanManager.inst().handleRoleSPCompareC2S(humanObj, msg.getTargetId(), msg.getSource());
    }


    /**
     * 头衔数据
     * @Param
     */
    @MsgReceiver(MsgAdventureTitle.adventure_title_info_c2s.class)
    public void adventure_title_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAdventureTitle.adventure_title_info_c2s msg = param.getMsg();
        HumanManager.inst().adventure_title_info_c2s(humanObj);
    }

    /**
     * 头衔升级
     * @Param
     */
    @MsgReceiver(MsgAdventureTitle.adventure_title_level_up_c2s.class)
    public void adventure_title_level_up_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAdventureTitle.adventure_title_level_up_c2s msg = param.getMsg();
        HumanManager.inst().adventure_title_level_up_c2s(humanObj);
    }


    @MsgReceiver(MsgLogin.heart_beat_c2s.class)
    public void _msg_heart_beat_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgLogin.heart_beat_c2s msg = param.getMsg();
        HumanManager.inst()._msg_heart_beat_c2s(humanObj, msg.getSvrTime());
    }




    @MsgReceiver(MsgCollection.collection_enhance_c2s.class)
    public void _msg_collection_enhance_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCollection.collection_enhance_c2s msg = param.getMsg();
        HumanManager.inst()._msg_collection_enhance_c2s(humanObj, msg.getCfgId());
    }

    /**
     * 转盘信息
     * @Param
     */
    @MsgReceiver(MsgAd.ad_wheel_info_c2s.class)
    public void _msg_ad_wheel_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        HumanManager.inst()._msg_ad_wheel_info_c2s(humanObj);
    }

    /**
     * 转盘抽奖
     * @Param
     */
    @MsgReceiver(MsgAd.ad_wheel_spin_c2s.class)
    public void _msg_ad_wheel_spin_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        HumanManager.inst()._msg_ad_wheel_spin_c2s(humanObj);
    }

    @MsgReceiver(MsgSolo.solo_video_c2s.class)
    public void _msg_solo_video_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgSolo.solo_video_c2s msg = param.getMsg();
        HumanManager.inst()._msg_solo_video_c2s(humanObj, msg.getVid(), msg.getSource());
    }

    @MsgReceiver(MsgSystem.server_merge_list_c2s.class)
    public void _msg_server_merge_list_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgSystem.server_merge_list_c2s msg = param.getMsg();
        HumanManager.inst()._msg_server_merge_list_c2s(humanObj);
    }

    @MsgReceiver(MsgSystem.server_cross_join_c2s.class)
    public void _msg_server_cross_join_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgSystem.server_cross_join_c2s msg = param.getMsg();
        HumanManager.inst()._msg_server_cross_join_c2s(humanObj, msg.getFuncId());
    }
}

