package org.gof.demo.worldsrv.activity.data.controldata;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfActivityControl;
import org.gof.demo.worldsrv.config.ConfActivityStamina_0;
import org.gof.demo.worldsrv.config.ConfActivityTerm;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct2;
import org.gof.demo.worldsrv.support.Log;

import java.util.HashMap;
import java.util.Map;

/**
 * 合成大西瓜活动数据
 */
public class ControlFruitMergeData implements IControlData {
    
    // 体力管理器
    public ControlStaminaManager staminaMgr;
    
    // 当前分数
    public int currScore = 0;
    
    // 最大局内分数
    public int maxScore = 0;
    
    // 累计分数
    public long totalScore = 0;
    
    // 总消耗体力
    public int totalCostStamina = 0;
    
    // 当前游戏状态
    public Define.p_fruit_merge_state state;
    
    // 道具使用次数记录
    public Map<Integer, Integer> itemUseCount = new HashMap<>();

    public Map<Integer, Integer> mergeFruitCount = new HashMap<>();
    
    public ControlFruitMergeData(String jsonStr) {
        JSONObject json = Utils.toJSONObject(jsonStr);
        if (json.containsKey("fruitMerge")) {
            MsgAct2.act_fruit_merge_info_s2c fruitMergeProto = Utils.decompressProtoLZ4(
                json.getString("fruitMerge"), MsgAct2.act_fruit_merge_info_s2c.parser());
            
            currScore = fruitMergeProto.getCurrScore();
            maxScore = fruitMergeProto.getMaxScore();
            totalScore = fruitMergeProto.getTotalScore();
            totalCostStamina = fruitMergeProto.getTotalCostStamina();

            if (fruitMergeProto.hasState()) {
                state = fruitMergeProto.getState();
            } else {
                state = Define.p_fruit_merge_state.newBuilder().build();
            }

        }
        if(json.containsKey("mergeFruitCount")) {
            mergeFruitCount = Utils.jsonToMapIntInt(json.getString("mergeFruitCount"));
        }
        if(json.containsKey("staminaMgr")) {
            staminaMgr = new ControlStaminaManager(json.getJSONObject("staminaMgr"));
        }

    }
    
    @Override
    public void create(HumanObject humanObj, ActivityVo vo) {
        // 初始化数据
        currScore = 0;
        maxScore = 0;
        totalScore = 0;
        totalCostStamina = 0;
        state = Define.p_fruit_merge_state.newBuilder().build();
        itemUseCount = new HashMap<>();
        
        // 初始化体力管理器
        ConfActivityControl confControl = ConfActivityControl.get(vo.activitySn);
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(confControl.type, vo.round);
        initStaminaManager(confControl.type, confTerm.group_id);
    }
    
    public void initStaminaManager(int actType, int groupId) {
        // 从ActivityStamina_0配置表获取体力相关配置
        ConfActivityStamina_0 staminaConf = ConfActivityStamina_0.get(actType, groupId);
        if (staminaConf != null) {
            staminaMgr = new ControlStaminaManager(staminaConf);
            staminaMgr.stamina = staminaConf.initial_stamina; // 初始体力
        } else {
            // 默认配置
            Log.activity.error("找不到活动体力配置，使用默认值, actType={}", actType);
            staminaMgr = new ControlStaminaManager(3600, 1, 10); // 1小时恢复1点，最大10点
            staminaMgr.stamina = 10;
        }
    }
    
    public MsgAct2.act_fruit_merge_info_s2c.Builder toProto() {
        MsgAct2.act_fruit_merge_info_s2c.Builder builder = MsgAct2.act_fruit_merge_info_s2c.newBuilder();
        builder.setCurrScore(currScore);
        builder.setMaxScore(maxScore);
        builder.setTotalScore(totalScore);
        builder.setTotalCostStamina(totalCostStamina);
        
        if (state != null) {
            builder.setState(state);
        }
        
        return builder;
    }
    
    @Override
    public String toJSON() {
        JSONObject json = new JSONObject();
        
        // 构建protobuf消息并压缩存储
        MsgAct2.act_fruit_merge_info_s2c.Builder builder = toProto();
        String compressedData = Utils.compressProtoLZ4(builder.build());
        json.put("fruitMerge", compressedData);
        json.put("mergeFruitCount", Utils.mapIntIntToJSON(mergeFruitCount));
        json.put("staminaMgr", staminaMgr.toJSON());
        
        return json.toJSONString();
    }
    
    /**
     * 更新游戏状态
     */
    public void updateGameState(Define.p_fruit_merge_state newState) {
        this.state = newState;
        this.currScore = newState.getCurrentScore();
    }
    
    /**
     * 结束游戏，更新最大分数和累计分数
     */
    public void endGame(int finalScore) {
        if (finalScore > maxScore) {
            maxScore = finalScore;
        }
        totalScore += finalScore;
        currScore = 0;
        totalCostStamina = 0;
        // 重置游戏状态
        state = Define.p_fruit_merge_state.newBuilder().build();
        mergeFruitCount.clear();
    }
    
    /**
     * 消耗体力
     */
    public boolean consumeStamina(int cost) {
        if (staminaMgr.consumeStamina(cost)) {
            totalCostStamina += cost;
            return true;
        }
        return false;
    }
    
    /**
     * 使用道具
     */
    public void useItem(int itemSn) {
        itemUseCount.put(itemSn, itemUseCount.getOrDefault(itemSn, 0) + 1);
    }
    
    /**
     * 获取道具使用次数
     */
    public int getItemUseCount(int itemSn) {
        return itemUseCount.getOrDefault(itemSn, 0);
    }
}
