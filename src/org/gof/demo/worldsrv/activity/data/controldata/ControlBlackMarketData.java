package org.gof.demo.worldsrv.activity.data.controldata;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.support.RandomUtils;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfBlackMarket;
import org.gof.demo.worldsrv.produce.RandomUtil;
import org.gof.demo.worldsrv.support.Util;

import java.util.HashMap;
import java.util.Map;

public class ControlBlackMarketData implements IControlData {
    // Map<黑市表sn, 已经购买数量>
    public Map<Integer, Integer> mallNumMap = new HashMap<>();
    // Map<黑市表sn, 黑市格子下标>
    public Map<Integer, Integer> mallIndexMap = new HashMap<>();

    public ControlBlackMarketData() {
    }

    public ControlBlackMarketData(String json) {
        if (json == null || Util.isNullOrEmptyJSONString(json)) {
            return;
        }
        JSONObject jo = JSONObject.parseObject(json);
        mallNumMap = Utils.jsonToMapIntInt(jo.getString("mnm"));
        mallIndexMap = Utils.jsonToMapIntInt(jo.getString("mim"));
    }

    public void refreshHumanMall() {
        this.mallNumMap.clear();
        this.mallIndexMap.clear();
        for (ConfBlackMarket conf : ConfBlackMarket.findAll()) {
            if (conf.mall_tab == 3) {
                mallNumMap.put(conf.sn, 0);
                mallIndexMap.put(conf.sn, Util.getRandRange(conf.goods));
            }
        }
    }

    @Override
    public void create(HumanObject humanObj, ActivityVo vo) {
        refreshHumanMall();
    }

    @Override
    public String toJSON() {
        JSONObject jo = new JSONObject();
        jo.put("mnm", Utils.mapIntIntToJSON(mallNumMap));
        jo.put("mim", Utils.mapIntIntToJSON(mallIndexMap));
        return jo.toJSONString();
    }
}
