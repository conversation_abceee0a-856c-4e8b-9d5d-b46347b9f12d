package org.gof.demo.worldsrv.activity.data.controldata;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct2;
import org.gof.demo.worldsrv.support.Log;

import java.util.*;

/**
 * 无职联动-好感度活动数据
 */
public class ControlWuzhiLoveData implements IControlData {
    
    // 活动阶段状态 (1:选择角色, 2:前置任务, 3:正式活动)
    public int eventStatus = 1;
    
    // 玩家选择的角色ID
    public int selectedCharId = 0;
    
    // 好感度信息Map，key为角色ID
    public Map<Integer, FavorInfo> favorInfos = new HashMap<>();
    
    /**
     * 好感度信息内部类
     */
    public static class FavorInfo {
        public int charId;          // 角色ID
        public int level = 1;       // 当前好感度等级
        public int exp = 0;         // 当前等级下的好感度经验值
        public Set<Integer> rewardedLv = new HashSet<>();  // 已领取奖励的等级列表
        
        public FavorInfo() {}
        
        public FavorInfo(int charId) {
            this.charId = charId;
        }
        
        public JSONObject toJSON() {
            JSONObject json = new JSONObject();
            json.put("charId", charId);
            json.put("level", level);
            json.put("exp", exp);
            json.put("rewardedLv", new ArrayList<>(rewardedLv));
            return json;
        }
        
        public static FavorInfo fromJSON(JSONObject json) {
            FavorInfo info = new FavorInfo();
            info.charId = json.getIntValue("charId");
            info.level = json.getIntValue("level");
            info.exp = json.getIntValue("exp");
            List<Integer> rewardedList = json.getJSONArray("rewardedLv").toJavaList(Integer.class);
            info.rewardedLv = new HashSet<>(rewardedList);
            return info;
        }
    }
    

    
    public ControlWuzhiLoveData() {
    }
    
    public ControlWuzhiLoveData(String jsonStr) {
        if (Utils.isEmptyJSONString(jsonStr)) {
            return;
        }
        
        JSONObject json = Utils.toJSONObject(jsonStr);
        
        // 基础数据
        eventStatus = json.getIntValue("eventStatus");
        selectedCharId = json.getIntValue("selectedCharId");

        // 好感度信息
        if (json.containsKey("favorInfos")) {
            JSONObject favorJson = json.getJSONObject("favorInfos");
            for (String key : favorJson.keySet()) {
                int charId = Integer.parseInt(key);
                FavorInfo info = FavorInfo.fromJSON(favorJson.getJSONObject(key));
                favorInfos.put(charId, info);
            }
        }
    }
    
    @Override
    public void create(HumanObject humanObj, ActivityVo vo) {
        // 初始化活动数据
        eventStatus = 1;
        selectedCharId = 0;
        favorInfos.clear();
    }
    
    @Override
    public String toJSON() {
        JSONObject json = new JSONObject();
        
        // 基础数据
        json.put("eventStatus", eventStatus);
        json.put("selectedCharId", selectedCharId);

        // 好感度信息
        if (!favorInfos.isEmpty()) {
            JSONObject favorJson = new JSONObject();
            for (Map.Entry<Integer, FavorInfo> entry : favorInfos.entrySet()) {
                favorJson.put(String.valueOf(entry.getKey()), entry.getValue().toJSON());
            }
            json.put("favorInfos", favorJson);
        }
        
        return json.toJSONString();
    }
    
    /**
     * 获取指定角色的好感度信息
     */
    public FavorInfo getFavorInfo(int charId) {
        return favorInfos.get(charId);
    }
    
    /**
     * 设置指定角色的好感度信息
     */
    public void setFavorInfo(int charId, FavorInfo info) {
        favorInfos.put(charId, info);
    }
    

}
