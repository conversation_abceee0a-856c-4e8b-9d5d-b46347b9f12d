package org.gof.demo.worldsrv.activity.data.controldata;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.Port;
import org.gof.core.support.Time;
import org.gof.demo.worldsrv.config.ConfActivityStamina_0;

public class ControlStaminaManager {
    public int stamina = 0;
    public long nextRecoverTime = 0;
    public long recoverCD; // 恢复间隔（毫秒）
    public int recoverAmount; // 每次恢复的体力值
    public int maxStamina; // 最大体力值

    public ControlStaminaManager(int confCD, int recoverAmount, int maxStamina) {
        this.recoverCD = confCD * Time.SEC;
        this.recoverAmount = recoverAmount;
        this.maxStamina = maxStamina;
    }

    public ControlStaminaManager(ConfActivityStamina_0 conf) {
        this((int)(conf.stamina_recover[0] * Time.SEC), conf.stamina_recover[1], conf.max_stamina);
    }

    public ControlStaminaManager(JSONObject jo) {
        fromJSON(jo);
    }

    public int getStamina() {
        tryRecoverStamina();
        return stamina;
    }

    public void tryRecoverStamina() {
        // 已满体力不需要恢复
        if (stamina >= maxStamina) {
            nextRecoverTime = 0;
            return;
        }

        // 检查是否到恢复时间
        long now = Port.getTime();
        long recoverInterval = recoverCD;

        if (nextRecoverTime == 0) {
            // 第一次设置恢复时间
            nextRecoverTime = now + recoverInterval;
            return;
        }

        if (now < nextRecoverTime) {
            return;
        }

        // 计算可恢复次数
        long timePassed = now - nextRecoverTime + recoverInterval;
        int recoverTimes = (int) (timePassed / recoverInterval);

        // 计算实际可恢复的体力值
        int canRecover = Math.min(recoverTimes * recoverAmount, maxStamina - stamina);
        if (canRecover > 0) {
            // 恢复体力
            stamina = Math.min(maxStamina, stamina + canRecover);
        }

        // 更新下一次恢复时间
        if (stamina < maxStamina) {
            nextRecoverTime = nextRecoverTime + (recoverTimes * recoverInterval);
        } else {
            nextRecoverTime = 0;
        }
    }

    // 消耗体力
    public boolean consumeStamina(int value) {
        if (stamina < value) {
            return false;
        }
        stamina -= value;

        if (stamina < maxStamina && nextRecoverTime == 0) {
            nextRecoverTime = Port.getTime() + recoverCD;
        }
        return true;
    }

    public void addStamina(int value) {
        stamina += value;
        if (stamina > maxStamina && nextRecoverTime != 0) {
            nextRecoverTime = 0;
        }
    }

    public JSONObject toJSON() {
        JSONObject jo = new JSONObject();
        jo.put("stmi", stamina);
        jo.put("rcCD", recoverCD);
        jo.put("rcA", recoverAmount);
        jo.put("maxS", maxStamina);
        jo.put("nextRT", nextRecoverTime);
        return jo;
    }
    public void fromJSON(JSONObject jo) {
        stamina = jo.getIntValue("stmi");
        recoverCD = jo.getLongValue("rcCD");
        recoverAmount = jo.getIntValue("rcA");
        maxStamina = jo.getIntValue("maxS");
        nextRecoverTime = jo.getLongValue("nextRT");
    }
}

