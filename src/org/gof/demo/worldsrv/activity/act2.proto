syntax = "proto3";
package Network.Protos.act2;

import "options.proto";
import "type.proto";

message act_guild_pay_info_c2s {
	option (msgid) = 6401;
	uint32 act_type = 1;
}

message act_guild_pay_info_s2c {
	option (msgid) = 6401;
	uint32 act_type = 1;
	uint32 count = 2;
	repeated uint32 got_reward = 3 [packed=false];
}

message act_guild_pay_get_reward_c2s {
	option (msgid) = 6402;
	uint32 act_type = 1;
	uint32 reward_id = 2;
}

message act_cohesion_info_c2s {
	option (msgid) = 6403;
	uint32 act_type = 1;
}

message act_cohesion_info_s2c {
	option (msgid) = 6403;
	uint32 act_type = 1;
	uint32 process = 2;
	repeated uint32 got_reward = 3 [packed=false];
}

message act_cohesion_get_reward_c2s {
	option (msgid) = 6404;
	uint32 act_type = 1;
	uint32 reward_id = 2;
}

message act_jump_result_c2s {
	option (msgid) = 6405;
	uint32 act_type = 1;
	uint32 chapter_id = 2;
	uint32 result = 3;
	uint32 jump_cnt = 4;
	uint32 rush_cnt = 5;
	uint32 distance = 6;
}

message act_jump_result_s2c {
	option (msgid) = 6405;
	uint32 act_type = 1;
	uint32 chapter_id = 2;
	uint32 result = 3;
	uint32 distance = 4;
	uint32 history_distance = 5;
	uint32 is_new_high = 6;
}

message act_candy_result_c2s {
	option (msgid) = 6406;
	repeated p_key_value candy_list = 1;
}

message act_candy_result_s2c {
	option (msgid) = 6406;
	repeated p_key_value candy_list = 1;
}

message act_candy_get_card_c2s {
	option (msgid) = 6407;
}

message act_candy_get_card_s2c {
	option (msgid) = 6407;
}

message act_divination_info_c2s {
	option (msgid) = 6408;
}

message act_divination_info_s2c {
	option (msgid) = 6408;
	uint32 draw_result = 1;
	uint32 cnt = 2;
	repeated p_pos card_list = 3;
}

message act_divination_draw_c2s {
	option (msgid) = 6409;
}

message act_divination_draw_s2c {
	option (msgid) = 6409;
	uint32 result = 1;
	uint32 cnt = 2;
}

message act_divination_open_c2s {
	option (msgid) = 6410;
	p_pos pos = 1;
}

message act_divination_open_s2c {
	option (msgid) = 6410;
	p_pos pos = 1;
	uint32 cnt = 2;
}

message act_move_enter_c2s {
	option (msgid) = 6411;
	uint32 act_type = 1;
	p_pos pos = 2;
}

message act_move_enter_s2c {
	option (msgid) = 6411;
	repeated p_act_move_member member_list = 1;
}

message act_move_exit_c2s {
	option (msgid) = 6412;
	uint32 act_type = 1;
}

message act_move_exit_s2c {
	option (msgid) = 6412;
}

message act_move_move_c2s {
	option (msgid) = 6413;
	uint32 act_type = 1;
	repeated p_pos pos_list = 2;
}

message act_move_move_s2c {
	option (msgid) = 6413;
}

message act_move_broadcast_s2c {
	option (msgid) = 6414;
	uint32 act_type = 1;
	uint32 type = 2;
	p_act_move_member action_member = 3;
	repeated p_pos pos_list = 4;
	uint32 emoji = 5;
}

message act_move_emoji_c2s {
	option (msgid) = 6415;
	uint32 act_type = 1;
	uint32 cfg_id = 2;
}

message act_move_emoji_s2c {
	option (msgid) = 6415;
	uint32 act_type = 1;
	uint32 cfg_id = 2;
}

message act_share_game_info_c2s {
	option (msgid) = 6416;
	uint32 act_type = 1;
}

message act_share_game_info_s2c {
	option (msgid) = 6416;
	uint32 act_type = 1;
	uint32 score = 2;
	uint32 get_num = 3;
}

message act_share_game_result_c2s {
	option (msgid) = 6417;
	uint32 act_type = 1;
	uint32 score = 2;
}

message act_share_game_result_s2c {
	option (msgid) = 6417;
	uint32 act_type = 1;
	uint32 score = 2;
	repeated p_reward reward_list = 3;
}

message act_cross_boss_info_c2s {
	option (msgid) = 6418;
	uint32 act_type = 1;
}

message act_cross_boss_info_s2c {
	option (msgid) = 6418;
	uint32 act_type = 1;
	uint32 cnt = 2;
	repeated uint32 reward_list = 3 [packed=false];
}

message act_cross_boss_throw_c2s {
	option (msgid) = 6419;
	uint32 act_type = 1;
	uint32 cnt = 2;
}

message act_cross_boss_throw_s2c {
	option (msgid) = 6419;
	uint32 act_type = 1;
	uint32 cnt = 2;
}

message act_cross_boss_claim_c2s {
	option (msgid) = 6420;
	uint32 act_type = 1;
	uint32 cfg_id = 2;
}

message act_cross_boss_claim_s2c {
	option (msgid) = 6420;
	uint32 act_type = 1;
	uint32 cfg_id = 2;
}

message act_cross_boss_buy_c2s {
	option (msgid) = 6421;
	uint32 act_type = 1;
	uint32 cnt = 2;
}

message act_cross_boss_buy_s2c {
	option (msgid) = 6421;
	uint32 act_type = 1;
	uint32 cnt = 2;
}

message act_cross_boss_claim_box_info_c2s {
	option (msgid) = 6422;
	uint32 act_type = 1;
}

message act_cross_boss_claim_box_info_s2c {
	option (msgid) = 6422;
	uint32 act_type = 1;
	repeated p_reward reward_list = 2;
	repeated p_act_big_reward big_list = 3;
}

message act_cross_boss_claim_box_c2s {
	option (msgid) = 6423;
	uint32 act_type = 1;
}

message act_cross_boss_claim_box_s2c {
	option (msgid) = 6423;
	uint32 act_type = 1;
}

message act_move_reward_info_c2s {
	option (msgid) = 6424;
	uint32 act_type = 1;
}

message act_move_reward_info_s2c {
	option (msgid) = 6424;
	uint32 act_type = 1;
	repeated p_key_value reward_cnt = 2;
}

message act_move_reward_claim_c2s {
	option (msgid) = 6425;
	uint32 act_type = 1;
	uint32 cfg_id = 2;
	uint32 random_pet_id = 3;
}

message act_move_reward_claim_s2c {
	option (msgid) = 6425;
	uint32 act_type = 1;
	uint32 cfg_id = 2;
}

message act_christmas_tree_decorate_info_c2s {
	option (msgid) = 6426;
	uint32 act_type = 1;
}

message act_christmas_tree_decorate_info_s2c {
	option (msgid) = 6426;
	uint32 act_type = 1;
	repeated p_key_value decorate_list = 2;
	uint32 is_claim = 3;
}

message act_christmas_tree_decorate_c2s {
	option (msgid) = 6427;
	uint32 act_type = 1;
	repeated p_key_value decorate = 2;
}

message act_custom_mall_info_c2s {
	option (msgid) = 6428;
	uint32 act_type = 1;
}

message act_custom_mall_info_s2c {
	option (msgid) = 6428;
	uint32 act_type = 1;
	repeated p_act_custom_mall bundle_list = 2;
	repeated p_act_custom_mall_receive receive_list = 3;
}

message act_custom_mall_set_c2s {
	option (msgid) = 6429;
	uint32 id = 1;
	repeated p_key_value choose_list = 2;
}

message act_custom_mall_set_s2c {
	option (msgid) = 6429;
	p_act_custom_mall bundle = 1;
}

message act_custom_mall_buy_c2s {
	option (msgid) = 6430;
	uint32 id = 1;
}

message act_custom_mall_buy_s2c {
	option (msgid) = 6430;
	p_act_custom_mall bundle = 1;
}

message act_custom_mall_search_c2s {
	option (msgid) = 6431;
	uint32 id = 1;
	string content = 2;
}

message act_custom_mall_search_s2c {
	option (msgid) = 6431;
	repeated p_act_custom_mall_role role_list = 1;
}

message act_custom_mall_send_c2s {
	option (msgid) = 6432;
	uint32 id = 1;
	uint64 target_id = 2;
	string message = 3;
	repeated p_key_value choose_list = 4;
}

message act_custom_mall_send_s2c {
	option (msgid) = 6432;
	p_act_custom_mall bundle = 1;
}

message act_custom_mall_claim_c2s {
	option (msgid) = 6433;
	uint32 act_type = 1;
	uint32 id = 2;
}

message act_custom_mall_claim_s2c {
	option (msgid) = 6433;
	uint32 act_type = 1;
	uint32 id = 2;
}

message act_chat_reward_c2s {
	option (msgid) = 6434;
	uint32 id = 1;
}

message act_chat_reward_s2c {
	option (msgid) = 6434;
	uint32 id = 1;
}

message act_bonfire_info_c2s {
	option (msgid) = 6441;
	uint32 act_type = 1;
}

message act_bonfire_info_s2c {
	option (msgid) = 6441;
	uint32 act_type = 1;
	uint32 cnt = 2;
	repeated uint32 claim_list = 3 [packed=false];
}

message act_bonfire_use_goods_c2s {
	option (msgid) = 6442;
	uint32 act_type = 1;
	uint32 num = 2;
}

message act_bonfire_use_goods_s2c {
	option (msgid) = 6442;
	uint32 act_type = 1;
	uint32 num = 2;
}

message act_bonfire_claim_reward_c2s {
	option (msgid) = 6443;
	uint32 act_type = 1;
	uint32 progress = 2;
}

message act_bonfire_claim_reward_s2c {
	option (msgid) = 6443;
	uint32 act_type = 1;
	repeated uint32 claim_list = 2 [packed=false];
}

message act_treasure_hunt_info_c2s {
	option (msgid) = 6444;
	uint32 act_type = 1;
}

message act_treasure_hunt_info_s2c {
	option (msgid) = 6444;
	uint32 act_type = 1;
	uint32 acc_count = 2;
	uint32 refresh_count = 3;
	uint32 next_refresh_time = 4;
	repeated uint32 got_rewards = 5 [packed=false];
	repeated p_treasure_hunt_gift gift_list = 6;
	repeated p_key_value must_info = 7;
	repeated p_key_value replace_count = 8;
	repeated p_key_value replace_info = 9;
}

message act_treasure_hunt_draw_c2s {
	option (msgid) = 6445;
	uint32 act_type = 1;
	uint32 seq_id = 2;
}

message act_treasure_hunt_draw_s2c {
	option (msgid) = 6445;
	uint32 act_type = 1;
	uint32 seq_id = 2;
	repeated p_treasure_hunt_gift update_gift_list = 3;
	repeated p_reward extra_reward = 4;
}

message act_treasure_hunt_refresh_c2s {
	option (msgid) = 6446;
	uint32 act_type = 1;
}

message act_treasure_hunt_replace_c2s {
	option (msgid) = 6447;
	uint32 act_type = 1;
	uint32 config_id = 2;
	uint32 choose_id = 3;
}

message act_treasure_hunt_replace_s2c {
	option (msgid) = 6447;
	uint32 act_type = 1;
	uint32 config_id = 2;
	uint32 choose_id = 3;
}

message act_treasure_hunt_reward_c2s {
	option (msgid) = 6448;
	uint32 act_type = 1;
	uint32 config_id = 2;
}

message act_treasure_hunt_reward_s2c {
	option (msgid) = 6448;
	uint32 act_type = 1;
	uint32 config_id = 2;
}

message act_treasure_hunt_report_c2s {
	option (msgid) = 6449;
	uint32 act_type = 1;
}

message act_treasure_hunt_report_s2c {
	option (msgid) = 6449;
	uint32 act_type = 1;
	uint32 type = 2;
	repeated p_treasure_hunt_report report_list = 3;
}

message act_star_rain_info_c2s {
	option (msgid) = 6450;
	uint32 act_type = 1;
}

message act_star_rain_info_s2c {
	option (msgid) = 6450;
	uint32 act_type = 1;
	uint32 sum_star = 2;
	uint32 star_process = 3;
	uint32 count = 4;
	repeated p_key_value must_info = 5;
	repeated p_key_value replace_info = 6;
	repeated p_act_draw_report report_list = 7;
}

message act_star_rain_draw_c2s {
	option (msgid) = 6451;
	uint32 act_type = 1;
	uint32 count = 2;
}

message act_star_rain_draw_s2c {
	option (msgid) = 6451;
	uint32 act_type = 1;
	repeated p_reward reward_list = 2;
	repeated uint32 drop_id_list = 3 [packed=false];
}

message act_star_rain_update_s2c {
	option (msgid) = 6452;
	uint32 act_type = 1;
	uint32 sum_star = 2;
	uint32 star_process = 3;
}

message act_christmas_farm_info_c2s {
	option (msgid) = 6453;
	uint32 act_type = 1;
}

message act_christmas_farm_info_s2c {
	option (msgid) = 6453;
	uint32 is_push = 1;
	uint32 act_type = 2;
	repeated p_key_value pos_list = 3;
	uint32 today_cnt = 4;
}

message act_christmas_farm_claim_c2s {
	option (msgid) = 6454;
	uint32 act_type = 1;
	uint32 pos = 2;
}

message act_christmas_farm_claim_s2c {
	option (msgid) = 6454;
	uint32 act_type = 1;
	uint32 pos = 2;
}

message act_golden_tower_info_c2s {
	option (msgid) = 6455;
	uint32 act_type = 1;
}

message act_golden_tower_info_s2c {
	option (msgid) = 6455;
	uint32 act_type = 1;
	uint32 round = 2;
	uint32 ceng = 3;
	repeated p_key_kv_list choose_list = 4;
	uint32 total = 5;
	repeated uint32 sum_rewards = 6 [packed=false];
	uint32 remain = 7;
}

message act_golden_tower_draw_c2s {
	option (msgid) = 6456;
	uint32 act_type = 1;
	uint32 draw_type = 2;
}

message act_golden_tower_draw_s2c {
	option (msgid) = 6456;
	uint32 act_type = 1;
	uint32 draw_num = 2;
	repeated p_key_value draw_list = 3;
	uint32 total = 4;
	uint32 is_up = 5;
	repeated p_reward reward_list = 6;
	uint32 remain = 7;
	uint32 round = 8;
	uint32 ceng = 9;
}

message act_golden_tower_choose_c2s {
	option (msgid) = 6457;
	uint32 act_type = 1;
	uint32 reward_id = 2;
	uint32 replace_id = 3;
}

message act_golden_tower_choose_s2c {
	option (msgid) = 6457;
	uint32 act_type = 1;
	uint32 reward_id = 2;
	uint32 replace_id = 3;
}

message act_golden_tower_count_reward_c2s {
	option (msgid) = 6458;
	uint32 act_type = 1;
	uint32 reward_id = 2;
}

message act_golden_tower_count_reward_s2c {
	option (msgid) = 6458;
	uint32 act_type = 1;
	uint32 reward_id = 2;
	repeated uint32 sum_rewards = 3 [packed=false];
}

message act_nine_number_info_c2s {
	option (msgid) = 6459;
}

message act_nine_number_info_s2c {
	option (msgid) = 6459;
	repeated p_key_value number_list = 1;
}

message act_nine_number_guess_c2s {
	option (msgid) = 6460;
	uint32 type = 1;
	uint32 key = 2;
}

message act_nine_number_guess_s2c {
	option (msgid) = 6460;
	uint32 type = 1;
	uint32 key = 2;
	repeated p_key_value number_list = 3;
}

message act_mini_game_buy_info_c2s {
	option (msgid) = 6461;
	uint32 act_type = 1;
}

message act_mini_game_buy_info_s2c {
	option (msgid) = 6461;
	uint32 act_type = 1;
	repeated p_key_value item_list = 2;
}

message act_mini_game_buy_item_c2s {
	option (msgid) = 6462;
	uint32 act_type = 1;
	uint32 item_id = 2;
}

message act_mini_game_buy_item_s2c {
	option (msgid) = 6462;
	uint32 act_type = 1;
	uint32 item_id = 2;
}

message act_nine_number_pre_c2s {
	option (msgid) = 6463;
}

message act_nine_number_pre_s2c {
	option (msgid) = 6463;
	uint32 is_start = 1;
}

message act_clear_game_info_c2s {
	option (msgid) = 6464;
	uint32 act_type = 1;
}

message act_clear_game_info_s2c {
	option (msgid) = 6464;
	uint32 act_type = 1;
	string save = 2;
	uint32 score = 3;
}

message act_clear_game_save_c2s {
	option (msgid) = 6465;
	uint32 act_type = 1;
	string save = 2;
}

message act_new_year_draw_slot_c2s {
	option (msgid) = 6466;
}

message act_new_year_draw_slot_s2c {
	option (msgid) = 6466;
	uint32 slot_id = 1;
	repeated p_reward reward_list = 2;
}

message act_luck_koi_reward_info_c2s {
	option (msgid) = 6467;
}

message act_luck_koi_reward_info_s2c {
	option (msgid) = 6467;
	repeated p_luck_koi info = 1;
}

message act_luck_slot_reward_info_c2s {
	option (msgid) = 6468;
}

message act_luck_slot_reward_info_s2c {
	option (msgid) = 6468;
	uint32 draw_times = 1;
	repeated uint32 draw_info = 2 [packed=false];
}

message act_yfs_draw_info_c2s {
	option (msgid) = 6469;
	uint32 act_type = 1;
}

message act_yfs_draw_info_s2c {
	option (msgid) = 6469;
	uint32 act_type = 1;
	uint32 count = 2;
	repeated uint32 count_reward_list = 3 [packed=false];
	repeated p_yfs_pool pool_list = 4;
}

message act_yfs_draw_draw_c2s {
	option (msgid) = 6470;
	uint32 act_type = 1;
	uint32 cfg_id = 2;
	uint32 type = 3;
}

message act_yfs_draw_draw_s2c {
	option (msgid) = 6470;
	uint32 act_type = 1;
	uint32 cfg_id = 2;
	uint32 type = 3;
	uint32 times = 4;
	repeated uint32 id_list = 5 [packed=false];
	repeated p_reward reward_list = 6;
}

message act_yfs_draw_count_reward_c2s {
	option (msgid) = 6471;
	uint32 act_type = 1;
	uint32 cfg_id = 2;
}

message act_yfs_draw_count_reward_s2c {
	option (msgid) = 6471;
	uint32 act_type = 1;
	uint32 cfg_id = 2;
}

message act_yfs_draw_choose_c2s {
	option (msgid) = 6472;
	uint32 act_type = 1;
	uint32 cfg_id = 2;
	uint32 choose_id = 3;
}

message act_yfs_draw_choose_s2c {
	option (msgid) = 6472;
	uint32 act_type = 1;
	uint32 cfg_id = 2;
	uint32 choose_id = 3;
}

message act_yfs_black_hole_c2s {
	option (msgid) = 6473;
	uint32 act_type = 1;
	uint32 cfg_id = 2;
}

message act_yfs_black_hole_s2c {
	option (msgid) = 6473;
	uint32 act_type = 1;
	uint32 cfg_id = 2;
	p_key_value goods_list = 3;
}

message act_yfs_reset_c2s {
	option (msgid) = 6474;
	uint32 act_type = 1;
	uint32 cfg_id = 2;
}

message act_yfs_reset_s2c {
	option (msgid) = 6474;
	uint32 act_type = 1;
	uint32 cfg_id = 2;
	p_yfs_pool pool = 3;
}

message act_draw_away_monster_info_c2s {
	option (msgid) = 6475;
	uint32 act_type = 1;
}

message act_draw_away_monster_info_s2c {
	option (msgid) = 6475;
	uint32 act_type = 1;
	uint32 now_step = 2;
}

message act_draw_away_monster_c2s {
	option (msgid) = 6476;
	uint32 act_type = 1;
	uint32 fast_move = 2;
}

message act_draw_away_monster_s2c {
	option (msgid) = 6476;
	uint32 act_type = 1;
	uint32 now_step = 2;
	repeated p_reward reward_list = 3;
}

message act_sugar_bawl_info_c2s {
	option (msgid) = 6477;
	uint32 act_type = 1;
}

message act_sugar_bawl_info_s2c {
	option (msgid) = 6477;
	uint32 act_type = 1;
	uint32 now_sweet_value = 2;
	repeated p_key_value reward_info = 3;
}

message act_sugar_bawl_fill_c2s {
	option (msgid) = 6478;
	uint32 act_type = 1;
}

message act_sugar_bawl_fill_s2c {
	option (msgid) = 6478;
	uint32 act_type = 1;
	uint32 result = 2;
}

message act_sugar_bawl_get_reward_c2s {
	option (msgid) = 6479;
	uint32 act_type = 1;
	uint32 reward_id = 2;
}

message act_sugar_bawl_get_reward_s2c {
	option (msgid) = 6479;
	uint32 act_type = 1;
	uint32 reward_id = 2;
	uint32 result = 3;
}

message act_mine_sweeper_info_c2s {
	option (msgid) = 6480;
	uint32 act_type = 1;
}

message act_mine_sweeper_info_s2c {
	option (msgid) = 6480;
	uint32 act_type = 1;
	uint32 template_id = 2;
	repeated p_mine_cell cell_list = 3;
}

message act_mine_sweeper_open_c2s {
	option (msgid) = 6481;
	uint32 act_type = 1;
	p_pos pos = 2;
}

message act_mine_sweeper_open_s2c {
	option (msgid) = 6481;
	uint32 act_type = 1;
	p_mine_cell cell = 2;
}

message act_valentine_cp_info_c2s {
	option (msgid) = 6482;
	uint32 act_type = 1;
}

message act_valentine_cp_info_s2c {
	option (msgid) = 6482;
	uint32 act_type = 1;
	uint32 is_cp = 2;
	string cp_name = 3;
	p_head cp_head = 4;
}

message act_valentine_cp_search_c2s {
	option (msgid) = 6483;
	uint32 type = 1;
	string content = 2;
}

message act_valentine_cp_search_s2c {
	option (msgid) = 6483;
	uint32 type = 1;
	repeated p_tanabata_teammate_info teammate_list = 2;
}

message act_valentine_cp_invite_c2s {
	option (msgid) = 6484;
	uint64 role_id = 1;
}

message act_valentine_cp_invite_s2c {
	option (msgid) = 6484;
	uint64 role_id = 1;
}

message act_valentine_cp_handle_invite_c2s {
	option (msgid) = 6485;
	uint64 role_id = 1;
}

message act_valentine_cp_handle_invite_s2c {
	option (msgid) = 6485;
	uint64 role_id = 1;
}

message act_valentine_cp_invite_info_c2s {
	option (msgid) = 6486;
	uint64 role_id = 1;
}

message act_valentine_cp_invite_info_s2c {
	option (msgid) = 6486;
	uint64 role_id = 1;
	uint32 state = 2;
}

message act_monopoly_info_c2s {
	option (msgid) = 6490;
	uint32 act_type = 1;
}

message act_monopoly_info_s2c {
	option (msgid) = 6490;
	uint32 act_type = 1;
	uint32 circle = 2;
	uint32 pos = 3;
	uint32 event = 4;
	repeated p_monopoly_grid grid_list = 5;
	repeated uint32 reward_circle_list = 6 [packed=false];
}

message act_monopoly_dice_c2s {
	option (msgid) = 6491;
	uint32 act_type = 1;
	uint32 op_type = 2;
	uint32 dice_num = 3;
}

message act_monopoly_dice_s2c {
	option (msgid) = 6491;
	uint32 act_type = 1;
	uint32 op_type = 2;
	repeated p_monopoly_dice dice = 3;
}

message act_monopoly_dice_time_s2c {
	option (msgid) = 6492;
	uint32 act_type = 1;
	uint32 dice_time = 2;
}

message act_monopoly_dice_grid_s2c {
	option (msgid) = 6493;
	uint32 act_type = 1;
	uint32 type = 2;
	repeated p_monopoly_grid grid = 3;
}

message act_monopoly_double_c2s {
	option (msgid) = 6494;
	uint32 act_type = 1;
}

message act_monopoly_double_s2c {
	option (msgid) = 6494;
	uint32 act_type = 1;
	uint32 grid_level = 2;
	repeated p_reward reward_list = 3;
}

message act_monopoly_circle_reward_c2s {
	option (msgid) = 6495;
	uint32 act_type = 1;
	uint32 reward_id = 2;
}

message act_monopoly_circle_reward_s2c {
	option (msgid) = 6495;
	uint32 act_type = 1;
	repeated uint32 reward_circle_list = 2 [packed=false];
}

message act_path_info_c2s {
	option (msgid) = 6496;
}

message act_path_info_s2c {
	option (msgid) = 6496;
	uint32 use_plan = 1;
	repeated p_key_string plan_name_list = 2;
	repeated p_path_info path_list = 3;
	repeated p_unlock_tree unlock_path_list = 4;
}

message act_path_enlighten_c2s {
	option (msgid) = 6497;
	uint32 tab_id = 1;
	uint32 path_cfg_id = 2;
	uint64 path_id = 3;
}

message act_path_enlighten_s2c {
	option (msgid) = 6497;
	p_path_info path_info = 1;
	repeated p_unlock_tree unlock_path_list = 2;
}

message act_path_replace_c2s {
	option (msgid) = 6498;
	uint64 id = 1;
}

message act_path_replace_s2c {
	option (msgid) = 6498;
	p_path_info path_info = 1;
}

message act_path_lock_c2s {
	option (msgid) = 6499;
	uint64 id = 1;
	uint32 slot_id = 2;
	uint32 is_lock = 3;
}

message act_path_lock_s2c {
	option (msgid) = 6499;
	p_path_info path_info = 1;
}

message act_path_auto_interception_c2s {
	option (msgid) = 6500;
	uint32 tab_id = 1;
	uint32 path_cfg_id = 2;
	uint64 path_id = 3;
	repeated p_key_value auto_interception = 4;
}

message act_path_auto_interception_s2c {
	option (msgid) = 6500;
	p_path_info path_info = 1;
}

message act_path_enlighten_history_c2s {
	option (msgid) = 6501;
	uint64 id = 1;
}

message act_path_enlighten_history_s2c {
	option (msgid) = 6501;
	uint64 id = 1;
	repeated p_path_history path_info = 2;
}

message act_path_use_plan_c2s {
	option (msgid) = 6502;
	uint32 id = 1;
}

message act_path_use_plan_s2c {
	option (msgid) = 6502;
	uint32 id = 1;
}

message act_path_change_plan_name_c2s {
	option (msgid) = 6503;
	uint32 id = 1;
	string name = 2;
}

message act_path_change_plan_name_s2c {
	option (msgid) = 6503;
	uint32 id = 1;
	string name = 2;
	uint32 result = 3;
}

message act_mashle_alchemy_drug_c2s {
	option (msgid) = 6504;
	uint32 act_type = 1;
	uint32 num = 2;
	uint32 score = 3;
}

message act_mashle_alchemy_drug_s2c {
	option (msgid) = 6504;
	repeated p_key_value drug_list = 1;
	repeated p_reward reward_list = 2;
}

message act_mashle_alchemy_battle_start_c2s {
	option (msgid) = 6505;
	uint64 role_id = 1;
}

message act_mashle_alchemy_battle_start_s2c {
	option (msgid) = 6505;
	uint64 role_id = 1;
	uint32 code = 2;
	uint32 battle_checkout = 3;
	uint64 random_seed = 4;
	repeated p_battle_role roles = 5;
	repeated p_key_value ext = 6;
}

message act_mashle_alchemy_battle_result_c2s {
	option (msgid) = 6506;
	uint64 role_id = 1;
	uint32 result = 2;
	uint32 manual_operators = 3;
	repeated p_battle_operator operators = 4;
	repeated p_key_value args = 5;
}

message act_mashle_alchemy_battle_result_s2c {
	option (msgid) = 6506;
	int32 code = 1;
	uint64 role_id = 2;
	uint32 result = 3;
	repeated p_reward reward_list = 4;
	repeated p_key_value ext = 5;
}

message act_mashle_alchemy_farm_info_c2s {
	option (msgid) = 6507;
	uint64 role_id = 1;
}

message act_mashle_alchemy_farm_info_s2c {
	option (msgid) = 6507;
	uint32 is_push = 1;
	uint64 role_id = 2;
	uint32 type = 3;
	uint32 start_time = 4;
	uint32 time = 5;
	uint64 damage = 6;
	uint32 cnt = 7;
	uint32 battle_cnt = 8;
	repeated uint64 share_list = 9 [packed=false];
}

message act_mashle_alchemy_rank_c2s {
	option (msgid) = 6508;
	uint64 role_id = 1;
}

message act_mashle_alchemy_rank_s2c {
	option (msgid) = 6508;
	uint64 role_id = 1;
	repeated p_rank_info rank_list = 2;
}

message act_mashle_alchemy_claim_box_c2s {
	option (msgid) = 6509;
}

message act_mashle_alchemy_claim_box_s2c {
	option (msgid) = 6509;
}

message act_mashle_alchemy_share_c2s {
	option (msgid) = 6510;
	uint64 role_id = 1;
}

message act_mashle_alchemy_share_s2c {
	option (msgid) = 6510;
	uint64 role_id = 1;
}

//卡皮巴拉数据
message act_slime_info_c2s {
    option (msgid) = 6511;
    uint32 act_type = 1;
}

message act_slime_info_s2c {
    option (msgid) = 6512;
    uint32 act_type = 1;
    uint32 level = 2;
    uint64 exp = 3;
    uint64 combat = 4;
    uint32 rank = 5;
    uint32 stamina = 6;//体力
    uint64 next_recover_time = 7;//体力下次恢复时间
    uint32 cost_stamina = 8;//累计消耗体力
    repeated p_key_value attr_list = 9;
    uint32 fight_stage = 10;
    uint64 curr_hp = 11;
    repeated p_key_value fight_attr = 12;
   	repeated p_slime_event fight_info = 13;
    repeated uint32 passed_stage = 14;//通关关卡
    repeated p_key_value talent_lv = 15;
    repeated p_slime_skill skill_info = 16;
    repeated p_key_value wheel_attr = 17;
    uint32 big_luck_count = 18;//大吉次数
}

// 进入关卡
message act_slime_enter_stage_c2s {
    option (msgid) = 6513;
    uint32 act_type = 1;
    uint32 stage_id = 2;
}

message act_slime_enter_stage_s2c {
    option (msgid) = 6514;
    uint32 act_type = 1;
    uint32 stage_id = 2;
    uint32 slime = 3;
    uint64 next_recover_time = 4;
     uint64 curr_hp = 5;
    repeated p_key_value fight_attr = 6;
}

// 结束关卡
message act_slime_finish_stage_c2s {
    option (msgid) = 6515;
    uint32 act_type = 1;
    uint32 stage_id = 2;
    uint32 result = 3; // 0失败 1成功
}

message act_slime_finish_stage_s2c {
    option (msgid) = 6516;
    uint32 act_type = 1;
    uint32 stage_id = 2;
    uint32 result = 3;
    uint32 start_lv = 4;
    uint64 start_combat = 5;
}

// 关卡内进度上报
message act_slime_stage_progress_c2s {
    option (msgid) = 6517;
    uint32 act_type = 1;
    p_slime_event event_info = 2;
}

message act_slime_stage_progress_s2c {
    option (msgid) = 6518;
    uint32 act_type = 1;
    uint32 result = 2;
}

// 天赋升级
message act_slime_talent_upgrade_c2s {
    option (msgid) = 6519;
    uint32 act_type = 1;
    uint32 attr_type = 2;
}

message act_slime_talent_upgrade_s2c {
    option (msgid) = 6520;
    uint32 act_type = 1;
    repeated p_key_value talent_level = 2;
}

// 大吉转盘抽奖
message act_slime_wheel_draw_c2s {
    option (msgid) = 6521;
    uint32 act_type = 1;
}

message act_slime_wheel_draw_s2c {
    option (msgid) = 6522;
    uint32 act_type = 1;
    uint32 buff_sn = 2;
    uint32 big_luck_num = 3;
    repeated p_key_value wheel_attr = 4;
}


// 体力值刷新
message act_slime_refresh_c2s {
    option (msgid) = 6523;
    uint32 act_type = 1;
}


// 体力值刷新
message act_slime_refresh_s2c {
    option (msgid) = 6524;
    uint32 act_type = 1;
    uint32 slime = 2;
    uint64 next_recover_time = 3;
}

// 战力和属性变化
message act_slime_attr_change_s2c {
    option (msgid) = 6525;
    uint32 act_type = 1;
    uint64 combat = 2;
    uint64 curr_hp = 3;
    uint32 lv = 4;
    uint64 exp = 5;
    uint32 big_luck_count = 6;
    repeated p_key_value attr_list = 7;
    repeated p_key_value fight_attr_list = 8;
}

// 技能洗炼
message act_slime_skill_refine_c2s {
    option (msgid) = 6526;
    uint32 act_type = 1;
    uint32 pos = 2;
}

message act_slime_skill_refine_s2c {
    option (msgid) = 6527;
    uint32 act_type = 1;
    uint32 pos = 2;
    uint32 sn = 3;
    uint32 refine_count=4;
}

// 技能洗炼确认
message act_slime_skill_choose_c2s {
    option (msgid) = 6528;
    uint32 act_type = 1;
    uint32 pos = 2;
    uint32 is_replace = 3;//1：替换 0：不替换
}

message act_slime_skill_choose_s2c {
    option (msgid) = 6529;
    uint32 act_type = 1;
    uint32 pos = 2;
    uint32 is_replace = 3;//1：替换 0：不替换
    uint32 skill_sn = 4;//新技能sn
}

message act_slime_mix_info_c2s {
	option (msgid) = 6531;
	uint32 actType = 1;
}

message act_slime_mix_info_s2c {
	option (msgid) = 6532;
	uint32 actType = 1;
	repeated p_key_value mixRate = 2;
}

message act_slime_mix_c2s {
	option (msgid) = 6533;
	uint32 actType = 1;
	uint32 mixType = 2;
}

message act_slime_mix_s2c {
	option (msgid) = 6534;
	uint32 actType = 1;
	uint32 mixType = 2;
	uint32 result = 3;
	repeated p_reward rewards = 4;
	uint32 mixRate = 5;
}

message act_slime_sweep_c2s {
	option (msgid) = 6535;
	uint32 actType = 1;
	uint32 stage_id = 2;
	uint32 count = 3;
	uint32 big_luck_num = 4;
	repeated p_key_value item = 5;
}

message act_slime_sweep_s2c {
	option (msgid) = 6536;
	uint32 actType = 1;
	uint32 stage_id = 2;
	uint32 count = 3;
	uint32 result = 4;//0:成功
}

message act_castle_info_c2s {
	option (msgid) = 6537;
	uint32 actType = 1;
}

message act_castle_info_s2c {
	option (msgid) = 6538;
	uint32 act_type = 1;
    uint32 level = 2; // 等级
    string exp = 3; // 经验
    uint32 stamina = 4; // 体力
    uint64 next_recover_time = 5; // 下次体力恢复时间
    uint32 passed_layers = 6; // 已通关层数
    uint32 stage = 7; // 阶段
    uint32 layer = 8; // 层数
    uint32 pos = 9; // 1开始
    uint32 is_level_up = 10; // 0：没升级，1：升级
    string power = 11; // 战力
    uint32 pool = 12; // 模板组
    uint32 seed = 13; // 随机种子
    repeated p_key_value_list element_rewards = 14;//k:元素类型 list：进度，领奖pos(1开始)
    uint64 race_start_time = 15;//竞赛开始时间
	uint32 race_start_layer = 16;//层
}

// 炼丹请求
message act_castle_gamble_c2s {
    option (msgid) = 6539;
    uint32 act_type = 1;
    uint32 is_ten = 2;  // 是否十连 1:是 0:否
}

message act_castle_gamble_s2c {
    option (msgid) = 6540;
    uint32 act_type = 1;
    uint32 is_ten = 2;  // 是否十连
    uint32 result = 3;  // 结果 0:成功 其他:失败
    repeated p_key_key_string_list results = 4;  // 炼丹结果列表k:gamble表sn list：元素,数量
    repeated p_key_value_list element_rewards = 5; // 葫芦元素进度 k:元素类型 list:进度,可领取pos
}


// 领取葫芦元素累计奖励
message act_castle_element_reward_c2s {
    option (msgid) = 6541;
    uint32 act_type = 1;
    uint32 element_type = 2;  // 元素类型
    uint32 pos = 3;  // 领取位置，起始1
}

message act_castle_element_reward_s2c {
    option (msgid) = 6542;
    uint32 act_type = 1;
    uint32 element_type = 2;  // 元素类型
    uint32 pos = 3;  // 领取位置
}


// 属性变化广播
message act_castle_attr_change_s2c {
    option (msgid) = 6543;
    uint32 act_type = 1;
    string power = 2;  // 战力
    uint32 level = 3;  // 等级
    string exp = 4;  // 经验
    uint32 is_level_up = 5; // 0：没升级，1：升级
}

// 体力值刷新
message act_stamina_refresh_c2s {
    option (msgid) = 6544;
    uint32 act_type = 1;
}


// 体力值刷新
message act_stamina_refresh_s2c {
    option (msgid) = 6545;
    uint32 act_type = 1;
    uint32 stamina = 2;
    uint64 next_recover_time = 3;
}


//移动
message act_castle_move_c2s {
    option (msgid) = 6546;
    uint32 act_type = 1;
    uint32 layer = 2;
    uint32 pos = 3;  //位置：1开始
}

message act_castle_move_s2c {
    option (msgid) = 6547;
    uint32 act_type = 1;
    uint64 pos = 2;  //位置：1开始
    uint32 is_end_layer = 3; //1：最后一层
    uint32 stage = 4; // 阶段
    uint32 layer = 5; // 层数
    uint32 total_layer = 6;//总层数
    uint32 pool = 7; // 模板
    uint32 seed = 8; // 随机种子
    repeated p_reward rewards = 9;
}

//触发竞赛
message act_castle_race_s2c {
    option (msgid) = 6548;
    uint32 act_type = 1;
    p_role_figure opponent_figure = 2;//对手外观
    string opponent_name = 3;
}


//竞赛开始
message act_castle_race_op_c2s {
    option (msgid) = 6549;
    uint32 act_type = 1;
    uint32 op = 2;//1:开始，2：拒绝
}

message act_castle_race_start_s2c {
    option (msgid) = 6550;
    uint32 act_type = 1;
    uint64 start_time = 2;//竞赛开始时间
    uint32 curr_total_layer = 3;
}

//竞赛结果
message act_castle_race_result_c2s {
    option (msgid) = 6551;
    uint32 act_type = 1;
    uint32 is_win = 2;
}

message act_castle_race_result_s2c {
	option (msgid) = 6552;
    uint32 act_type = 1;
    uint32 is_win = 2;
}

//神将打造信息
message act_dungeon_craft_info_c2s {
    option (msgid) = 6553;
    uint32 act_type = 1;
}

message act_dungeon_craft_info_s2c {
    option (msgid) = 6554;
    uint32 act_type = 1;
    uint32 total_num = 2;//打造总次数
}

message act_dungeon_craft_c2s {
    option (msgid) = 6555;
    uint32 act_type = 1;
    uint32 craft_type = 2;
    uint32 num = 3;//打造次数
}

message act_dungeon_craft_s2c {
    option (msgid) = 6556;
    uint32 act_type = 1;
    uint32 craft_type = 2;
    uint32 num = 3;//打造次数
    uint32 total_num = 4;//打造总次数
    repeated p_reward big_rewards = 5;//大奖奖励
}

// 翻牌活动信息请求
message act_card_eliminate_info_c2s {
    option (msgid) = 6557;
    uint32 act_type = 1;
}

// 翻牌活动信息响应
message act_card_eliminate_info_s2c {
    option (msgid) = 6558;
    uint32 act_type = 1;
    uint64 max_score = 2;                  // 最高积分
    uint32 rank = 3;                  	   // 排名
    uint32 ticket = 4;                     // 门票数量
    uint64 next_recover_time = 5;          // 下次门票恢复时间
    repeated uint32 random_buffs = 6;	   // 选择buf阶段有值						
    uint32 stamina = 7;                    // 当前局内体力，游戏中值>0
    uint32 round = 8;                      // 当前轮数
    uint32 score = 9;                      // 当前积分
    uint32 total_step = 10;                // 累计步数
    p_card_eliminate_task task = 11; 	   // 任务
    uint32 combo = 12;                      // 当前连消次数
    repeated p_card_eliminate_card cards= 13;    // 卡牌信息
    repeated p_key_value buffs = 14;  	   // 已获得的buff列表k:sn,v:星数
}

// 开始游戏请求
message act_card_eliminate_start_c2s {
    option (msgid) = 6559;
    uint32 act_type = 1;
}

// 开始游戏响应
message act_card_eliminate_start_s2c {
    option (msgid) = 6560;
    uint32 act_type = 1;
    uint32 round = 2;                      // 当前轮数
    uint32 stamina = 3;                    // 当前局内体力
    p_card_eliminate_task task = 4;    	   // 任务
}

// 翻牌请求
message act_card_eliminate_flip_c2s {
    option (msgid) = 6561;
    uint32 act_type = 1;
    uint32 pos = 2;                        // 翻牌位置
}

// 翻牌响应
message act_card_eliminate_flip_s2c {
    option (msgid) = 6562;
    uint32 act_type = 1;
    repeated p_key_value flip_pos= 2;	   // k：pos v:element_id
    repeated p_key_value x_ray_pos= 3;	   // 透视效果的卡牌位置
    repeated uint32 feliminated_pos = 4;   // 消除的卡牌位置
    uint32 stamina = 5;                    // 剩余体力
    uint32 score = 6;                      // 当前积分
    uint32 combo = 7;                      // 当前连消次数
    p_card_eliminate_task task = 8;		   // 任务
}

// 轮次完成响应
message act_card_eliminate_round_complete_s2c {
    option (msgid) = 6563;
    uint32 act_type = 1;
    uint32 round = 2;                      // 完成的轮数
    uint32 stamina = 3;                    // 更新后的体力
    repeated uint32 buff_choices = 4;  	   // 可选的buff列表
}

// 选择buff请求
message act_card_eliminate_select_buff_c2s {
    option (msgid) = 6564;
    uint32 act_type = 1;
    uint32 buff_id = 2;                    // 选择的buff ID
}

// 选择buff响应
message act_card_eliminate_select_buff_s2c {
    option (msgid) = 6565;
    uint32 act_type = 1;
    uint32 buff_id = 2;    // 选择的buff
    repeated p_key_value all_buffs = 3;  // 更新后的所有buff
    uint32 round = 4;                      // 当前轮数
    uint32 stamina = 5;                  // 剩余体力
    p_card_eliminate_task task = 6;		 // 任务
}

// 游戏结束响应
message act_card_eliminate_game_over_c2s {
    option (msgid) = 6566;
    uint32 act_type = 1;
}


// 游戏结束响应,体力用完或主动请求
message act_card_eliminate_game_over_s2c {
    option (msgid) = 6567;
    uint32 act_type = 1;
    uint32 final_score = 2;                // 最终得分
    uint32 highest_score = 3;              // 历史最高分
    uint32 rank = 4;                       // 当前排名
    uint32 total_step = 5;                // 累计步数
    repeated p_reward rewards = 6;         // 获得的奖励
}

// 无职联动-好感活动信息请求
message act_wuzhi_love_info_c2s {
    option (msgid) = 6700;
    uint32 act_type = 1;
}

message act_wuzhi_love_info_s2c {
    option (msgid) = 6701;
    uint32 act_type = 1;
    uint32 event_status = 2; // 1:选择角色阶段 2:前置任务阶段 3:正式活动阶段
    uint32 selected_char_id = 3;
    repeated p_favor_info favor_infos = 4;
    // 任务信息现在由通用任务系统处理
}

// 无职联动-选择角色
message act_wuzhi_love_select_char_c2s {
    option (msgid) = 6702;
    uint32 act_type = 1;
    uint32 char_id = 2;
}

message act_wuzhi_love_select_char_s2c {
    option (msgid) = 6703;
    uint32 act_type = 1;
    uint32 char_id = 2;
}

// 无职联动-合成手办
message act_wuzhi_love_compose_figure_c2s {
    option (msgid) = 6704;
    uint32 act_type = 1;
}

message act_wuzhi_love_compose_figure_s2c {
    option (msgid) = 6705;
    uint32 act_type = 1;
    uint32 figure_id = 2;
}

// 无职联动-赠送礼物
message act_wuzhi_love_give_gift_c2s {
    option (msgid) = 6706;
    uint32 act_type = 1;
    uint32 char_id = 2;
    repeated p_key_value item_sn_num = 3;
}

message act_wuzhi_love_give_gift_s2c {
    option (msgid) = 6707;
    uint32 act_type = 1;
    uint32 char_id = 2;
    uint32 new_level = 3;
    uint32 new_exp = 4;
}

// 无职联动-领取好感等级奖励
message act_wuzhi_love_claim_level_reward_c2s {
    option (msgid) = 6708;
    uint32 act_type = 1;
    uint32 char_id = 2;
    uint32 level = 3;
}

message act_wuzhi_love_claim_level_reward_s2c {
    option (msgid) = 6709;
    uint32 act_type = 1;
    uint32 char_id = 2;
    uint32 level = 3;
}

// 每日任务奖励领取现在由通用任务系统处理
