package org.gof.demo.worldsrv.activity.test;

import org.gof.demo.worldsrv.activity.ActivityControlType;
import org.gof.demo.worldsrv.activity.calculator.ActivityControlWuzhiLove;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlWuzhiLoveData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.support.Log;

/**
 * 无职联动-好感度活动测试类
 */
public class WuzhiLoveActivityTest {
    
    /**
     * 测试活动数据创建和序列化
     */
    public static void testDataSerialization() {
        Log.game.info("开始测试无职好感度活动数据序列化...");
        
        // 创建测试数据
        ControlWuzhiLoveData data = new ControlWuzhiLoveData();
        data.eventStatus = 2;
        data.selectedCharId = 1001;
        
        // 添加好感度信息
        ControlWuzhiLoveData.FavorInfo favorInfo = new ControlWuzhiLoveData.FavorInfo(1001);
        favorInfo.level = 3;
        favorInfo.exp = 150;
        favorInfo.rewardedLv.add(1);
        favorInfo.rewardedLv.add(2);
        data.setFavorInfo(1001, favorInfo);
        
        // 添加每日任务
        ControlWuzhiLoveData.DailyTask task1 = new ControlWuzhiLoveData.DailyTask(1001);
        task1.progress = 5;
        task1.completed = true;
        data.dailyTasks.add(task1);
        
        ControlWuzhiLoveData.DailyTask task2 = new ControlWuzhiLoveData.DailyTask(1002);
        task2.progress = 2;
        data.dailyTasks.add(task2);
        
        // 测试序列化
        String jsonStr = data.toJSON();
        Log.game.info("序列化结果: {}", jsonStr);
        
        // 测试反序列化
        ControlWuzhiLoveData newData = new ControlWuzhiLoveData(jsonStr);
        
        // 验证数据
        assert newData.eventStatus == 2 : "活动状态不匹配";
        assert newData.selectedCharId == 1001 : "选择角色不匹配";
        
        ControlWuzhiLoveData.FavorInfo newFavorInfo = newData.getFavorInfo(1001);
        assert newFavorInfo != null : "好感度信息为空";
        assert newFavorInfo.level == 3 : "好感度等级不匹配";
        assert newFavorInfo.exp == 150 : "好感度经验不匹配";
        assert newFavorInfo.rewardedLv.contains(1) : "已领取奖励等级不匹配";
        assert newFavorInfo.rewardedLv.contains(2) : "已领取奖励等级不匹配";
        
        assert newData.dailyTasks.size() == 2 : "每日任务数量不匹配";
        ControlWuzhiLoveData.DailyTask newTask1 = newData.getDailyTask(1001);
        assert newTask1 != null : "每日任务1为空";
        assert newTask1.progress == 5 : "任务1进度不匹配";
        assert newTask1.completed : "任务1完成状态不匹配";
        
        Log.game.info("无职好感度活动数据序列化测试通过!");
    }
    
    /**
     * 测试好感度升级逻辑
     */
    public static void testFavorLevelUp() {
        Log.game.info("开始测试好感度升级逻辑...");
        
        ControlWuzhiLoveData.FavorInfo favorInfo = new ControlWuzhiLoveData.FavorInfo(1001);
        favorInfo.level = 1;
        favorInfo.exp = 80;
        
        // 模拟升级检查（需要在ActivityControlWuzhiLove中实现公共方法）
        // 这里只是示例，实际需要调用控制器的方法
        
        Log.game.info("好感度升级逻辑测试完成");
    }
    
    /**
     * 测试每日任务刷新逻辑
     */
    public static void testDailyTaskRefresh() {
        Log.game.info("开始测试每日任务刷新逻辑...");
        
        ControlWuzhiLoveData data = new ControlWuzhiLoveData();
        
        // 测试是否需要刷新
        boolean needRefresh = data.needRefreshDailyTasks();
        Log.game.info("是否需要刷新每日任务: {}", needRefresh);
        
        // 模拟刷新任务
        java.util.List<Integer> newTasks = java.util.Arrays.asList(1001, 1002, 1003);
        data.refreshDailyTasks(newTasks);
        
        assert data.dailyTasks.size() == 3 : "刷新后任务数量不匹配";
        assert data.lastDailyRefreshTime > 0 : "刷新时间未更新";
        
        Log.game.info("每日任务刷新逻辑测试通过!");
    }
    
    /**
     * 测试活动控制器创建
     */
    public static void testActivityControllerCreation() {
        Log.game.info("开始测试活动控制器创建...");
        
        try {
            ActivityControlWuzhiLove controller = new ActivityControlWuzhiLove(ActivityControlType.Act_6208);
            assert controller != null : "控制器创建失败";
            
            Log.game.info("活动控制器创建测试通过!");
        } catch (Exception e) {
            Log.game.error("活动控制器创建测试失败: {}", e.getMessage());
        }
    }
    
    /**
     * 运行所有测试
     */
    public static void runAllTests() {
        Log.game.info("=== 开始无职联动-好感度活动测试 ===");
        
        try {
            testDataSerialization();
            testFavorLevelUp();
            testDailyTaskRefresh();
            testActivityControllerCreation();
            
            Log.game.info("=== 所有测试通过! ===");
        } catch (Exception e) {
            Log.game.error("测试失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 测试活动状态转换
     */
    public static void testActivityStatusTransition() {
        Log.game.info("开始测试活动状态转换...");
        
        ControlWuzhiLoveData data = new ControlWuzhiLoveData();
        
        // 初始状态应该是选择角色阶段
        assert data.eventStatus == 1 : "初始状态不正确";
        
        // 模拟选择角色后的状态变化
        data.selectedCharId = 1001;
        data.eventStatus = 2;
        
        // 模拟完成前置任务后的状态变化
        data.eventStatus = 3;
        
        Log.game.info("活动状态转换测试通过!");
    }
    
    /**
     * 测试前置任务完成检查
     */
    public static void testPreTaskCompletion() {
        Log.game.info("开始测试前置任务完成检查...");

        ControlWuzhiLoveData data = new ControlWuzhiLoveData();

        // 测试前置任务完成状态
        boolean allCompleted = data.isAllPreTaskCompleted();
        Log.game.info("所有前置任务是否完成: {}", allCompleted);

        Log.game.info("前置任务完成检查测试完成");
    }

    /**
     * 测试配表集成
     */
    public static void testConfigIntegration() {
        Log.game.info("开始测试配表集成...");

        try {
            // 测试ConfWuzhiLove配表
            org.gof.demo.worldsrv.config.ConfWuzhiLove wuzhiConf =
                org.gof.demo.worldsrv.config.ConfWuzhiLove.getBy(
                    org.gof.demo.worldsrv.config.ConfWuzhiLove.K.type,
                    org.gof.demo.worldsrv.activity.ActivityControlType.Act_6208);

            if (wuzhiConf != null) {
                Log.game.info("找到无职好感配置: id={}, type={}, group_id={}",
                    wuzhiConf.id, wuzhiConf.type, wuzhiConf.group_id);

                if (wuzhiConf.gift != null) {
                    Log.game.info("礼物配置数量: {}", wuzhiConf.gift.length);
                }

                if (wuzhiConf.daily_task_id != null) {
                    Log.game.info("每日任务ID范围: {} - {}",
                        wuzhiConf.daily_task_id[0], wuzhiConf.daily_task_id[1]);
                }
            } else {
                Log.game.warn("未找到Act_6208的配置");
            }

            // 测试ConfWuzhiLoveLevel配表
            java.util.List<org.gof.demo.worldsrv.config.ConfWuzhiLoveLevel> levelConfs =
                org.gof.demo.worldsrv.config.ConfWuzhiLoveLevel.findBy(
                    org.gof.demo.worldsrv.config.ConfWuzhiLoveLevel.K.character_id, 1001);

            Log.game.info("角色1001的好感等级配置数量: {}", levelConfs.size());

            Log.game.info("配表集成测试完成");
        } catch (Exception e) {
            Log.game.error("配表集成测试失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 运行所有测试（更新版本）
     */
    public static void runAllTestsWithConfig() {
        Log.game.info("=== 开始无职联动-好感度活动完整测试 ===");

        try {
            testDataSerialization();
            testFavorLevelUp();
            testDailyTaskRefresh();
            testActivityControllerCreation();
            testActivityStatusTransition();
            testPreTaskCompletion();
            testConfigIntegration();

            Log.game.info("=== 所有测试通过! ===");
        } catch (Exception e) {
            Log.game.error("测试失败: {}", e.getMessage(), e);
        }
    }
}
