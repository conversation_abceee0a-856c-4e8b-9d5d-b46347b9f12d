package org.gof.demo.worldsrv.activity.calculator;

import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlAirdropGiftData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfActivityTerm;
import org.gof.demo.worldsrv.config.ConfAirdropGift;
import org.gof.demo.worldsrv.config.ConfAirdropGiftBox;
import org.gof.demo.worldsrv.entity.ActControlData;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.produce.RandomUtil;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ActivityControlAirdropGift extends AbstractActivityControl {
    public ActivityControlAirdropGift(int type) {
        super(type);
    }

    @Override
    public void dailyResetActivityData(HumanObject humanObj) {
        super.dailyResetActivityData(humanObj);
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ControlAirdropGiftData data = (ControlAirdropGiftData) controlData.getControlData();
        data.freeNum = 0;
        controlData.updateControlData();
    }

    private Define.p_airdrop_box.Builder to_p_airdrop_box(int pos, int boxSn, int boxGear, Map<Integer, Integer> rewardMap) {
        Define.p_airdrop_box.Builder box = Define.p_airdrop_box.newBuilder();
        box.setPos(pos);
        box.setBoxSn(boxSn);
        box.setBoxGear(boxGear);
        box.addAllRewardList(InstanceManager.inst().to_p_rewardList(rewardMap));
        return box;
    }

    public void on_act_airdrop_gift_info_c2s(HumanObject humanObj) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ControlAirdropGiftData data = (ControlAirdropGiftData) controlData.getControlData();
        MsgAct.act_airdrop_gift_info_s2c.Builder msg = MsgAct.act_airdrop_gift_info_s2c.newBuilder();
        msg.setActType(type);
        msg.setFreeNum(data.freeNum);
        msg.setDrawNum(data.drawNum);
        msg.setGiftSn(data.giftSn);
        msg.setScore((int) data.score);
        for (int pos : data.posBoxSnMap.keySet()) {
            msg.addBoxList(to_p_airdrop_box(pos, data.posBoxSnMap.get(pos), data.posBoxGearMap.get(pos), data.posBoxItemMap.get(pos)));
        }
        humanObj.sendMsg(msg);
    }

    public void on_act_airdrop_gift_draw_c2s(HumanObject humanObj, int drawType) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ActControlData actData = controlData.getActControlData();
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, actData.getRound());
        if (confTerm == null) {
            Log.activity.error("[空投礼包]活动找不到活动期数表ConfActivityTerm, type={}, round={}", type, actData.getRound());
            return;
        }
        ControlAirdropGiftData data = (ControlAirdropGiftData) controlData.getControlData();
        ConfAirdropGift conf = ConfAirdropGift.get(data.giftSn);
        // 1.检测逻辑
        int num;
        if (drawType == 1 && data.freeNum < 1) {
            // 单抽还有免费次数的话，就直接抽
            num = 1;
            data.freeNum++;
        } else if (drawType == 1) {
            // 单抽，那还要检测抽卡次数是否超了活动期间内可以抽的次数
            if (data.drawNum + 1 > confTerm.parameter) {
                return;
            }
            ReasonResult rr = ProduceManager.inst().checkAndCostItem(humanObj, conf.single_cost, MoneyItemLogKey.空投礼包);
            if (!rr.success) {
                Inform.sendMsg_error(humanObj, ErrorTip.ItemNotEnough);
                return;
            }
            num = 1;
            data.drawNum += num;
        } else if (drawType == 2) {
            // 5连抽，那还要检测抽卡次数是否超了活动期间内可以抽的次数
            if (data.drawNum + 5 > confTerm.parameter) {
                return;
            }
            ReasonResult rr = ProduceManager.inst().checkAndCostItem(humanObj, conf.five_cost, MoneyItemLogKey.空投礼包);
            if (!rr.success) {
                Inform.sendMsg_error(humanObj, ErrorTip.ItemNotEnough);
                return;
            }
            num = 5;
            data.drawNum += num;
        } else {
            return;
        }
        long addValue = (long) conf.point * num;
        data.score += addValue;
        addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_2023, addValue, false, new Object[]{addValue});
        // 2.生成奖励
        // 2.1 计算权重
        int[][] weight = new int[conf.output_box.length + 1][];
        int weightSum = 0;
        int index = 0;
        for (; index < conf.output_box.length; index++) {
            weight[index] = conf.output_box[index];
            weightSum += weight[index][1];
        }
        weight[index] = new int[]{0, 10000 - weightSum};
        List<Integer> boxSnList = new ArrayList<>();
        List<int[]> rewardList = new ArrayList<>();
        // 2.2 循环生成奖励，计算
        for (int i = 0; i < num; i++) {
            Map<Integer, Integer> dropItemMap = ProduceManager.inst().getDropMap(conf.output);
            for (Map.Entry<Integer, Integer> entry : dropItemMap.entrySet()) {
                rewardList.add(new int[] { entry.getKey(), entry.getValue() });
            }
            if (data.posBoxSnMap.size() + boxSnList.size() > conf.num) {
                continue;
            }
            int sn = Utils.getRandRangeValue(weight);
            if (sn != 0) {
                boxSnList.add(sn);
            }
        }
        // 2.3 生成宝箱数据
        List<Integer> boxPosList = new ArrayList<>();
        if (!boxSnList.isEmpty()) {
            int boxSnIndex = 0;
            for (int i = 1; i <= conf.num && boxSnIndex < boxSnList.size(); i++) {
                if (data.posBoxSnMap.containsKey(i)) {
                    continue;
                }
                boxPosList.add(i);
                int boxSn = boxSnList.get(boxSnIndex);
                ConfAirdropGiftBox confBox = ConfAirdropGiftBox.get(boxSn);
                int boxGear = Utils.getRandRange(confBox.box_output);
                Map<Integer, Integer> boxItemMap = ProduceManager.inst().getDropMap(confBox.box_output[boxGear][0]);
                data.posBoxSnMap.put(i, boxSn);
                data.posBoxGearMap.put(i, boxGear);
                data.posBoxItemMap.put(i, boxItemMap);
                boxSnIndex++;
            }
        }
        Map<Integer, Integer> rewardMap = new HashMap<>();
        for (int[] ints : rewardList) {
            rewardMap.put(ints[0], rewardMap.getOrDefault(ints[0], 0) + ints[1]);
        }
        ProduceManager.inst().produceAdd(humanObj, rewardMap, MoneyItemLogKey.空投礼包);
        controlData.updateControlData();
        // 3.发送消息
        MsgAct.act_airdrop_gift_draw_s2c.Builder msg = MsgAct.act_airdrop_gift_draw_s2c.newBuilder();
        msg.setActType(type);
        msg.setFreeNum(data.freeNum);
        msg.setDrawNum(data.drawNum);
        msg.setScore((int) data.score);
        msg.addAllRewardList(InstanceManager.inst().to_p_rewardList2(rewardList.toArray(new int[0][])));
        for (int pos : boxPosList) {
            msg.addBoxList(to_p_airdrop_box(pos, data.posBoxSnMap.get(pos), data.posBoxGearMap.get(pos), data.posBoxItemMap.get(pos)));
        }
        humanObj.sendMsg(msg);
    }

    public void on_act_airdrop_gift_open_c2s(HumanObject humanObj, int pos) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ControlAirdropGiftData data = (ControlAirdropGiftData) controlData.getControlData();
        if (!data.posBoxSnMap.containsKey(pos)) {
            Log.activity.error("[空投活动]打开宝箱失败，宝箱不存在！humanId={}, actType={}, pos={}", humanObj.id, type, pos);
            return;
        }
        int boxSn = data.posBoxSnMap.get(pos);
        int boxGear = data.posBoxGearMap.get(pos);
        Map<Integer, Integer> rewardMap = data.posBoxItemMap.get(pos);
        ConfAirdropGiftBox conf = ConfAirdropGiftBox.get(boxSn);
        if (conf == null) {
            Log.activity.error("[空投活动]打开宝箱失败，宝箱配置不存在！humanId={}, actType={}, pos={}", humanObj.id, type, pos);
            return;
        }
        data.posBoxSnMap.remove(pos);
        data.posBoxGearMap.remove(pos);
        data.posBoxItemMap.remove(pos);
        data.score += conf.open_point[boxGear];
        controlData.updateControlData();
        addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_2023, conf.open_point[boxGear], false, new Object[]{conf.open_point[boxGear]});
        int[] cost = conf.open_key[boxGear];
        ReasonResult rr = ProduceManager.inst().checkAndCostItem(humanObj, cost[0], cost[1], MoneyItemLogKey.空投礼包);
        if (!rr.success) {
            Inform.sendMsg_error(humanObj, ErrorTip.ItemNotEnough);
            return;
        }
        // 2.处理奖励双倍
        int randomValue = RandomUtil.random(10000);
        int rewardMultiple = 1;
        if (randomValue <= conf.double_ratio[boxGear]) {
            rewardMultiple = 2;
        }
        for (Integer key : rewardMap.keySet()) {
            rewardMap.put(key, rewardMap.get(key) * rewardMultiple);
        }
        ProduceManager.inst().produceAdd(humanObj, rewardMap, MoneyItemLogKey.空投礼包);
        if (rewardMultiple == 1) {
            InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewardMap);
        } else {
            List<Define.p_key_value> extList = new ArrayList<>();
            extList.add(Define.p_key_value.newBuilder().setK(1).setV(rewardMultiple).build());
            InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_1001011, InstanceManager.inst().to_p_rewardList(rewardMap), extList);
        }
        // 3.发消息
        MsgAct.act_airdrop_gift_open_s2c.Builder msg = MsgAct.act_airdrop_gift_open_s2c.newBuilder();
        msg.setActType(type);
        msg.setMultiple(rewardMultiple);
        msg.setBox(to_p_airdrop_box(pos, boxSn, boxGear, rewardMap));
        msg.setScore((int) data.score);
        humanObj.sendMsg(msg);
    }

    public void on_act_airdrop_gift_del_c2s(HumanObject humanObj, int pos) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ControlAirdropGiftData data = (ControlAirdropGiftData) controlData.getControlData();
        if (!data.posBoxSnMap.containsKey(pos)) {
            return;
        }
        int boxSn = data.posBoxSnMap.get(pos);
        int boxGear = data.posBoxGearMap.get(pos);
        Map<Integer, Integer> rewardMap = data.posBoxItemMap.get(pos);
        data.posBoxSnMap.remove(pos);
        data.posBoxGearMap.remove(pos);
        data.posBoxItemMap.remove(pos);
        controlData.updateControlData();

        MsgAct.act_airdrop_gift_del_s2c.Builder msg = MsgAct.act_airdrop_gift_del_s2c.newBuilder();
        msg.setActType(type);
        msg.setDelBox(to_p_airdrop_box(pos, boxSn, boxGear, rewardMap));
        humanObj.sendMsg(msg);
    }
}
