package org.gof.demo.worldsrv.activity.calculator;


import org.gof.core.support.Param;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.activity.data.serverdata.IServerActData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfActivityControl;
import org.gof.demo.worldsrv.config.ConfPayMall;
import org.gof.demo.worldsrv.entity.ActivityData;
import org.gof.demo.worldsrv.msg.Define;

public interface IActivityControl {
    /**
     * 初始化活动的服务器活动数据（有的活动需要服务器内所有玩家公用一套数据），循环活动的话根据round判断是轮次刷新还是初始化，两种情况都会调用
     */
    IServerActData initServerActData(int round);
    IServerActData initServerActData(int round, ActivityData activityData);
    /**
     * 转换已有的活动数据（用作起服加载）, 增加round参数为了json为空字串走默认初始化逻辑使用
     */
    IServerActData parseServerActData(String json, int round);
    /**
     * 结算全服活动数据（例如：阵营对抗，需要根据活动结束时阵营胜负情况给玩家发奖励）
     */
    void settleServerActData(int serverId, int actSn, int round);

    public void initActivityData(HumanObject humanObj, ConfActivityControl confControl, ActivityVo vo);
    public void sendActivityData(HumanObject humanObj);
    public Define.p_act.Builder toActivityData(HumanObject humanObj);

    void onActivityClose(HumanObject humanObj, ConfActivityControl confControl);
    void onActivityEndShow(HumanObject humanObj, ConfActivityControl confControl);

    void dailyResetActivityData(HumanObject humanObj);

    /**
     * 活动解锁的处理（处理活动业务数据不应该在活动开启时初始化的处理）
     */
    void unlockActivity(HumanObject humanObj);

    /**
     * 支付回调活动处理，只有礼包的条件配置是活动的才会调用该接口，如果活动需要根据支付做后续操作就重写该方法
     * @param humanObj      玩家对象
     * @param payMallSn     ConfPayMall的sn
     */
    void pay(HumanObject humanObj, int payMallSn);

    /**
     * 活动道具使用
     */
    void itemUse(HumanObject humanObj, int goodsId, int num);
    void itemAdd(HumanObject humanObj, int goodsId, int num);

    boolean canPay(HumanObject humanObj, ConfPayMall confPayMall);

    /**
     * 事件响应
     * 例如：钓鱼冲刺活动，钓鱼本身是单独的养成，我需要在活动中记录钓鱼相关的数据，
     * 为了解耦，用事件响应来处理，钓鱼养成发送事件，活动根据事件来处理，就不会在钓鱼养成的代码处直接调用活动代码
     */
    void handleEvent(HumanObject humanObj, int event, Param param);

    /**
     * 活动信息请求
     */
    void on_act_info_c2s(HumanObject humanObj);
}
