package org.gof.demo.worldsrv.activity.calculator;

import org.gof.core.Port;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlWuzhiLoveData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfActivityControl;
import org.gof.demo.worldsrv.config.ConfActivityTask;
import org.gof.demo.worldsrv.config.ConfActivityTerm;
import org.gof.demo.worldsrv.config.ConfWuzhiLove;
import org.gof.demo.worldsrv.config.ConfWuzhiLoveLevel;
import org.gof.demo.worldsrv.activity.data.ActivityTaskVO;
import org.gof.demo.worldsrv.entity.ActControlData;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct2;
import org.gof.demo.worldsrv.support.Log;

import java.util.*;

import java.util.*;

/**
 * 无职联动-好感度活动控制器
 */
public class ActivityControlWuzhiLove extends AbstractActivityControl {
    
    public ActivityControlWuzhiLove(int type) {
        super(type);
    }

    /**
     * 获取无职好感配置
     */
    private ConfWuzhiLove getWuzhiLoveConf(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return null;
        }

        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            return null;
        }

        return ConfWuzhiLove.getBy(ConfWuzhiLove.K.type, type, ConfWuzhiLove.K.group_id, confTerm.group_id);
    }

    /**
     * 获取礼物的好感度值
     */
    private int getGiftFavorValue(ConfWuzhiLove conf, int itemId) {
        if (conf.gift == null || conf.gift.length == 0) {
            return 0;
        }

        for (int[] giftConfig : conf.gift) {
            if (giftConfig.length >= 2 && giftConfig[0] == itemId) {
                return giftConfig[1]; // 返回好感度值
            }
        }

        return 0; // 不是有效的礼物
    }

    /**
     * 清除前置任务并添加每日任务
     */
    private void clearPreTasksAndAddDailyTasks(HumanObject humanObj, ActivityControlObjectData data) {
        // 获取配置
        ConfWuzhiLove conf = getWuzhiLoveConf(humanObj);
        if (conf == null) {
            return;
        }

        // 清除前置任务
        if (conf.pre_task != null && conf.pre_task.length > 0) {
            for (int preTaskGroupId : conf.pre_task) {
                data.removeActivityTaskByGroupId(preTaskGroupId);
            }
        }

        // 添加每日任务
        addDailyTasksFromConfig(humanObj, data, conf);

        // 发送任务更新
        sendActivityTaskData(humanObj);
    }

    /**
     * 从配置添加每日任务
     */
    private void addDailyTasksFromConfig(HumanObject humanObj, ActivityControlObjectData data, ConfWuzhiLove conf) {
        if (conf.daily_task_id == null || conf.daily_task_id.length < 2) {
            Log.activity.error("每日任务配置错误，humanId={}", humanObj.id);
            return;
        }

        // 从配置表获取任务池范围
        int taskIdStart = conf.daily_task_id[0];
        int taskIdEnd = conf.daily_task_id[1];
        int dailyTaskNum = conf.daily_task_num;

        // 生成任务池
        List<Integer> taskPool = new ArrayList<>();
        for (int i = taskIdStart; i <= taskIdEnd; i++) {
            taskPool.add(i);
        }

        // 使用当天的时间作为随机种子，确保全服一致
        long currentTime = System.currentTimeMillis();
        long todayStart = currentTime - (currentTime % (24 * 60 * 60 * 1000));
        Random random = new Random(todayStart);

        // 随机选择任务
        Collections.shuffle(taskPool, random);
        List<Integer> selectedTasks = taskPool.subList(0, Math.min(dailyTaskNum, taskPool.size()));

        // 添加任务到通用任务系统
        for (int taskId : selectedTasks) {
            ConfActivityTask confTask = ConfActivityTask.get(taskId);
            if (confTask != null) {
                ActivityTaskVO taskVO = new ActivityTaskVO(data.getActControlData().getActivitySn(), type, confTask, 0);
                data.addActivityTask(taskVO, false);
            }
        }

        data.setActivityTask();
        Log.activity.info("添加每日任务，humanId={}, selectedTasks={}", humanObj.id, selectedTasks);
    }

    /**
     * 检查是否完成所有前置任务
     */
    private boolean isAllPreTaskCompleted(HumanObject humanObj, ActivityControlObjectData data) {
        // 获取配置
        ConfWuzhiLove conf = getWuzhiLoveConf(humanObj);
        if (conf == null || conf.pre_task == null || conf.pre_task.length == 0) {
            return true; // 没有前置任务
        }

        // 检查每个前置任务组是否完成
        for (int preTaskGroupId : conf.pre_task) {
            if (!isTaskGroupCompleted(data, preTaskGroupId)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查指定任务组是否完成
     */
    private boolean isTaskGroupCompleted(ActivityControlObjectData data, int taskGroupId) {
        // 通过通用任务系统检查任务组完成状态
        Map<Integer, Integer> perfectMap = Utils.jsonToMapIntInt(data.getActControlData().getPerfectMap());
        return perfectMap.containsKey(taskGroupId);
    }

    @Override
    protected void initActivityControlData(HumanObject humanObj, ActivityControlObjectData data, ConfActivityControl confControl, ActivityVo vo) {
        // 先调用父类方法初始化通用任务（包括前置任务）
        super.initActivityControlData(humanObj, data, confControl, vo);

        // 获取无职好感配置
        ConfWuzhiLove conf = getWuzhiLoveConf(humanObj);
        if (conf == null) {
            Log.activity.error("无职好感活动配置不存在，type={}, humanId={}", type, humanObj.id);
            return;
        }

        // 初始化活动特有数据
        ControlWuzhiLoveData controlData = (ControlWuzhiLoveData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("无职好感活动数据为空，humanId={}", humanObj.id);
            return;
        }

        // 前置任务已经通过父类方法初始化到data.getActivityTask()中
        // 这里只需要更新活动数据
        data.updateControlData();
    }

    @Override
    public void dailyResetActivityData(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }

        ControlWuzhiLoveData controlData = (ControlWuzhiLoveData) data.getControlData();
        if (controlData == null) {
            return;
        }

        // 根据活动阶段处理不同的任务
        if (controlData.eventStatus == 2) {
            // 前置任务阶段：调用父类方法处理前置任务重置
            super.dailyResetActivityData(humanObj);
        } else if (controlData.eventStatus == 3) {
            // 正式活动阶段：清除前置任务，添加每日任务
            clearPreTasksAndAddDailyTasks(humanObj, data);
        }
    }
    
    /**
     * 处理活动信息请求
     */
    public void on_act_wuzhi_love_info_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}请求无职好感度活动{}信息时，活动数据不存在", humanObj.id, type);
            return;
        }
        
        ControlWuzhiLoveData controlData = (ControlWuzhiLoveData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("玩家{}的无职好感度活动{}数据为空", humanObj.id, type);
            return;
        }
        
        // 构建响应消息
        MsgAct2.act_wuzhi_love_info_s2c.Builder builder = MsgAct2.act_wuzhi_love_info_s2c.newBuilder();
        builder.setActType(type);
        builder.setEventStatus(controlData.eventStatus);
        builder.setSelectedCharId(controlData.selectedCharId);

        // 添加好感度信息
        for (ControlWuzhiLoveData.FavorInfo favorInfo : controlData.favorInfos.values()) {
            Define.p_favor_info.Builder favorBuilder = Define.p_favor_info.newBuilder();
            favorBuilder.setCharId(favorInfo.charId);
            favorBuilder.setLevel(favorInfo.level);
            favorBuilder.setExp(favorInfo.exp);
            favorBuilder.addAllRewardedLv(favorInfo.rewardedLv);
            builder.addFavorInfos(favorBuilder);
        }

        // 任务信息现在由通用任务系统处理，通过sendActivityTaskData发送

        humanObj.sendMsg(builder.build());
    }
    
    /**
     * 处理选择角色请求
     */
    public void on_act_wuzhi_love_select_char_c2s(HumanObject humanObj, int charId) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}选择角色时，活动数据不存在", humanObj.id);
            return;
        }
        
        ControlWuzhiLoveData controlData = (ControlWuzhiLoveData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("玩家{}的活动数据为空", humanObj.id);
            return;
        }
        
        // 验证活动状态
        if (controlData.eventStatus != 1) {
            Log.activity.error("玩家{}选择角色时活动状态错误，当前状态:{}", humanObj.id, controlData.eventStatus);
            return;
        }
        
        // 获取配置验证角色ID
        ConfWuzhiLove conf = getWuzhiLoveConf(humanObj);
        if (conf == null) {
            Log.activity.error("无职好感活动配置不存在，humanId={}", humanObj.id);
            return;
        }

        // 验证角色ID是否有效（通过好感等级表验证）
        List<ConfWuzhiLoveLevel> levelConfs = ConfWuzhiLoveLevel.findBy(ConfWuzhiLoveLevel.K.character_id, charId);
        if (levelConfs.isEmpty()) {
            Log.activity.error("无效的角色ID，charId={}, humanId={}", charId, humanObj.id);
            return;
        }

        // 设置选择的角色
        controlData.selectedCharId = charId;
        controlData.eventStatus = 2; // 进入前置任务阶段

        // 初始化该角色的好感度信息
        ControlWuzhiLoveData.FavorInfo favorInfo = new ControlWuzhiLoveData.FavorInfo(charId);
        controlData.setFavorInfo(charId, favorInfo);

        // 发放选择角色奖励
        if (conf.select_reward != null && conf.select_reward.length > 0) {
            for (int[] reward : conf.select_reward) {
                if (reward.length >= 2) {
                    int itemId = reward[0];
                    int itemNum = reward[1];
                    // TODO: 调用道具发放接口
                    Log.activity.info("发放选择角色奖励，humanId={}, itemId={}, itemNum={}", humanObj.id, itemId, itemNum);
                }
            }
        }
        
        // 更新数据
        data.updateControlData();
        
        // 发送响应
        MsgAct2.act_wuzhi_love_select_char_s2c.Builder builder = MsgAct2.act_wuzhi_love_select_char_s2c.newBuilder();
        builder.setActType(type);
        builder.setCharId(charId);
        humanObj.sendMsg(builder.build());
    }
    
    /**
     * 处理合成手办请求
     */
    public void on_act_wuzhi_love_compose_figure_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}合成手办时，活动数据不存在", humanObj.id);
            return;
        }
        
        ControlWuzhiLoveData controlData = (ControlWuzhiLoveData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("玩家{}的活动数据为空", humanObj.id);
            return;
        }
        
        // 验证活动状态
        if (controlData.eventStatus != 2) {
            Log.activity.error("玩家{}合成手办时活动状态错误，当前状态:{}", humanObj.id, controlData.eventStatus);
            return;
        }
        
        // 检查是否完成所有前置任务（通过通用任务系统检查）
        if (!isAllPreTaskCompleted(humanObj, data)) {
            Log.activity.error("玩家{}合成手办时前置任务未完成", humanObj.id);
            return;
        }
        
        // 获取配置
        ConfWuzhiLove conf = getWuzhiLoveConf(humanObj);
        if (conf == null) {
            Log.activity.error("无职好感活动配置不存在，humanId={}", humanObj.id);
            return;
        }

        // 验证和扣除合成材料
        if (conf.compose_cost != null && conf.compose_cost.length > 0) {
            for (int[] cost : conf.compose_cost) {
                if (cost.length >= 2) {
                    int itemId = cost[0];
                    int itemNum = cost[1];
                    // TODO: 验证道具数量是否足够
                    // TODO: 扣除道具
                    Log.activity.info("扣除合成消耗，humanId={}, itemId={}, itemNum={}", humanObj.id, itemId, itemNum);
                }
            }
        }

        // 发放手办奖励
        int figureId = 1001; // 默认值
        if (conf.compose_reward != null && conf.compose_reward.length > 0) {
            figureId = conf.compose_reward[0]; // 取第一个手办ID
            for (int rewardFigureId : conf.compose_reward) {
                // TODO: 发放手办道具
                Log.activity.info("发放手办奖励，humanId={}, figureId={}", humanObj.id, rewardFigureId);
            }
        }
        
        // 进入正式活动阶段
        controlData.eventStatus = 3;

        // 清除前置任务并添加每日任务
        clearPreTasksAndAddDailyTasks(humanObj, data);
        
        // 更新数据
        data.updateControlData();
        
        // 发送响应
        MsgAct2.act_wuzhi_love_compose_figure_s2c.Builder builder = MsgAct2.act_wuzhi_love_compose_figure_s2c.newBuilder();
        builder.setActType(type);
        builder.setFigureId(figureId);
        humanObj.sendMsg(builder.build());
    }
    
    /**
     * 处理赠送礼物请求
     */
    public void on_act_wuzhi_love_give_gift_c2s(HumanObject humanObj, int charId, List<Define.p_key_value> itemSnNumList) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}赠送礼物时，活动数据不存在", humanObj.id);
            return;
        }
        
        ControlWuzhiLoveData controlData = (ControlWuzhiLoveData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("玩家{}的活动数据为空", humanObj.id);
            return;
        }
        
        // 验证活动状态
        if (controlData.eventStatus != 3) {
            Log.activity.error("玩家{}赠送礼物时活动状态错误，当前状态:{}", humanObj.id, controlData.eventStatus);
            return;
        }
        
        // 验证角色ID
        if (charId != controlData.selectedCharId) {
            Log.activity.error("玩家{}赠送礼物时角色ID错误，选择的角色:{}, 请求的角色:{}", humanObj.id, controlData.selectedCharId, charId);
            return;
        }
        
        ControlWuzhiLoveData.FavorInfo favorInfo = controlData.getFavorInfo(charId);
        if (favorInfo == null) {
            Log.activity.error("玩家{}赠送礼物时找不到角色{}的好感度信息", humanObj.id, charId);
            return;
        }
        
        // 获取配置
        ConfWuzhiLove conf = getWuzhiLoveConf(humanObj);
        if (conf == null) {
            Log.activity.error("无职好感活动配置不存在，humanId={}", humanObj.id);
            return;
        }

        // TODO: 验证玩家是否拥有足够的道具（需要背包系统支持）

        // 计算好感度增加值
        int totalFavorAdd = 0;
        for (Define.p_key_value item : itemSnNumList) {
            int itemId = (int)item.getK();
            int itemNum = (int)item.getV();

            // 从配置表获取礼物的好感度值
            int favorValue = getGiftFavorValue(conf, itemId);
            if (favorValue > 0) {
                totalFavorAdd += itemNum * favorValue;
                Log.activity.info("礼物好感度计算，humanId={}, itemId={}, itemNum={}, favorValue={}",
                    humanObj.id, itemId, itemNum, favorValue);
            } else {
                Log.activity.error("无效的礼物道具，humanId={}, itemId={}", humanObj.id, itemId);
                return;
            }
        }

        // TODO: 检查是否拥有手办增益（需要背包系统支持）

        // TODO: 扣除道具（需要背包系统支持）
        
        // 增加好感度经验
        favorInfo.exp += totalFavorAdd;
        
        // 检查升级
        checkLevelUp(favorInfo);
        
        // 更新数据
        data.updateControlData();
        
        // 发送响应
        MsgAct2.act_wuzhi_love_give_gift_s2c.Builder builder = MsgAct2.act_wuzhi_love_give_gift_s2c.newBuilder();
        builder.setActType(type);
        builder.setCharId(charId);
        builder.setNewLevel(favorInfo.level);
        builder.setNewExp(favorInfo.exp);
        humanObj.sendMsg(builder.build());
    }
    
    /**
     * 处理领取等级奖励请求
     */
    public void on_act_wuzhi_love_claim_level_reward_c2s(HumanObject humanObj, int charId, int level) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}领取等级奖励时，活动数据不存在", humanObj.id);
            return;
        }
        
        ControlWuzhiLoveData controlData = (ControlWuzhiLoveData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("玩家{}的活动数据为空", humanObj.id);
            return;
        }
        
        // 验证活动状态
        if (controlData.eventStatus != 3) {
            Log.activity.error("玩家{}领取等级奖励时活动状态错误，当前状态:{}", humanObj.id, controlData.eventStatus);
            return;
        }
        
        ControlWuzhiLoveData.FavorInfo favorInfo = controlData.getFavorInfo(charId);
        if (favorInfo == null) {
            Log.activity.error("玩家{}领取等级奖励时找不到角色{}的好感度信息", humanObj.id, charId);
            return;
        }
        
        // 检查等级是否达到
        if (favorInfo.level < level) {
            Log.activity.error("玩家{}领取等级奖励时等级不足，当前等级:{}, 请求等级:{}", humanObj.id, favorInfo.level, level);
            return;
        }
        
        // 检查是否已领取
        if (favorInfo.rewardedLv.contains(level)) {
            Log.activity.error("玩家{}重复领取等级{}奖励", humanObj.id, level);
            return;
        }
        
        // 发放等级奖励
        ConfWuzhiLoveLevel levelConf = ConfWuzhiLoveLevel.get(charId, level);
        if (levelConf != null && levelConf.reward != null && levelConf.reward.length > 0) {
            for (int[] reward : levelConf.reward) {
                if (reward.length >= 2) {
                    int itemId = reward[0];
                    int itemNum = reward[1];
                    // TODO: 调用道具发放接口
                    Log.activity.info("发放好感等级奖励，humanId={}, charId={}, level={}, itemId={}, itemNum={}",
                        humanObj.id, charId, level, itemId, itemNum);
                }
            }
        }
        
        // 记录已领取
        favorInfo.rewardedLv.add(level);
        
        // 更新数据
        data.updateControlData();
        
        // 发送响应
        MsgAct2.act_wuzhi_love_claim_level_reward_s2c.Builder builder = MsgAct2.act_wuzhi_love_claim_level_reward_s2c.newBuilder();
        builder.setActType(type);
        builder.setCharId(charId);
        builder.setLevel(level);
        humanObj.sendMsg(builder.build());
    }
    
    /**
     * 检查好感度升级
     */
    private void checkLevelUp(ControlWuzhiLoveData.FavorInfo favorInfo) {
        while (true) {
            int expNeeded = getExpNeeded(favorInfo.charId, favorInfo.level);
            if (expNeeded <= 0 || favorInfo.exp < expNeeded) {
                break; // 没有下一级或经验不足
            }

            favorInfo.exp -= expNeeded;
            favorInfo.level++;
            Log.activity.info("好感度升级，charId={}, newLevel={}, remainExp={}",
                favorInfo.charId, favorInfo.level, favorInfo.exp);
        }
    }

    /**
     * 获取指定等级升级所需经验
     */
    private int getExpNeeded(int charId, int level) {
        ConfWuzhiLoveLevel levelConf = ConfWuzhiLoveLevel.get(charId, level);
        if (levelConf != null) {
            return levelConf.need;
        }
        return 0; // 已达到最高等级
    }
    




    @Override
    public void handleEvent(HumanObject humanObj, int event, Param param) {
        // 处理游戏事件，如任务进度更新等
        // 可以在这里监听各种游戏事件来更新每日任务进度
    }

    @Override
    public void on_act_info_c2s(HumanObject humanObj) {
        on_act_wuzhi_love_info_c2s(humanObj);
    }
}
