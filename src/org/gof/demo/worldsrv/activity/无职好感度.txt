无职联动-好感攻略玩法 (服务器技术案子)
1. 核心数据结构与状态
需要为玩家活动数据创建一个独立的存储结构，包含以下核心字段：
event_status: 活动阶段状态 (1:选择角色, 2:前置任务, 3:正式活动)。
selected_char_id: 玩家选择的角色ID。
pre_task_progress: 前置任务完成进度及道具领取状态。
favor_infos: 一个Map或List，存储每个联动角色的好感度信息，结构如下：
char_id: 角色ID。
level: 当前好感度等级。
exp: 当前等级下的好感度经验值。
rewarded_lv: 已领取奖励的等级列表。
daily_tasks: 当前刷新的每日任务列表及其完成状态。
2. 玩法逻辑实现
2.1. 阶段1: 选择角色 (event_status = 1)
处理选择请求 (act_wuzhi_love_select_char_c2s):
验证 act_type 是否匹配当前活动。
验证 event_status 是否为1。
验证 char_id 是否为本次活动的有效可选角色（查 WuzhiLove_无职好感表）。
若验证通过：
将 selected_char_id 存入玩家活动数据。
根据 WuzhiLove_无职好感表 的 select_reward 字段，向玩家发放初始奖励。
将 event_status 更新为2。
返回 act_wuzhi_love_select_char_s2c 确认选择成功。
2.2. 阶段2: 前置任务 (event_status = 2)
任务逻辑:
前置任务是一次性的，任务链ID配置在 WuzhiLove_无职好感表 的 pre_task 字段,是一个一维数值，走通用逻辑继承。
任务关联 ActivityTask_活动任务
处理合成请求 (act_wuzhi_love_compose_figure_c2s):
验证 act_type 是否匹配。
验证 event_status 是否为2。
检查玩家是否已完成并领取了所有前置任务的奖励。
根据 WuzhiLove_无职好感表 的 compose_cost 字段，验证并扣除所需的4个任务道具。
根据 compose_reward 字段，将对应ID的手办道具发放给玩家。
将 event_status 更新为3。
返回 act_wuzhi_love_compose_figure_s2c，告知客户端合成成功及获得的 figure_id。
2.3. 阶段3: 正式活动 (event_status = 3)
每日任务刷新:前面的前置任务清掉直接变成每日任务
实现一个每日重置逻辑（通常在每日00:00）。
重置时，根据 WuzhiLove_无职好感表 的 daily_task_id 字段配置的任务ID池，和 daily_task_num 字段配置的数量，为玩家随机生成新的每日任务，随机的每日任务要保证全服一样，给一个随机种子用当前时间-当前时间取模1天的时间。
清空前一日未完成或未领取的任务状态，奖励不补发。
处理赠送礼物请求 (act_wuzhi_love_give_gift_c2s):
验证 event_status 是否为3。
验证 char_id 是否为玩家已选择的角色。
遍历 item_sn_num 列表，对每种道具：
验证玩家是否拥有足够数量的该道具。
根据 WuzhiLove_无职好感表 的 gift_id 字段，查找该道具对应的好感度值。
扣除所有有效的赠送道具。
计算总好感度增加值。检查玩家是否拥有 figure 字段关联的手办，若拥有，则对增加值应用增益效果。
将计算后的好感度增加到对应角色的 exp 上。
循环判断 exp 是否满足升级条件（查询 WuzhiLoveLevel_无职好感等级表 的 need 字段），如果满足则 level+1，exp 减去升级所需值，直到不满足升级条件为止。
更新玩家数据中的 level 和 exp。
返回 act_wuzhi_love_give_gift_s2c，下发最新的 new_level 和 new_exp。
处理领取等级奖励请求 (act_wuzhi_love_claim_level_reward_c2s):
验证 event_status 是否为3。
检查 char_id 对应的角色当前 level 是否大于或等于请求领取的 level。
检查请求的 level 是否已在 rewarded_lv 列表中，防止重复领取。
若可领取：
查询 WuzhiLoveLevel_无职好感等级表，根据 level 查找对应的 reward 字段并发放奖励。
将请求的 level 添加到 rewarded_lv 列表中。
返回 act_wuzhi_love_claim_level_reward_s2c 确认成功。
3. 数据查询
活动信息下发 (act_wuzhi_love_info_s2c): 当客户端请求活动信息时，服务器需读取玩家的活动数据，并填充 event_status, selected_char_id, 以及 favor_infos（包含每个角色的等级、经验和已领奖列表）后返回。
4. 协议
code
Protobuf
// 无职联动-好感活动信息请求
message act_wuzhi_love_info_c2s {
    option (msgid) = 6700;
    uint32 act_type = 1;
}

message act_wuzhi_love_info_s2c {
    option (msgid) = 6701;
    uint32 act_type = 1;
    uint32 event_status = 2; // 1:选择角色阶段 2:前置任务阶段 3:正式活动阶段
    uint32 selected_char_id = 3;
    repeated p_favor_info favor_infos = 4;
}

// 无职联动-选择角色
message act_wuzhi_love_select_char_c2s {
    option (msgid) = 6702;
    uint32 act_type = 1;
    uint32 char_id = 2;
}

message act_wuzhi_love_select_char_s2c {
    option (msgid) = 6703;
    uint32 act_type = 1;
    uint32 char_id = 2;
}

// 无职联动-合成手办
message act_wuzhi_love_compose_figure_c2s {
    option (msgid) = 6704;
    uint32 act_type = 1;
}

message act_wuzhi_love_compose_figure_s2c {
    option (msgid) = 6705;
    uint32 act_type = 1;
    uint32 figure_id = 2;
}

// 无职联动-赠送礼物
message act_wuzhi_love_give_gift_c2s {
    option (msgid) = 6706;
    uint32 act_type = 1;
    uint32 char_id = 2;
    repeated p_key_value item_sn_num = 3;
}

message act_wuzhi_love_give_gift_s2c {
    option (msgid) = 6707;
    uint32 act_type = 1;
    uint32 char_id = 2;
    uint32 new_level = 3;
    uint32 new_exp = 4;
}

// 无职联动-领取好感等级奖励
message act_wuzhi_love_claim_level_reward_c2s {
    option (msgid) = 6708;
    uint32 act_type = 1;
    uint32 char_id = 2;
    uint32 level = 3;
}

message act_wuzhi_love_claim_level_reward_s2c {
    option (msgid) = 6709;
    uint32 act_type = 1;
    uint32 char_id = 2;
    uint32 level = 3;
}

message p_favor_info {
    uint32 char_id = 1;
    uint32 level = 2;
    uint32 exp = 3;
    repeated uint32 rewarded_lv = 4;//已经领取的好感度等级
}
数据的存储接口的调用代码规范例子参考ControlCardEliminateData ActivityControlCardEliminate
协议在MsgAct2. message p_ 的协议在Define.