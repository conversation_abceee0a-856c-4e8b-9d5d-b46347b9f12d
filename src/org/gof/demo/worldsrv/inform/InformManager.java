package org.gof.demo.worldsrv.inform;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.*;
import org.gof.core.support.function.GofFunction0;
import org.gof.demo.worldsrv.activity.ActivityControlType;
import org.gof.demo.worldsrv.arena.ArenaManager;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfActivityControl;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.doubleChapter.DoubleChapterManager;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.demo.worldsrv.entity.HumanBrief;
import org.gof.demo.worldsrv.entity.HumanExtInfo;
import org.gof.demo.worldsrv.flyPet.FlyPetManager;
import org.gof.demo.worldsrv.flyPet.hybrid.FlyHybridService;
import org.gof.demo.worldsrv.flyPet.hybrid.FlyHybridServiceProxy;
import org.gof.demo.worldsrv.friend.FriendManager;
import org.gof.demo.worldsrv.guild.GuildData;
import org.gof.demo.worldsrv.guild.GuildServiceProxy;
import org.gof.demo.worldsrv.human.HumanData;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgChat;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.TokenItemType;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.HumanScopeKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;
import org.gof.demo.worldsrv.team.TeamManager;
import org.gof.demo.worldsrv.team.TeamMember;
import org.gof.demo.worldsrv.team.TeamServiceProxy;
import org.gof.demo.worldsrv.test.GMManager;

import java.util.*;

public class InformManager extends ManagerBase {

    /**
     * 获取实例
     *
     * @return
     */
    public static InformManager inst() {
        return inst(InformManager.class);
    }

    public void _msg_chat_message_c2s(HumanObject humanObj, MsgChat.chat_message_c2s msg){
        int channel = msg.getChannel();
        int contentType = msg.getContentType();
        String content = msg.getContent();
        long targetId = msg.getTargetId();
        List<Define.p_link> links = msg.getLinksList();
        List<Define.p_key_value_string> args = msg.getArgsList();
        if (content.startsWith("-gm ")) {
            String gmStr = content.replace("-gm ","");
            GMManager.inst().onGm(humanObj, gmStr);
            return;
        }

        int lv = ConfGlobal.get(ConfGlobalKey.chat_level.SN).value;
        if (lv > humanObj.getHuman().getLevel()) {
            Inform.sendMsg_error(humanObj,75);
            return;
        }
        long silenceEndTime = humanObj.getHuman3().getSilenceEndTime();
        if (silenceEndTime > Port.getTime()) {
            Inform.sendMsg_error(humanObj,74);
            return;
        }

        if (contentType == Inform.ContentTypeEmoji) {
            // 表情还要检测表情包是否解锁
            String[] parts = content.split("_");// 格式是：表情包sn_第几个表情（解锁逻辑是表情包解锁，其下所有表情都能使用）
            int sn = Utils.intValue(parts[0]);
            if (!humanObj.operation.emojiSnList.contains(sn)) {
                Inform.sendMsg_error(humanObj, ErrorTip.ChatEmojiLock);
                return;
            }
        }

        if (channel != Inform.私聊) {
            // 发言冷却
            long interval = ConfGlobal.get(ConfGlobalKey.chat_cd.SN).value * Time.SEC;
            if ((Utils.longValue(humanObj.lastSayTimeMap.get(channel)) + interval) > Port.getTime()) {
                Inform.sendMsg_error(humanObj, 76);
                return;
            }
            // 飞宠相关的分享只能发到私聊
            if (contentType == Inform.ContentTypeLink) {
                for (Define.p_link p : links) {
                    if (p.getType() == Inform.FlyPetRoleUse || p.getType() == Inform.FlyPetPetUse) {
                        Inform.sendMsg_error(humanObj, ErrorTip.FlyPetApplyOnlyPrivateChat);
                        return;
                    }
                }
            }
        }

        long guildId = humanObj.getHuman2().getGuildId();
        int serverId = Utils.getServerIdByHumanId(humanObj.id);
        if(channel == Inform.世界){
            sendMsgChatMessage(HumanScopeKey.ALL, targetId, channel, humanObj.id, serverId, contentType, content, links, args);
        } else if (channel == Inform.私聊) {
            if (contentType == Inform.ContentTypeLink) {
                checkTeamApply(humanObj, links, targetId);

                if(links.stream().anyMatch(p -> p.getType() == Inform.DoubleChapterInviteShare)){
                    long finalTargetId = targetId;
                    DoubleChapterManager.inst().chatInviteShare(humanObj, targetId, res->{
                        int code = Utils.intValue(res.result());
                        if(code == 0){
                            // 记录聊天记录
                            addChathistory(channel, finalTargetId, humanObj.id, humanObj, contentType, content, links, args,"");
                            sendMsgChatMessage(HumanScopeKey.HUMAN, finalTargetId, channel, humanObj.id, serverId, contentType, content, links, args);
                            addChatHumanId(humanObj.id, finalTargetId);// 记录聊天记录人
                            addChatHumanId(finalTargetId, humanObj.id);// 记录聊天记录人
                        }else if(code == LanguageKey.DoubleChapterAlreadyAdd
                                || code == LanguageKey.DoubleChapterLimit
                                || code == LanguageKey.DoubleChapterFullLimit
                                || code == LanguageKey.DoubleChapterAlreadyAdd){
                            Inform.sendMsg_error(humanObj,code);
                        }else {
                            otherChatSelf(humanObj,finalTargetId,Inform.ContentTypeMultiText,String.valueOf(code));
                        }
                    });
                    return;
                }

                boolean isFlyPetLink = false;
                for (Define.p_link p : links) {
                    if (p.getType() == Inform.FlyPetRoleUse || p.getType() == Inform.FlyPetPetUse) {
                        isFlyPetLink = true;
                        break;
                    }
                }
                if (isFlyPetLink) {
                    long targetHumanId = targetId;
                    handleFlyPetLink(humanObj, targetId, links, () -> {
                        // 更新发言时间
                        humanObj.informLastSayTime = Port.getTime();
                        humanObj.lastSayTimeMap.put(channel, Port.getTime());
                        sendMsgChatMessage(HumanScopeKey.HUMAN, targetHumanId, channel, humanObj.id, serverId, contentType, content, links, args);
                        addChatHumanId(humanObj.id, targetHumanId);// 记录聊天记录人
                        addChatHumanId(targetHumanId, humanObj.id);// 记录聊天记录人

                        // 更新发言时间
                        humanObj.informLastSayTime = Port.getTime();
                        humanObj.lastSayTimeMap.put(channel, Port.getTime());

                        if (guildId > 0) {
                            GuildServiceProxy proxy = GuildServiceProxy.newInstance();
                            proxy.getElem(guildId, humanObj.id);
                            proxy.listenResult(this::_result_guild_elem, "humanObj", humanObj, "channel",channel, "targetId", targetHumanId, "contentType", contentType, "content", content, "links", links, "args", args);
                        } else {
                            // 记录聊天记录
                            addChathistory(channel, targetHumanId, humanObj.id, humanObj, contentType, content, links, args,"");
                        }
                    });
                    return;
                }
            }
            sendMsgChatMessage(HumanScopeKey.HUMAN, targetId, channel, humanObj.id, serverId, contentType, content, links, args);
            addChatHumanId(humanObj.id, targetId);// 记录聊天记录人
            addChatHumanId(targetId, humanObj.id);// 记录聊天记录人
        } else if(channel == Inform.帮派){
            // TODO
            targetId = guildId;
            sendMsgChatMessage(HumanScopeKey.UNION, targetId, channel, humanObj.id, serverId, contentType, content, links, args);
            // 进行公会答题
            guildAnswerQuestion(humanObj, content);
        } else if(channel == Inform.本服全区全服){
            sendMsgChatMessage(HumanScopeKey.ZONEALL, targetId, channel, humanObj.id, serverId, contentType, content, links, args);
        }
        // 更新发言时间
        humanObj.informLastSayTime = Port.getTime();
        humanObj.lastSayTimeMap.put(channel, Port.getTime());
        if (guildId > 0) {
            GuildServiceProxy proxy = GuildServiceProxy.newInstance();
            proxy.getElem(guildId, humanObj.id);
            proxy.listenResult(this::_result_guild_elem, "humanObj", humanObj, "channel",channel, "targetId", targetId, "contentType", contentType, "content", content, "links", links, "args", args);
        } else {
            // 记录聊天记录
            addChathistory(channel, targetId, humanObj.id, humanObj, contentType, content, links, args,"");
        }
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_回归任务, TaskConditionTypeKey.TASK_TYPE_96, 1);
    }

    /**
     * 处理飞宠配对相关分享
     */
    private void handleFlyPetLink(HumanObject humanObj, long targetId, List<Define.p_link> links, GofFunction0 successFunc) {
        if (links == null) {
            return;
        }
        Define.p_link link = links.get(0);
        if (link.getType() == Inform.FlyPetRoleUse) {
            FlyHybridServiceProxy prx = FlyHybridServiceProxy.newInstance();
            prx.addFlyPetPartnerAsk(humanObj.getHumanId(), targetId, FriendManager.inst().isFriend(humanObj, targetId), humanObj.getHuman2().getGuildId());
            prx.listenResult((result, context) -> {
                ReasonResult rr = result.get();
                if (!rr.success) {
                    Inform.sendMsg_error(humanObj, rr.code);
                    return;
                }
                successFunc.apply();
            });
        } else if (link.getType() == Inform.FlyPetPetUse) {
            if (!FlyPetManager.inst().isFlyPetPartner(humanObj, targetId)) {
                Inform.sendMsg_error(humanObj, ErrorTip.FlyPetNotPartner);
                return;
            }
            FlyHybridServiceProxy prx = FlyHybridServiceProxy.newInstance();
            prx.addFlyPetPermissionAsk(humanObj.getHumanId(), link.getArgsList(1));
            prx.listenResult((result, context) -> {
                ReasonResult rr = result.get();
                if (!rr.success) {
                    Inform.sendMsg_error(humanObj, rr.code);
                    return;
                }
                successFunc.apply();
            });
        }
    }

    private void addChatHumanId(long sendHumanId, long targetId){
        String str = Utils.getRedisStrValue( RedisKeys.chatHumanLog + sendHumanId);
        if(str == null){
            str = "";
        }
        List<Long> idList = Utils.strToLongList(str);
        // 因为有个数限制，所以先删除再添加
        if(idList.contains(targetId)){
            idList.remove(targetId);
        }
        idList.add(targetId);
        int maxNum = ConfGlobal.get(ConfGlobalKey.chat_records_number.SN).value;
        if(idList.size() > maxNum){
            int num = maxNum - idList.size();
            for(int i = 0; i < num; i++){
                idList.remove(0);
            }
        }
        int day = ConfGlobal.get(ConfGlobalKey.chat_records_time.SN).value;
        int sec = (int)(day * Time.DAY / Time.SEC);
        String newStr = Utils.listToString(idList);
        RedisTools.setAndExpire(EntityManager.redisClient, RedisKeys.chatHumanLog + sendHumanId, newStr, sec);
    }

    private void _result_guild_elem(Param results, Param context) {
        HumanObject humanObj = Utils.getParamValue(context, "humanObj", null);
        if (humanObj == null) {
            return;
        }
        String elem = Utils.getParamValue(results, "elem", "");
        int channel = Utils.getParamValue(context, "channel", 0);
        long targetId = Utils.getParamValue(context, "targetId", 0L);
        int contentType = Utils.getParamValue(context, "contentType", 0);
        String content = Utils.getParamValue(context, "content", "");
        List<Define.p_link> links = Utils.getParamValue(context, "links", null);
        List<Define.p_key_value_string> args = Utils.getParamValue(context, "args", null);
        addChathistory(channel, targetId, humanObj.id, humanObj, contentType, content, links, args, elem);
    }


    private void addChathistory(int channel, long targetId, long sendHumanId, HumanObject humanObj, int contentType,
                                String content, List<Define.p_link> links, List<Define.p_key_value_string> args, String elem) {
        String key = RedisKeys.chatChannelLog + channel;
        if (channel == Inform.世界 || channel == Inform.新闻) {
            key = Utils.createStr("{}_server:{}", key, Utils.getServerIdByHumanId(humanObj.getHuman().getId()));
        } else if(channel == Inform.私聊) {
            if (sendHumanId > targetId) {
                key = Utils.createStr("{}{}{}", key, sendHumanId, targetId);
            } else {
                key = Utils.createStr("{}{}{}", key, targetId, sendHumanId);
            }
        } else if (channel == Inform.帮派) {
            key += humanObj.getHuman2().getGuildId();
        } else if (channel == Inform.本服全区全服) {
        }
        JSONArray ja = Utils.toJSONArray(Utils.getRedisStrValue(key));
        if (ja == null || ja.size() == 0) {
            ja = new JSONArray();
        }
        JSONObject jo = new JSONObject();
        jo.put("links", linksToStr(links));
        jo.put("args", argsToStr(args));
        jo.put("time", Utils.getTimeSec());
        jo.put("contentType", contentType);
        jo.put("content", content);
        jo.put("targetId", targetId);
        jo.put("sendHumanId", sendHumanId);
        jo.put("channel", channel);
        jo.put("elem", elem);
        jo.put("titleSn", humanObj.getHuman().getCurrentTitleSn());
        jo.put("bubble", humanObj.getHumanExtInfo().getCurrentChatBubbleSn());

        ja.add(jo);
        int maxNum = ConfGlobal.get(ConfGlobalKey.chat_records_number.SN).value;
        if (ja.size() > maxNum) {
            int num = maxNum - jo.size();
            for (int i = 0; i < num; i++) {
                ja.remove(0);
            }
        }

        int day = ConfGlobal.get(ConfGlobalKey.chat_records_time.SN).value;
        int sec = (int)(day * Time.DAY / Time.SEC);
        RedisTools.setAndExpire(EntityManager.redisClient, key, ja.toJSONString(), sec);

    }

    /**
     * 批量添加聊天记录（不要一次加一条，原来新闻播报一次触发多条可能会导致消息丢失，因为Utils.getRedisStrValue并不一定能取到最新结果）
     */
    public void addNewsChatHistory(int serverId, int contentType, List<String> contentList, List<Define.p_key_value_string> argsList) {
        String key = Utils.createStr("{}_server:{}", (RedisKeys.chatChannelLog + Inform.新闻), serverId);
        JSONArray ja = Utils.toJSONArray(Utils.getRedisStrValue(key));
        if (ja == null || ja.size() == 0) {
            ja = new JSONArray();
        }
        for (String content : contentList) {
            JSONObject jo = new JSONObject();
            jo.put("time", Utils.getTimeSec());
            jo.put("contentType", contentType);
            jo.put("content", content);
            jo.put("targetId", 0);
            jo.put("sendHumanId", 1);
            jo.put("channel", Inform.新闻);
            JSONArray argsJA = new JSONArray();
            for (Define.p_key_value_string p : argsList) {
                JSONObject argsJO = new JSONObject();
                jo.put("id", p.getK());
                jo.put("num", p.getV());
                argsJA.add(argsJO);
            }
            jo.put("elem", argsJA.toJSONString());
            ja.add(jo);
        }
        int maxNum = ConfGlobal.get(ConfGlobalKey.chat_records_number.SN).value;
        if (ja.size() > maxNum) {
            int num = ja.size() - maxNum;
            for (int i = 0; i < num; i++) {
                ja.remove(0);
            }
        }

        int day = ConfGlobal.get(ConfGlobalKey.chat_records_time.SN).value;
        int sec = (int)(day * Time.DAY / Time.SEC);
        RedisTools.setAndExpire(EntityManager.redisClient, key, ja.toJSONString(), sec);
    }

    private String argsToStr(List<Define.p_key_value_string> args) {
        if (args == null || args.size() == 0) {
            return "";
        }
        Map<Integer, String> map = new HashMap<>();
        for (Define.p_key_value_string p : args) {
            map.put(p.getK(), p.getS());
        }
        return Utils.mapIntStrToJSON(map);
    }

    private String linksToStr(List<Define.p_link> links) {
        if (links == null || links.size() == 0) {
            return "";
        }
        JSONArray ja = new JSONArray();
        for (Define.p_link p : links) {
            JSONObject jo = new JSONObject();
            jo.put("type", p.getType());
            jo.put("pos", p.getPos());
            jo.put("args", Utils.listToString(p.getArgsListList()));
            jo.put("strs", p.getStringListList());
            ja.add(jo);
        }
        return ja.toJSONString();
    }

    private void checkTeamApply(HumanObject humanObj, List<Define.p_link> links, long targetId){
        if(links == null){
            return;
        }
        long teamId = humanObj.getHuman2().getTeamId();
        if(teamId <= 0){
            return;
        }
        boolean isApply = false;
        for(Define.p_link p : links){
            if(p.getType() == Inform.MakeTeam){
                TeamServiceProxy proxy = TeamServiceProxy.newInstance();
                proxy.addApplyId(teamId, humanObj.id, targetId);
                isApply = true;
            }
        }
        if(isApply){
            TeamManager.inst()._msg_team_apply_c2s(humanObj, 0);
        }
    }

    /**
     * 验证发言是否冷却
     *
     * @param humanObject
     * @return
     */
    private boolean isCooldown(HumanObject humanObject) {
        if (humanObject.informLastSayTime == 0) return true;
        //发言间隔
        long interval = ConfGlobal.get(ConfGlobalKey.chat_cd.SN).value * Time.SEC;

        return (humanObject.informLastSayTime + interval) < Port.getTime();
    }

    public void sendMsgChatMessage(HumanScopeKey key, long targetId, int channel, long sendHumanId, int serverId, int contentType,
                        String content, List<Define.p_link> links, List<Define.p_key_value_string> args){
        List<Long> list = new ArrayList<>();
        if (targetId != 0) list.add(targetId);
        Param param = new Param();
        param.put("key", key);
        param.put("targetId", targetId);
        param.put("channel", channel);
        param.put("sendHumanId", sendHumanId);
        param.put("serverId", serverId);
        param.put("contentType", contentType);
        param.put("content", content);
        param.put("links", links);
        param.put("args", args);
        param.put("list", list);

        HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
        prx.sendMsgChatMessage(param);
    }


    // ==================================TODO 红包========================================

    public void sendCreateRed(HumanObject humanObj, long redId, int humanNum){
        // int zone, int sumNum, int num, p_brief_red sendInfo, String param
        int zone = humanObj.getHuman().getServerId();
        int sumNum = 336;// TODO
        ChatServiceProxy proxy = ChatServiceProxy.newInstance();
        proxy.createRed(zone, sumNum, humanNum, to_p_brief_red(humanObj, redId), "");

    }

    public void lotteryRed(HumanObject humanObj, long redId){
        ChatServiceProxy proxy = ChatServiceProxy.newInstance();
        proxy.lotteryRed(redId, to_p_brief_red(humanObj, redId));
        proxy.listenResult(this::_result_lotteryRed, "humanObj", humanObj);
    }

    private Define.p_brief_red to_p_brief_red(HumanObject humanObj, long redId){
        Define.p_brief_red.Builder dInfo = Define.p_brief_red.newBuilder();
        dInfo.setId(redId);
        dInfo.setCfgId(0);
        dInfo.setState(1);
        dInfo.setRoleId(humanObj.id);
        dInfo.setName(humanObj.name);
        dInfo.setExpiryTime(Utils.getTimeSec());

        return dInfo.build();
    }

    private void _result_lotteryRed(Param results, Param context){
        int addNum = Utils.getParamValue(results,"addNum", 0);
        if(addNum <= 0){
            Log.temp.error("===addNum={}", addNum);
            return;
        }
        HumanObject humanObj = Utils.getParamValue(context, "humanObj", null);
        if(humanObj == null){
            return;
        }
        ProduceManager.inst().produceAdd(humanObj, TokenItemType.GOLD, addNum, MoneyItemLogKey.红包);
        RedInfo redInfo = Utils.getParamValue(context, "redInfo", null);
        if(redInfo == null){
            return;
        }
        // TODO 通知

    }


    public void sendMsg_red_all_list_s2c(HumanObject humanObj){
        ChatServiceProxy proxy = ChatServiceProxy.newInstance();
        proxy.allSendMsg(humanObj.getHuman().getServerId());
        proxy.listenResult(this::_result_allSendMsg, "humanObj", humanObj);
    }

    private void _result_allSendMsg(Param results, Param context) {
        HumanObject humanObj = Utils.getParamValue(context, "humanObj", null);
        if (humanObj == null) {
            return;
        }

    }

    public void sendMsg_chat_message_s2c(HumanObject humanObj, int channel, long targetId, Define.p_chat p_chat){
        MsgChat.chat_message_s2c.Builder msg = MsgChat.chat_message_s2c.newBuilder();
        msg.setChannel(channel);
        msg.setTargetId(targetId);
        msg.setChatInfo(p_chat);
        humanObj.sendMsg(msg);
    }

    public void _msg_chat_channel_list_c2s(HumanObject humanObj) {
        MsgChat.chat_channel_list_s2c.Builder msg = MsgChat.chat_channel_list_s2c.newBuilder();
        // 不用包含1、2、3、5
        Set<Integer> openActivitySnList = humanObj.openActivitySnList;
        for(int sn : openActivitySnList){
            ConfActivityControl conf = ConfActivityControl.get(sn);
            if(conf == null){
                Log.temp.error("===ConfActivityControl is null, sn={}", sn);
                continue;
            }
            if(conf.type == ActivityControlType.Act_33){
                msg.addChatChannelList(Inform.跨服);
                break;
            }
        }
        humanObj.sendMsg(msg);
    }

    public void _msg_chat_history_c2s(HumanObject humanObj, int channel, long targetId) {
        String key = RedisKeys.chatChannelLog + channel;
        if (channel == Inform.世界 || channel == Inform.新闻) {
            key = Utils.createStr("{}_server:{}", key, Utils.getServerIdByHumanId(humanObj.getHuman().getId()));
        } else if (channel == Inform.私聊) {//
            if (humanObj.id > targetId) {
                key = Utils.createStr("{}{}{}", key, humanObj.id, targetId);
            } else {
                key = Utils.createStr("{}{}{}", key, targetId, humanObj.id);
            }
        } else if (channel == Inform.帮派) {
            key += humanObj.getHuman2().getGuildId();
        } else if (channel == Inform.本服全区全服){
        }

        Port port = Port.getCurrent();
        RedisTools.get(EntityManager.redisClient, key, ret -> {
            if (ret.failed()) {
                Log.chat.error("===获取聊天记录失败, channel={}， cause={}", channel, ret.cause());
                return;
            }
            JSONArray ja = Utils.toJSONArray(ret.result());
            if (ja == null || ja.size() == 0) {
                MsgChat.chat_history_s2c.Builder msg = MsgChat.chat_history_s2c.newBuilder();
                msg.setChannel(channel);
                msg.setTargetId(targetId);
                port.doAction(()-> {
                    humanObj.sendMsg(msg);
                });
                return;
            }
            List<Long> blockIdList = Utils.strToLongList(humanObj.operation.friend.getBlackList());

            List<Long> humanIdList = new ArrayList<>();
            for(int i = 0; i < ja.size(); i++){
                JSONObject jo = ja.getJSONObject(i);
                long sendHumanId = jo.getLongValue("sendHumanId");
                if (sendHumanId == 1) {
                    continue;
                }
                humanIdList.add(sendHumanId);
            }
            if(!humanIdList.contains(humanObj.id)){
                humanIdList.add(humanObj.id);
            }

            HumanData.getList(humanIdList, HumanManager.inst().humanClasses, res-> {
                if (res.failed()) {
                    Log.human.error("MemberCallback getMemberAsync error", res.cause());
                    return;
                }
                List<HumanData> humanDataList = res.result();
                Map<Long, HumanData> humanDataMap = new HashMap<>();
                for(HumanData data : humanDataList){
                    humanDataMap.put(data.human.getId(), data);
                }
                MsgChat.chat_history_s2c.Builder msg = MsgChat.chat_history_s2c.newBuilder();
                msg.setChannel(channel);
                msg.setTargetId(targetId);
                msg.addAllChatHistory(to_p_chat_List(humanObj, humanDataMap, ja, blockIdList));
                if(targetId == 0){
                    humanObj.sendMsg(msg);
                    return;
                }
                HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
                // 我是否被对方拉黑
                proxy.isInBlockList(targetId, humanObj.id);
                proxy.listenResult((results, context) -> {
                    boolean isBlock = Utils.getParamValue(results, "isBlock", false);
                    msg.setIsBlock(isBlock ? 1 : 0);
                    humanObj.sendMsg(msg);
                });
            });
        });
    }

    private  List<Define.p_chat> to_p_chat_List(HumanObject humanObj, Map<Long, HumanData> humanDataMap, JSONArray ja, List<Long> blockIdList) {
        List<Define.p_chat> chatList = new ArrayList<>();
        for (int i = 0; i < ja.size(); i++) {
            JSONObject jo = ja.getJSONObject(i);
            long sendHumanId = jo.getLongValue("sendHumanId");
            if (blockIdList.contains(sendHumanId) && sendHumanId != 1) {
                // 拉黑的人说的话，直接不显示
                continue;
            }
            TeamMember memberSend = null;
            if (sendHumanId != 1) {
                HumanData dataSend = humanDataMap.get(sendHumanId);
                if (dataSend == null) {
                    continue;
                }
                memberSend = new TeamMember(dataSend);
            }
            String str = jo.getString("content");
            int time = jo.getIntValue("time");
            Define.p_chat.Builder p_chat = Define.p_chat.newBuilder();
            p_chat.setRoleId(sendHumanId);
            p_chat.setGender(1);//性别无
            p_chat.setType(jo.getIntValue("contentType"));
            p_chat.setContent(str);
            p_chat.setTime(time);
            if (sendHumanId == 1) {
                p_chat.setName("");
                p_chat.setServerId(Config.SERVER_ID);
                p_chat.setHead(Define.p_head.newBuilder().setId(1).build());
            } else {
                p_chat.setName(memberSend.human.getName());
                p_chat.setServerId(memberSend.human.getServerId());
                if (memberSend.p_battle != null) {
                    p_chat.setHead(memberSend.p_battle.getHead());
                }
            }
            // 是否过滤这条聊天消息
            boolean isRemove = false;
            JSONArray jalinks = Utils.toJSONArray(jo.getString("links"));
            if(jalinks != null){
                for(int j = 0; j < jalinks.size(); j++){
                    JSONObject jalink = jalinks.getJSONObject(j);
                    Define.p_link.Builder p_link = Define.p_link.newBuilder();
                    p_link.setType(jalink.getIntValue("type"));
                    p_link.setPos(jalink.getIntValue("pos"));
                    if(jalink.getIntValue("type") == Inform.ContentTypeLink && sendHumanId != humanObj.id){// 组队邀请
                        Map<Integer, Long> settingMap = Utils.jsonToMapIntLong(humanObj.getHumanExtInfo().getSettingMap());
                        long isValue = settingMap.getOrDefault(10003, 0L); // Setting表配置
                        if(isValue == 1){// 屏蔽过滤
                            isRemove = true;
                            break;
                        }
                    }
                    List<Long> argsIdList = Utils.strToLongList(jalink.getString("args"));
                    p_link.addAllArgsList(argsIdList);
                    List<String> stringList = (List<String>)jalink.get("strs");
                    if(stringList != null) {
                        p_link.addAllStringList(stringList);
                    }
                    p_chat.addLinks(p_link.build());
                }
            }
            if(isRemove){// 策划要求不显示
                continue;
            }
            JSONArray elemJa = Utils.toJSONArray(jo.getString("elem"));
            if(elemJa != null){
                for(int j = 0; j < elemJa.size(); j++) {
                    JSONObject elemJo = elemJa.getJSONObject(j);
                    Define.p_chat_elem.Builder p_chat_elem = Define.p_chat_elem.newBuilder();
                    p_chat_elem.setField(elemJo.getIntValue("id"));
                    p_chat_elem.setNum(elemJo.getLongValue("num"));
                    p_chat.addExtList(p_chat_elem.build());
                }
            }
            Define.p_chat_elem.Builder p_chat_elem = Define.p_chat_elem.newBuilder();
            p_chat_elem.setField(ParamKey.elem_id_5);
            if(jo.containsKey("titleSn")){
                p_chat_elem.setNum(jo.getIntValue("titleSn"));
            } else if (sendHumanId != 1) {
                p_chat_elem.setNum(Utils.intValue(memberSend.param.get("ct")));
            }
            p_chat.addExtList(p_chat_elem.build());

            Define.p_chat_elem.Builder p_chat_elem7 = Define.p_chat_elem.newBuilder();
            p_chat_elem7.setField(ParamKey.elem_id_7);
            if(jo.containsKey("bubble")){
                p_chat_elem7.setNum(jo.getIntValue("bubble"));
            } else if (sendHumanId != 1) {
                p_chat_elem7.setNum(Utils.intValue(memberSend.param.get("bu")));
            }
            p_chat.addExtList(p_chat_elem7.build());

            chatList.add(p_chat.build());
        }

        return chatList;
    }

    public void _msg_chat_friend_list_c2s(HumanObject humanObj) {
        Port port = Port.getCurrent();
        RedisTools.get(EntityManager.redisClient, RedisKeys.chatHumanLog + humanObj.id, ret -> {
            if(ret.failed()){
                Log.friend.error("===获取聊天记录失败, humanId={}， cause={}", humanObj.id, ret.cause());
                return;
            }
            List<Long> idList = Utils.strToLongList(ret.result());
            if(idList.isEmpty()){
                MsgChat.chat_friend_info_list_s2c.Builder msg = MsgChat.chat_friend_info_list_s2c.newBuilder();
                port.doAction(()-> {
                    humanObj.sendMsg(msg);
                });
                return;
            }
            port.doAction(()-> {
                _msg_chat_friend_info_list_c2s(humanObj, idList);
            });
        });

    }


    public void _msg_chat_friend_info_list_c2s(HumanObject humanObj, List<Long> idList) {
        if(idList.isEmpty()){
            _msg_chat_friend_list_c2s(humanObj);
            return;
        }
        HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
        prx.getOnLineHumanList(idList);
        prx.listenResult(this::_result_getOnLineHumanList, "humanObj", humanObj, "idList", idList);
    }
    private void _result_getOnLineHumanList(Param results, Param context) {
        MsgChat.chat_friend_info_list_s2c.Builder msg = MsgChat.chat_friend_info_list_s2c.newBuilder();
        HumanObject humanObj = Utils.getParamValue(context, "humanObj", null);
        if (humanObj == null) {
            Log.temp.error("humanObj is null");
            return;
        }
        List<Long> idList = Utils.getParamValue(context, "idList", null);
        if (idList == null || idList.isEmpty()) {
            Log.temp.error("idList is null");
            humanObj.sendMsg(msg);
            return;
        }
        List<Long> allIdList = new ArrayList<>(idList);
        if(!allIdList.contains(humanObj.id)){
            allIdList.add(humanObj.id);
        }
        List<Long> onlineList = results.get();
        if (onlineList == null) {
            Log.temp.error("onlineList is null");
            humanObj.sendMsg(msg);
            return;
        }
        List<Long> blockIdList = Utils.strToLongList(humanObj.operation.friend.getBlackList());

        Port port = Port.getCurrent();
        HumanData.getList(allIdList, HumanManager.inst().humanClasses, res-> {
            if (res.failed()) {
                Log.chat.error("私聊记录拉取玩家失败。idList={}", allIdList, res.cause());
                return;
            }
            List<HumanData> humanDataList = res.result();
            Map<Long, HumanData> humanDataMap = new HashMap<>();
            for(HumanData data : humanDataList){
                humanDataMap.put(data.human.getId(), data);
            }
            for(long id : idList){
                if(blockIdList.contains(id)){
                    continue;
                }
                Define.p_chat_friend friend = to_p_chat_friend(humanObj, id, onlineList.contains(id), humanObj.id, blockIdList, humanDataMap);
                if(friend == null){
                    Log.chat.error("to_p_chat_friend is null, id={}", id);
                    continue;
                }
                msg.addFriendInfoList(friend);
            }
            port.doAction(()-> {
                humanObj.sendMsg(msg);
            });
        });
    }

    private Define.p_chat_friend to_p_chat_friend(HumanObject humanObj, long id, boolean online, long humanId, List<Long> blockIdList, Map<Long, HumanData> humanDataMap){
        HumanData data = humanDataMap.get(id);
        if(data == null || data.human == null){
            return null;
        }
        Define.p_chat_friend.Builder dInfo = Define.p_chat_friend.newBuilder();
        dInfo.setRoleId(id);
        dInfo.setName(data.human.getName());
        dInfo.setIsOnline(online ? 1 : 0);

        Define.p_head.Builder head = Define.p_head.newBuilder();
        head.setId(data.human.getHeadSn());
        head.setFrameId(data.human.getCurrentHeadFrameSn());
        head.setUrl("");
        dInfo.setHead(head);
        String key = RedisKeys.chatChannelLog + Inform.私聊;
        if(humanId > id){
            key = Utils.createStr("{}{}{}", key, humanId, id);
        } else {
            key = Utils.createStr("{}{}{}", key, id, humanId);
        }
        JSONArray ja = Utils.toJSONArray(Utils.getRedisStrValue(key));
        List<Define.p_chat> chatList = to_p_chat_List(humanObj, humanDataMap, ja, blockIdList);
        if(chatList != null && !chatList.isEmpty()){
            dInfo.setChatInfo(chatList.get(chatList.size() - 1));
        }
        String unreadKey = Utils.createStr("{}{}{}", RedisKeys.chat2HumanUnread, humanId, id);
        dInfo.setNum(Utils.intValue(Utils.getRedisStrValue(unreadKey)));
        return dInfo.build();
    }

    public void _msg_ban_chat_c2s(HumanObject humanObj) {
        MsgChat.ban_chat_s2c.Builder msg = MsgChat.ban_chat_s2c.newBuilder();
        HumanExtInfo extInfo = humanObj.getHumanExtInfo();
        Map<Long, Long> idBlockTimeMap = Utils.jsonToMapLongLong(extInfo.getIdBlockTimeMap());
        for(Map.Entry<Long, Long> entry : idBlockTimeMap.entrySet()) {
            long id = entry.getKey();
            long blockTime = entry.getValue();
            msg.addRoleId(HumanManager.inst().to_p_key_value(id, blockTime));
        }
        humanObj.sendMsg(msg);
    }

    public void _msg_chat_history_read_c2s(HumanObject humanObj, long targetId) {

        int day = ConfGlobal.get(ConfGlobalKey.chat_records_time.SN).value;
        int sec = (int)(day * Time.DAY / Time.SEC);
        String key = Utils.createStr("{}{}{}", RedisKeys.chat2HumanUnread, humanObj.id, targetId);
        RedisTools.setAndExpire(EntityManager.redisClient, key, String.valueOf(0), sec);

        sendMsg_chat_history_read_s2c(humanObj, targetId);
    }

    private void sendMsg_chat_history_read_s2c(HumanObject humanObj, long targetId){
        MsgChat.chat_history_read_s2c.Builder msg = MsgChat.chat_history_read_s2c.newBuilder();
        msg.setTargetId(targetId);
        humanObj.sendMsg(msg);
    }

    /**
     * 进行公会答题
     * @param humanObj
     * @param answer
     */
    private void guildAnswerQuestion(HumanObject humanObj, String answer){
        if(!GuildData.ANSWER_TYPE.contains(answer)){
            return;
        }
        long guildId = humanObj.getHuman2().getGuildId();
        if (guildId <= 0) {
            Log.guild.warn("guildAnswerQuestion guildId <= 0");
            return;
        }
        GuildServiceProxy proxy = GuildServiceProxy.newInstance();
        if(proxy == null){
            return;
        }
        proxy.guildAnswerQuestion(guildId, humanObj.id, answer);
        proxy.listenResult(this::_result_guild_answer_question, "humanObj", humanObj);
    }

    private void _result_guild_answer_question(Param results, Param context) {
        HumanObject humanObj = Utils.getParamValue(context, "humanObj", null);
        if (humanObj == null) {
            return;
        }
        boolean result = Utils.getParamValue(results, "result", false);
        if(!result){
            return;
        }
        int[][] rewards = Utils.parseIntArray2(ConfGlobal.get(ConfGlobalKey.quiz_answer_reward.SN).strValue);
        if(rewards == null || rewards.length == 0){
            Log.guild.warn("趣味竞答答题奖励配置错误");
            return;
        }
        ProduceManager.inst().produceAdd(humanObj, rewards, MoneyItemLogKey.趣味竞答答题);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewards);
    }

    public void otherChatSelf(HumanObject humanObj, long otherId, int contentType, String content) {
        HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
        proxy.getHumanBrief2(otherId);
        proxy.listenResult(this::_result_otherChatSelf, "humanObj", humanObj, "contentType",contentType ,"content", content);
    }

    private void _result_otherChatSelf(Param results, Param context) {
        HumanObject humanObj = context.get("humanObj");
        HumanBrief brief = results.get("humanBrief");
        int contentType = context.get("contentType");
        String content = context.get("content");
        if(brief == null) {
            return;
        }

        MsgChat.chat_message_s2c.Builder msg = MsgChat.chat_message_s2c.newBuilder();
        msg.setChannel(Inform.私聊);
        msg.setTargetId(brief.getId());
        Define.p_chat.Builder dInfo = Define.p_chat.newBuilder();
        dInfo.setRoleId(brief.getId());
        Define.p_head.Builder dHead = Define.p_head.newBuilder();
        // TODO
        dHead.setId(brief.getHeadSn());
        dHead.setFrameId(brief.getCurrentHeadFrameSn());
        dHead.setUrl("");
        dInfo.setHead(dHead);

        dInfo.setName(brief.getName());
        dInfo.setGender(0);
        dInfo.setContent(content);
        dInfo.setServerId(brief.getServerId());
        dInfo.setTime(Utils.getTimeSec());
        dInfo.setType(contentType);
        msg.setChatInfo(dInfo);

        Define.p_chat_elem.Builder dElemTitle = Define.p_chat_elem.newBuilder();
        dElemTitle.setField(ParamKey.elem_id_5);
        dElemTitle.setNum(0);
        dInfo.addExtList(dElemTitle);
        Define.p_chat_elem.Builder dElemBubble = Define.p_chat_elem.newBuilder();
        dElemBubble.setField(ParamKey.elem_id_7);
        dElemBubble.setNum(0);
        dInfo.addExtList(dElemBubble);
        humanObj.sendMsg(msg);
    }
}