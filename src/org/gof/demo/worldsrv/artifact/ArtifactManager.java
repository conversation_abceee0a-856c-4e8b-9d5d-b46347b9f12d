package org.gof.demo.worldsrv.artifact;

import org.gof.core.support.ManagerBase;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.observer.Listener;
import org.gof.demo.battlesrv.manager.CombatChangeLog;
import org.gof.demo.battlesrv.manager.PropManager;
import org.gof.demo.battlesrv.msgHandler.SkillManager;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.charm.CharmManager;
import org.gof.demo.worldsrv.common.GameServiceProxy;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.human.*;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.item.ItemConstants;
import org.gof.demo.worldsrv.item.ItemManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgArtifact;
import org.gof.demo.worldsrv.msg.MsgArtifactGem;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.produce.ProduceVo;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.StringZipUtils;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.enumKey.NewsConditionTypeKey;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;

import java.math.BigDecimal;
import java.util.*;

public class ArtifactManager extends ManagerBase {

    /**
     * 获取实例
     *
     * @return
     */
    public static ArtifactManager inst() {
        return inst(ArtifactManager.class);
    }

    public static final int GEM_INDEX_MIN = 1;
    public static final int GEM_INDEX_MAX = 6;

    public void init(HumanObject humanObj) {
        Artifact artifact = humanObj.artifact.atf;
        if(artifact == null){
            return;
        }
        String gemBagStr = artifact.getGemBagMap();
        if(artifact.getIsZip()==1){
            gemBagStr = StringZipUtils.unzip(gemBagStr);
        }
        humanObj.artifact.gemBagMap = ArtifactGemVo.fromMapJsonString(gemBagStr);

        //初始化宝石ID生成器是gemMap中最大的ID+1
        long idGen = Integer.MAX_VALUE;
        for (Map.Entry<Long, ArtifactGemVo> entry : humanObj.artifact.gemBagMap.entrySet()) {
            if (entry.getKey() > idGen) {
                idGen = entry.getKey();
            }
        }
        humanObj.artifact.setIdGen(idGen + 1);
    }

    public void saveGemBag(HumanObject humanObj) {
        Artifact artifact = humanObj.artifact.atf;
        String gemBagStr = ArtifactGemVo.mapJsonString(humanObj.artifact.gemBagMap);
        if(gemBagStr.length()>1000){
            gemBagStr = StringZipUtils.zip(gemBagStr);
            artifact.setIsZip(1);
        }else if(artifact.getIsZip()==1){
            artifact.setIsZip(0);
        }
        artifact.setGemBagMap(gemBagStr);
    }
    public int bagSize(HumanObject humanObj) {
        return humanObj.artifact.gemBagMap.size();
    }
    public void produceGem(HumanObject humanObj, List<ProduceVo> voList, MoneyItemLogKey log) {
        if(!humanObj.isModUnlock(47)){
            return;
        }
        if((ArtifactManager.inst().bagSize(humanObj)) >= ConfGlobal.get(202).value){
            Inform.sendMsg_error(humanObj,504);
            return;
        }
        Human human = humanObj.getHuman();
        MsgArtifactGem.artifact_gem_add_s2c.Builder msg = MsgArtifactGem.artifact_gem_add_s2c.newBuilder();
        for(ProduceVo vo : voList){
            if(vo.type != ItemConstants.符石){
                Log.temp.error("===类型不对， humanId={}, vo={}, voList={}, log={}", humanObj.id, vo, voList, log);
                continue;
            }
            Object obj = vo.objList.get(0);
            if(!(obj instanceof ArtifactGemVo)){
                Log.temp.error("===对象不对， humanId={}, vo={}, voList={}, log={}, obj={}", humanObj.id, vo, voList, log, obj);
                continue;
            }
            ArtifactGemVo gem = (ArtifactGemVo)obj;
            Define.p_artifact_gem.Builder gemInfo = gem.toProtobuf();
            msg.addGemList(gemInfo);
            humanObj.artifact.gemBagMap.put(gem.id, gem);

            com.pwrd.op.LogOp.log("addGem",
                    human.getId(),
                    org.gof.core.Port.getTime(),
                    Utils.formatTime(org.gof.core.Port.getTime(), "yyyy-MM-dd"),
                    human.getAccount(),
                    human.getName(),
                    human.getLevel(),
                    human.getServerId(),
                    gem.id,
                    gem.lv,
                    gem.quality + "_" + gem.pos + "_" + gem.suit,
                    1
            );
        }
        saveGemBag(humanObj);
        humanObj.sendMsg(msg);
    }

    /**
     * 属性生成
     * @param groupId
     * @param excludeIds 排除在外的id
     */
    public ConfArtifactGemattr genAttrConf(int groupId, List<Integer> excludeIds) {
        List<Integer> confGemattrList = GlobalConfVal.getConfGroupGemattrSnList(groupId);

        if(confGemattrList == null || confGemattrList.size() <= excludeIds.size()){
            Log.game.error("神器宝石属性配置错误，group_id={}", groupId);
            return null;
        }
        int[] weights = new int[confGemattrList.size()];
        for (int i = 0; i < confGemattrList.size(); i++) {
            int sn = confGemattrList.get(i);
            if(excludeIds != null && excludeIds.contains(sn)){
                weights[i] = 0;
                continue;
            }
            weights[i] = ConfArtifactGemattr.get(sn).pro;
        }
        int randAttrIndex = Utils.randomByWeight(weights);
        if(randAttrIndex >= 0 && randAttrIndex < confGemattrList.size()){
            return ConfArtifactGemattr.get(confGemattrList.get(randAttrIndex));
        }
        return null;
    }

    /**
     * 神器信息
     */
    public void handleArtifactInfoC2S(HumanObject humanObj) {
        Artifact artifact = humanObj.artifact.atf;
        if(artifact == null){
            humanObj.sendMsg(MsgArtifact.artifact_info_s2c.newBuilder().setUse(0).setExp(0).setLevel(0).setSkillUse(0));
            return;
        }
        MsgArtifact.artifact_info_s2c.Builder msg = MsgArtifact.artifact_info_s2c.newBuilder();
        msg.setLevel(artifact.getLevel());
        msg.setExp(artifact.getExp());
        msg.setUse(humanObj.getHuman2().getArtifactUse());
        msg.setSkillUse(getArtifactSn(humanObj.getHuman2().getArtifactSkillSnLv()));

        Map<Long, Long> skinMap = Utils.jsonToMapLongLong(artifact.getSkinLvMap());

        Define.p_key_value.Builder p = Define.p_key_value.newBuilder();
        for (Map.Entry<Long, Long> entry : skinMap.entrySet()) {
            p.setK(entry.getKey());
            p.setV(entry.getValue());
            msg.addSkinList(p);
        }

        humanObj.sendMsg(msg);
    }

    /**
     * 神器升级
     */
    public void handleArtifactLevupC2S(HumanObject humanObj, int type) {
        Artifact artifact = humanObj.artifact.atf;
        if(artifact == null){
            return;
        }

        ConfArtifactLevel confLevelNext = ConfArtifactLevel.get(artifact.getLevel()+1);
        if(confLevelNext == null){
            Log.game.error("神器等级配置错误或者已经最高等级，等级={}", artifact.getLevel());
            return;
        }

        int addExp = 0;
        if(type == 0){
            addExp = confLevelNext.expendGoods[1];
            //锤炼一次

        }else {
            //一键锤炼
            int itemNum = ItemManager.inst().getItemNum(humanObj, confLevelNext.expendGoods[0]);
            int needNum = confLevelNext.expendExp - artifact.getExp();
            addExp = itemNum < needNum ? itemNum : needNum;
        }
        if(addExp == 0){
            return;
        }
        ReasonResult reason = ProduceManager.inst().checkAndCostItem(humanObj, confLevelNext.expendGoods[0], addExp, MoneyItemLogKey.神器升级);
        if(!reason.success){
            return;
        }

        int exp = artifact.getExp()+addExp;
        artifact.setExp(exp);
        if(exp>=confLevelNext.expendExp) {
            artifact.setLevel(confLevelNext.sn);
            updatePorpCalcPower(humanObj);
            if(confLevelNext.unlock > 0){
                Map<Long, Long> skinMap = Utils.jsonToMapLongLong(artifact.getSkinLvMap());
                skinMap.put((long)confLevelNext.unlock, 0L);
                artifact.setSkinLvMap(Utils.mapLongLongToJSON(skinMap));
                humanObj.getHuman2().setArtifactUse(confLevelNext.unlock);
                humanObj.getHuman().update();
                HumanManager.inst().checkCrossSyns(humanObj, HumanBrief.K.roleFigure, HumanBrief.K.battleRole);
                // 重新计算美观值
                CharmManager.reCalcCharmValue(humanObj, true);
            }
            ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_66, 0);
        }
        //发送消息
        MsgArtifact.artifact_levup_s2c.Builder msg = MsgArtifact.artifact_levup_s2c.newBuilder();
        msg.setExp(artifact.getExp());
        humanObj.sendMsg(msg);

        handleArtifactInfoC2S(humanObj);
    }

    public void updatePorpCalcPower(HumanObject humanObj){
        PropCalc propCalc = new PropCalc();
        int power = 0;
        Artifact atf = humanObj.artifact.atf;
        if(atf == null){
            return;
        }
        //神器属性
        ConfArtifactLevel confLevel = ConfArtifactLevel.get(humanObj.artifact.atf.getLevel());
        if(confLevel == null){
            Log.game.error("神器等级配置错误，等级={}", humanObj.artifact.atf.getLevel());
            return;
        }
        propCalc.plus(confLevel.attr);
        power += confLevel.power;
        //皮肤属性
        Map<Long, Long> skinMap = Utils.jsonToMapLongLong(atf.getSkinLvMap());
        for (Map.Entry<Long, Long> entry : skinMap.entrySet()) {
            ConfArtifactSkin_0 confSkin = ConfArtifactSkin_0.get(entry.getKey(), entry.getValue());
            if(confSkin == null){
                continue;
            }
            propCalc.plus(confSkin.attr[0], BigDecimal.valueOf(confSkin.attr[1]));
            power += confSkin.power;
        }

        //宝石属性，和宝石套装属性
        Map<Integer, Long> gemPosMap = Utils.jsonToMapIntLong(atf.getGemPosMap());
        Map<Integer,Integer> suitNumMap = new HashMap<>();
        for (Map.Entry<Integer, Long> entry : gemPosMap.entrySet()) {
            ArtifactGemVo gem = humanObj.artifact.gemBagMap.get(entry.getValue());
            if(gem == null){
                continue;
            }
            propCalc.plus(gem.baseAttr);
            propCalc.plus(gem.randAttr);
            suitNumMap.put(gem.suit, suitNumMap.getOrDefault(gem.suit, 0)+1);
        }
        //2件套和4件套属性
        for (Map.Entry<Integer, Integer> entry : suitNumMap.entrySet()) {
            ConfArtifactGemsets confSuit = ConfArtifactGemsets.get(entry.getKey());
            if(confSuit == null){
                continue;
            }
            if(entry.getValue() >= 4 && confSuit.bonus4_attr.length>1){
                propCalc.plus(confSuit.bonus4_attr[0], BigDecimal.valueOf(confSuit.bonus4_attr[1]));
                power += confSuit.bonus4_power;
            }else if(entry.getValue() >= 2 && confSuit.bonus2_attr.length>1){
                propCalc.plus(confSuit.bonus2_attr[0], BigDecimal.valueOf(confSuit.bonus2_attr[1]));
                power += confSuit.bonus2_power;
            }
        }
        for(int i = 0; i < confLevel.baseSkill.length; i+=2){
            ConfSkillLevel_0 confSkillLevel0 = ConfSkillLevel_0.get(confLevel.baseSkill[i], confLevel.baseSkill[i+1]);
            if(confSkillLevel0 == null){
                continue;
            }
            if(confSkillLevel0.attrType == null || confSkillLevel0.attrType[0] != 1){
                propCalc.plus(confSkillLevel0.ownEffect);
            }
        }
        int artifactSkillSnLv = humanObj.getHuman2().getArtifactSkillSnLv();
        // 技能属性
        ConfArtifactSkin_0 confSkin = ConfArtifactSkin_0.get(getArtifactSn(artifactSkillSnLv), getArtifactLv(artifactSkillSnLv));
        if(confSkin != null && confSkin.skin_skill != null){
            for (int i = 0; i < confSkin.skin_skill.length; i++) {
                ConfSkillLevel_0 confSkillLevel0 = ConfSkillLevel_0.get(confSkin.skin_skill[i][0], confSkin.skin_skill[i][1]);
                if(confSkillLevel0 == null){
                    continue;
                }
                if(confSkillLevel0.attrType == null || confSkillLevel0.attrType[0] != 1){
                    propCalc.plus(confSkillLevel0.ownEffect);
                }
            }
        }

        humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.artifact, propCalc.toJSONStr());
        HumanManager.inst().updatePowerPar(humanObj, EModule.ARTIFACT, power);
        PropManager.inst().propCalc(humanObj, CombatChangeLog.神器);
    }

    /**
     * 神器使用
     * @param use 使用状态
     */
    public void handleArtifactUseC2S(HumanObject humanObj, int use) {
        Artifact artifact = humanObj.artifact.atf;
        if(artifact == null){
            return;
        }
        Map<Long,Long> skinMap = Utils.jsonToMapLongLong(artifact.getSkinLvMap());
        if(!skinMap.containsKey((long)use) && use != 0){
            return;
        }
        if(use == humanObj.getHuman2().getArtifactUse()){
            return;
        }
        humanObj.getHuman2().setArtifactUse(use);
        humanObj.getHuman().update();

        MsgArtifact.artifact_use_s2c.Builder msg = MsgArtifact.artifact_use_s2c.newBuilder();
        msg.setUse(use);
        humanObj.sendMsg(msg);
        HumanManager.inst().checkCrossSyns(humanObj, HumanBrief.K.roleFigure, HumanBrief.K.battleRole);
    }

    public void handleArtifactUseSkillC2S(HumanObject humanObj, int skillUse) {
        changeArtifactUseSkill(humanObj, skillUse);
    }

    public boolean changeArtifactUseSkill(HumanObject humanObj, int skillUse) {
        Artifact artifact = humanObj.artifact.atf;
        if(artifact == null){
            return false;
        }
        if(skillUse == getArtifactSn(humanObj.getHuman2().getArtifactSkillSnLv())){
            return false;
        }
        Map<Long,Long> skinMap = Utils.jsonToMapLongLong(artifact.getSkinLvMap());
        if(!skinMap.containsKey((long)skillUse) && skillUse != 0){
            return false;
        }
        List<Define.p_passive_skill> deleteList = getPassSkill(humanObj.getHuman2().getArtifactSkillSnLv(),humanObj.artifact.atf.getLevel());
        humanObj.getHuman2().setArtifactSkillSnLv(getArtifactSnLv(skillUse, (int) skinMap.getOrDefault((long)skillUse, 0L).longValue()));
        List<Define.p_passive_skill> updateList = getPassSkill(humanObj.getHuman2().getArtifactSkillSnLv(),humanObj.artifact.atf.getLevel());
        SkillManager.inst().sendMsg_skill_passive_update_s2c(humanObj, updateList, deleteList);

        MsgArtifact.artifact_use_skill_s2c.Builder msg = MsgArtifact.artifact_use_skill_s2c.newBuilder();
        msg.setSkillUse(skillUse);
        humanObj.sendMsg(msg);

        HumanManager.inst().setPlanTab(humanObj, PlanVo.TAB_ARTF, skillUse);

        updatePorpCalcPower(humanObj);
        return true;
    }

    /**
     * 神器解锁皮肤和升级皮肤
     * @param artifact_id 神器ID
     */
    public void handleArtifactUpSkinC2S(HumanObject humanObj, int artifactId) {
        Artifact artifact = humanObj.artifact.atf;
        if(artifact == null){
            return;
        }
        Map<Long, Long> skinMap = Utils.jsonToMapLongLong(artifact.getSkinLvMap());
        Long lv = skinMap.getOrDefault((long)artifactId, 0L);
        ConfArtifactSkin_0 confSkin = ConfArtifactSkin_0.get(artifactId,lv);
        if(confSkin == null){
            return;
        }
        ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj, confSkin.expend[0], confSkin.expend[1],MoneyItemLogKey.神器皮肤升级);
        if(!result.success){
            return;
        }
        // 保存旧的技能配置
//        List<Define.p_passive_skill> deleteList = new ArrayList<>();
//        if(getArtifactSn(humanObj.getHuman2().getArtifactSkillSnLv()) == artifactId) {
//            ConfArtifactSkin_0 oldConfSkin = ConfArtifactSkin_0.get(artifactId, lv);
//            if(oldConfSkin != null && oldConfSkin.skin_skill != null) {
//                for(int[] skill : oldConfSkin.skin_skill) {
//                    if(skill.length >= 2) {
//                        deleteList.add(SkillManager.inst().to_p_passive_skill(skill[0], skill[1]));
//                    }
//                }
//            }
//        }
        lv++;
        skinMap.put((long)artifactId, lv);
        artifact.setSkinLvMap(Utils.mapLongLongToJSON(skinMap));
        if(getArtifactSn(humanObj.getHuman2().getArtifactSkillSnLv()) == artifactId){
            humanObj.getHuman2().setArtifactSkillSnLv(getArtifactSnLv(artifactId, Utils.intValue(lv)));
            humanObj.getHuman().update();
            List<Define.p_passive_skill> updateList = getPassSkill(humanObj.getHuman2().getArtifactSkillSnLv(),artifact.getLevel());
            // 从deleteList中移除新等级仍然存在的技能
//            ConfArtifactSkin_0 newConfSkin = ConfArtifactSkin_0.get(artifactId, lv);
//            if(newConfSkin != null && newConfSkin.skin_skill != null) {
//                for(int[] newSkill : newConfSkin.skin_skill) {
//                    if(newSkill.length >= 2) {
//                        deleteList.removeIf(oldSkill ->
//                                oldSkill.getSkillId() == newSkill[0] && oldSkill.getSkillLv() == newSkill[1]);
//                    }
//                }
//            }
            SkillManager.inst().sendMsg_skill_passive_update_s2c(humanObj, updateList, null);
        }
        updatePorpCalcPower(humanObj);
        // 重新计算美观值
        CharmManager.reCalcCharmValue(humanObj, true);

        MsgArtifact.artifact_up_skin_s2c.Builder msg = MsgArtifact.artifact_up_skin_s2c.newBuilder();
        msg.setArtifactId(artifactId);
        msg.setSkinLev(Utils.intValue(lv));
        humanObj.sendMsg(msg);
        if(lv==1){
            humanObj.getHuman2().setArtifactUse(artifactId);
            humanObj.getHuman().update();
            HumanManager.inst().checkCrossSyns(humanObj, HumanBrief.K.roleFigure, HumanBrief.K.battleRole);
            humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_成就任务, TaskConditionTypeKey.TASK_TYPE_119);
            Event.fire(EventKey.UPDATE_NEWS, "humanObj", humanObj, "newsCondition", NewsConditionTypeKey.ArtifactUnlock, "value", artifactId);
        }
        handleArtifactInfoC2S(humanObj);
    }

    public int getArtifactSnLv(int sn, int lv) {
       return sn*100+lv;
    }

    public int getArtifactSn(int snLv) {
        return snLv/100;
    }

    public int getArtifactLv(int snLv) {
        return snLv%100;
    }

    /**
     * 神器宝石信息
     */
    public void handleArtifactGemInfoC2S(HumanObject humanObj) {
        MsgArtifactGem.artifact_gem_info_s2c.Builder msg = MsgArtifactGem.artifact_gem_info_s2c.newBuilder();
        Artifact artifact = humanObj.artifact.atf;
        if(artifact == null){
            humanObj.sendMsg(msg);
            return;
        }

        Map<Integer,Long> gemPosMap = Utils.jsonToMapIntLong(artifact.getGemPosMap());
        for (Map.Entry<Integer, Long> entry : gemPosMap.entrySet()) {
            Define.p_key_value.Builder pKeyValue = Define.p_key_value.newBuilder();
            pKeyValue.setK(entry.getKey());
            pKeyValue.setV(entry.getValue());
            msg.addPosList(pKeyValue);
        }

        for (Map.Entry<Long, ArtifactGemVo> entry : humanObj.artifact.gemBagMap.entrySet()) {
            ArtifactGemVo gem = entry.getValue();
            Define.p_artifact_gem.Builder gemInfo = gem.toProtobuf();
            msg.addGemList(gemInfo);
        }

        humanObj.sendMsg(msg);
    }

    /**
     * 神器宝石装备
     * @param pos_id 位置ID
     * @param gem_id 宝石ID
     */
    public void handleArtifactGemWearC2S(HumanObject humanObj, int posId, long gemId) {
        Artifact artifact = humanObj.artifact.atf;
        if(artifact == null){
            return;
        }

        if(!humanObj.artifact.gemBagMap.containsKey(gemId)&&gemId!=0){
            Log.game.error("神器宝石背包中没有该宝石，gemId={}", gemId);
            return;
        }
        if(posId < GEM_INDEX_MIN || posId > GEM_INDEX_MAX){
            Log.game.error("神器宝石位置错误，posId={}", posId);
            return;
        }
        Map<Integer,Long> gemPosMap = Utils.jsonToMapIntLong(artifact.getGemPosMap());

        gemPosMap.put(posId, gemId);
        artifact.setGemPosMap(Utils.mapIntLongToJSON(gemPosMap));
        artifact.update();
        updatePorpCalcPower(humanObj);

        MsgArtifactGem.artifact_gem_wear_s2c.Builder msg = MsgArtifactGem.artifact_gem_wear_s2c.newBuilder();
        msg.setPosId(posId);
        msg.setGemId(gemId);
        humanObj.sendMsg(msg);
    }

    /**
     * 神器宝石升级
     * @param gem_id 宝石ID
     * @param use_list 使用的宝石ID列表
     * @param item_list 物品列表
     */
    public void handleArtifactGemUpC2S(HumanObject humanObj, long gemId, List<Long> useListList, List<Define.p_key_value> itemListList) {
        Artifact artifact = humanObj.artifact.atf;
        if(artifact == null){
            return;
        }
        ArtifactGemVo gem = humanObj.artifact.gemBagMap.get(gemId);
        if(gem == null){
            Log.game.error("神器宝石背包中没有该宝石，gemId={}", gemId);
            return;
        }
        ConfArtifactGemQuality confGemQuality = ConfArtifactGemQuality.get(gem.quality);
        if(confGemQuality == null){
            Log.game.error("神器宝石品质配置错误，quality={}", gem.quality);
            return;
        }

        int exp = gem.exp;
        //强化石提供的经验
        for (Define.p_key_value item : itemListList) {
            int itemSn = Utils.intValue(item.getK());
            int num = Utils.intValue(item.getV());
            if(num<=0){
                continue;
            }
            ConfGoods confGoods = ConfGoods.get(itemSn);
            if(confGoods == null){
                Log.game.error("物品配置错误，itemSn={}", itemSn);
                continue;
            }
            if(confGoods.type != ItemConstants.附魔强化石){
                Log.game.error("物品类型错误，itemSn={}", itemSn);
                continue;
            }
            ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj, itemSn, num, MoneyItemLogKey.神器宝石升级);
            if(!result.success){
                continue;
            }
            exp += confGoods.effect[0][0] * num;
        }

        //符石提供的经验
        Iterator<Long> it = useListList.iterator();
        while (it.hasNext()) {
            long useGemId = it.next();
            ArtifactGemVo useGem = humanObj.artifact.gemBagMap.get(useGemId);
            if(useGem == null){
                Log.game.error("神器宝石背包中没有该宝石，useGemId={}", useGemId);
                it.remove();
                continue;
            }
            if(useGem.isLock == 1){
                Log.game.error("神器宝石已锁定，useGemId={}", useGemId);
                it.remove();
                continue;
            }
            //基础经验
            exp += ConfArtifactGemQuality.get(useGem.quality).quality_exp;
            //吃下去的经验按比例吐出来
            int lv = useGem.lv;
            ConfArtifactGemLevel_0 confLevel = ConfArtifactGemLevel_0.get(useGem.quality, useGem.lv);
            if(confLevel == null){
                Log.game.error("神器宝石等级配置错误，lv={}, quality={}", useGem.lv, useGem.quality);
                continue;
            }
            exp += useGem.exp * confLevel.weaken/10000.0f;
            while (lv > 1) {
                lv--;
                confLevel = ConfArtifactGemLevel_0.get(useGem.quality,lv);
                if(confLevel == null){
                    break;
                }
                exp += confLevel.exp * confLevel.weaken/10000.0f;
            }
            humanObj.artifact.gemBagMap.remove(useGemId);
            com.pwrd.op.LogOp.log("removeGem",
                    humanObj.getHuman().getId(),
                    org.gof.core.Port.getTime(),
                    Utils.formatTime(org.gof.core.Port.getTime(), "yyyy-MM-dd"),
                    humanObj.getHuman().getAccount(),
                    humanObj.getHuman().getName(),
                    humanObj.getHuman().getLevel(),
                    humanObj.getHuman().getServerId(),
                    useGem.id,
                    useGem.lv,
                    useGem.quality + "_" + useGem.pos + "_" + useGem.suit,
                    1
            );
        }

        //升级后的等级经验
        ConfArtifactGemLevel_0 confLevel = ConfArtifactGemLevel_0.get(gem.quality, gem.lv);
        if(confLevel == null){
            Log.game.error("神器宝石等级配置错误，lv={}, quality={}", gem.lv, gem.quality);
            return;
        }
        while (confLevel != null&&exp >= confLevel.exp&&confLevel.exp>0) {
            exp -= confLevel.exp;
            gem.lv++;
            //基础属性
            Map<Integer,BigDecimal> baseAttr = gem.baseAttr.getDatas();
            for (Map.Entry<Integer, BigDecimal> entry : baseAttr.entrySet()) {
                int attrId = entry.getKey();
                int attrValue = entry.getValue().intValue();
                ConfArtifactGemattr confGemattr = getConfArtifactGemattr(confGemQuality.mainattr_groups[gem.pos-1], attrId);
                if(confGemattr == null){
                    continue;
                }
                int attrValueAdd = confGemattr.upgrade_value.length>1?Utils.random(confGemattr.upgrade_value[0],confGemattr.upgrade_value[1]+1):confGemattr.upgrade_value[0];
                gem.baseAttr.plus(attrId, BigDecimal.valueOf(attrValueAdd));
            }
            //随机属性
            if(confLevel.is_strengthen == 1){
                Map<Integer,BigDecimal> randAttr = gem.randAttr.getDatas();
                boolean isAdd = Utils.random(10000) < ConfGlobal.get(201).intArray[randAttr.size()];
                //新增一条属性
                if(isAdd){
                    List<Integer> excludeIds = new ArrayList<>();
                    for (Map.Entry<Integer, BigDecimal> entry : randAttr.entrySet()) {
                        excludeIds.add(getConfArtifactGemattr(confGemQuality.viceattr_group, entry.getKey()).sn);
                    }
                    ConfArtifactGemattr confGemattr = genAttrConf(confGemQuality.viceattr_group, excludeIds);
                    int attrValueAdd = confGemattr.initial_value.length>1?Utils.random(confGemattr.initial_value[0],confGemattr.initial_value[1]+1):confGemattr.initial_value[0];
                    gem.randAttr.plus(confGemattr.attr_id, BigDecimal.valueOf(attrValueAdd));
                }else {
                    //根据权重增强一条现有属性
                    int[] weights = new int[randAttr.size()];
                    int i = 0;
                    for (Map.Entry<Integer, BigDecimal> entry : randAttr.entrySet()) {
                        weights[i] = getConfArtifactGemattr(confGemQuality.viceattr_group, entry.getKey()).pro;
                        i++;
                    }
                    int randAttrIndex = Utils.randomByWeight(weights);
                    int attrId = (int) randAttr.keySet().toArray()[randAttrIndex];
                    ConfArtifactGemattr confGemattr = getConfArtifactGemattr(confGemQuality.viceattr_group, attrId);
                    int attrValueAdd = confGemattr.upgrade_value.length>1?Utils.random(confGemattr.upgrade_value[0],confGemattr.upgrade_value[1]+1):confGemattr.upgrade_value[0];
                    gem.randAttr.plus(attrId, BigDecimal.valueOf(attrValueAdd));
                }
            }
            confLevel = ConfArtifactGemLevel_0.get(gem.quality, gem.lv);
        }
        gem.exp = exp;

        saveGemBag(humanObj);
        //如果宝石穿着了，更新属性
        Map<Integer,Long> gemPosMap = Utils.jsonToMapIntLong(artifact.getGemPosMap());
        if(gemPosMap.containsValue(gemId)){
            updatePorpCalcPower(humanObj);
        }

        MsgArtifactGem.artifact_gem_up_s2c.Builder msg = MsgArtifactGem.artifact_gem_up_s2c.newBuilder();
        msg.addAllItemList(itemListList);
        msg.addAllCostList(useListList);
        msg.setGemInfo(gem.toProtobuf());
        humanObj.sendMsg(msg);
    }

    private ConfArtifactGemattr getConfArtifactGemattr(int groupId, int attrId){
        List<Integer> snList = GlobalConfVal.getConfGroupGemattrSnList(groupId);
        if(snList == null || snList.size() == 0){
            Log.game.error("神器宝石属性配置错误，groupId={}, attrId={}", groupId, attrId);
            return null;
        }
        for (int sn : snList) {
            ConfArtifactGemattr conf = ConfArtifactGemattr.get(sn);
            if(conf.attr_id == attrId){
                return conf;
            }
        }
        return null;
    }

    /**
     * 神器宝石红点已读
     * @param gem_id 宝石ID
     */
    public void handleArtifactGemRedReadC2S(HumanObject humanObj, long gemId) {
        Artifact artifact = humanObj.artifact.atf;
        if(artifact == null){
            return;
        }
        ArtifactGemVo gem = humanObj.artifact.gemBagMap.get(gemId);
        if(gem == null){
            Log.game.error("神器宝石背包中没有该宝石，gemId={}", gemId);
            return;
        }
        gem.isRed = 0;

        saveGemBag(humanObj);

        MsgArtifactGem.artifact_gem_red_read_s2c.Builder msg = MsgArtifactGem.artifact_gem_red_read_s2c.newBuilder();
        msg.setGem(gem.toProtobuf());
        humanObj.sendMsg(msg);
    }

    /**
     * 神器宝石锁定
     * @param gem_id 宝石ID
     */
    public void handleArtifactGemLockC2S(HumanObject humanObj, long gemId) {
        Artifact artifact = humanObj.artifact.atf;
        if(artifact == null){
            artifact = new Artifact();
            artifact.setId(humanObj.id);
            artifact.setLevel(1);
            artifact.persist();
            humanObj.artifact.atf = artifact;
            ArtifactManager.inst().init(humanObj);
        }
        ArtifactGemVo gem = humanObj.artifact.gemBagMap.get(gemId);
        if(gem == null){
            Log.game.error("神器宝石背包中没有该宝石，gemId={}", gemId);
            return;
        }
        gem.isLock = gem.isLock == 1 ?  0 : 1;

        saveGemBag(humanObj);

        MsgArtifactGem.artifact_gem_lock_s2c.Builder msg = MsgArtifactGem.artifact_gem_lock_s2c.newBuilder();
        msg.setGem(gem.toProtobuf());
        humanObj.sendMsg(msg);
    }

    public List<Define.p_passive_skill> getPassSkill(int artifactSkillSnLv, int artifactLv) {
        List<Define.p_passive_skill> list = new ArrayList<>();
        ConfArtifactSkin_0 confSkin = ConfArtifactSkin_0.get(getArtifactSn(artifactSkillSnLv), getArtifactLv(artifactSkillSnLv));
        if(confSkin == null || confSkin.skin_skill == null){
            return list;
        }
        for (int i = 0; i < confSkin.skin_skill.length; i++) {
            Define.p_passive_skill.Builder passive_skill = Define.p_passive_skill.newBuilder();
            passive_skill.setSkillId(confSkin.skin_skill[i][0]);
            passive_skill.setSkillLv(confSkin.skin_skill[i][1]);
            list.add(passive_skill.build());
        }
        ConfArtifactLevel confLevel = ConfArtifactLevel.get(artifactLv);
        if(confLevel == null){
            return list;
        }
        for(int i = 0; i < confLevel.baseSkill.length; i+=2){
            Define.p_passive_skill.Builder passive_skill = Define.p_passive_skill.newBuilder();
            passive_skill.setSkillId(confLevel.baseSkill[i]);
            passive_skill.setSkillLv(confLevel.baseSkill[i+1]);
            list.add(passive_skill.build());
        }
        return list;
    }

    /**
     * 神器功能解锁
     */
    @Listener(EventKey.FUNCTION_OPEN)
    public void unlock(Param param) {
        HumanObject humanObj = param.get("humanObj");
        List<Integer> snList = Utils.getParamValue(param, "openList", new ArrayList<>());
        if (!snList.contains(FuncOpenType.FUNC_ARTIFACT)) {
            return;
        }
        if (humanObj.artifact.atf != null) {
            return;
        }
//        if (!humanObj.isModUnlock(FuncOpenType.FUNC_ARTIFACT)) {
//            return;
//        }
        //初始化神器
        initArtifact(humanObj);
    }

    @Listener(EventKey.FUNCTION_OPEN_LOGIN)
    public void _listener_FUNCTION_OPEN_LOGIN(Param param) {
        HumanObject humanObj = param.get("humanObj");
        int sn = Utils.getParamValue(param, "sn", 0);
        if (sn != FuncOpenType.FUNC_ARTIFACT) {
            return;
        }
        if (humanObj.artifact.atf != null) {
            return;
        }
        if (!humanObj.isModUnlock(FuncOpenType.FUNC_ARTIFACT)) {
            return;
        }
        //初始化神器
        initArtifact(humanObj);
    }

    private void initArtifact(HumanObject humanObj) {
        Artifact artifact = new Artifact();
        artifact.setId(humanObj.id);
        artifact.setLevel(1);
        artifact.persist();
        humanObj.artifact.atf = artifact;
        ArtifactManager.inst().init(humanObj);
        updatePorpCalcPower(humanObj);
        handleArtifactInfoC2S(humanObj);
    }
}
