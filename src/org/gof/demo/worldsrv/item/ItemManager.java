package org.gof.demo.worldsrv.item;

import org.apache.commons.collections.map.HashedMap;
import org.gof.core.Port;
import org.gof.core.scheduler.ScheduleTask;
import org.gof.core.support.*;
import org.gof.core.support.observer.Listener;
import org.gof.demo.battlesrv.msgHandler.SkillManager;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.battlesrv.support.PropKey;
import org.gof.demo.worldsrv.activity.ActivityControlTypeFactory;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.calculator.IActivityControl;
import org.gof.demo.worldsrv.angel.AngelManager;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.common.GameServiceProxy;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.Farm;
import org.gof.demo.worldsrv.fate.FateManager;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.home.BuildVo;
import org.gof.demo.worldsrv.home.HomeManager;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.human.MoneyManager;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgGoods;
import org.gof.demo.worldsrv.pet.PetManager;
import org.gof.demo.worldsrv.privilege.PrivilegeManager;
import org.gof.demo.worldsrv.privilege.PrivilegeType;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.TokenItemType;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.enumKey.NewsConditionTypeKey;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.gof.demo.worldsrv.produce.ProduceManager.snList;


public class ItemManager extends ManagerBase {

	/**
	 * 获取实例
	 * @return
	 */
	public static ItemManager inst() {
		return inst(ItemManager.class);
	}

	/** 
	 * 统一给道具
	 * <AUTHOR>
	 * @Date 2024/2/28
	 * @Param 
	 */
	public void produceAdd(HumanObject humanObj, int sn, int num, MoneyItemLogKey log, Object... obj) {
		ItemData itemData = humanObj.operation.itemData;
		ConfGoods confItem = ConfGoods.get(sn);
		if(confItem == null){
			Log.temp.error("===ConfItem配表错误， sn={}, num={}, log={}, obj={}", sn, num, log, obj);
			return;
		}
		itemData.addOrCostItemSnNum(humanObj, sn, num, log);
		if(Utils.isDebugMode()){
//			Log.temp.info("====获得物品道具， sn={}，num={}, log={}", sn, num, log);
		}

	}



	/** 
	 * 检测新获得物品后，是否有后续操作(例如，自动扣除解锁技能/同伴，或开礼包)
	 * <AUTHOR>
	 * @Date 2024/3/13
	 * @Param TODO 注意：13类型开礼包。有包套物品套包无限循环风险。
	 */
	public void checkAddItem(HumanObject humanObj, int itemSn){
		ConfGoods confGoods = ConfGoods.get(itemSn);
		if(confGoods.type == ItemConstants.技能){
			skillUnlock(humanObj, confGoods.effect[0][0], 1);
		} else if(confGoods.type == ItemConstants.同伴){
			petUnlock(humanObj, confGoods.effect[0][0]);
		} else if(confGoods.type == ItemConstants.礼包){
			useBagType13(humanObj, confGoods);
		}
	}

	private void skillUnlock(HumanObject humanObj, int skillSn, int level){
		List<Integer> skillSnList = Utils.strToIntList(humanObj.getHuman3().getSkillSnList());
		if(skillSnList.contains(skillSn)){
			return;
		}
		ConfSkillLevel_0 confSkillLevel_0 = ConfSkillLevel_0.get(skillSn, level);
		if(confSkillLevel_0 == null){
			Log.temp.error("===ConfSkillLevel_0配表错误, skillSn={},1", skillSn);
			return;
		}
		if(confSkillLevel_0.expend == null || confSkillLevel_0.expend.length <= 0){
			Log.temp.error("===无消耗,confSkillLevel_0 sn={}, level ={}", skillSn, level);
			return;
		}
		ReasonResult result = ProduceManager.inst().canCostIntArr(humanObj, confSkillLevel_0.expend);
		if(!result.success){
			return;
		}
		Map<Integer, Integer> skillSnLvMap = humanObj.operation.skillData.skillSnLvMap;
		if(skillSnLvMap.containsKey(skillSn) ){
			return;
		}
		ProduceManager.inst().costIntArr(humanObj, confSkillLevel_0.expend, MoneyItemLogKey.技能解锁);
		skillSnList.add(skillSn);
		humanObj.getHuman3().setSkillSnList(Utils.listToString(skillSnList));
		humanObj.getHuman3().update();
		skillSnLvMap.put(skillSn, level);
		humanObj.getHuman2().setSkillSnLvMap(Utils.mapIntIntToJSON(skillSnLvMap));
		humanObj.getHuman2().update();
		SkillManager.inst().updatePower(humanObj);
		SkillManager.inst().updateSkillPorpCalc(humanObj);
		ConfSkill conf = ConfSkill.get(skillSn);
		Event.fire(EventKey.UPDATE_NEWS, "humanObj", humanObj, "newsCondition", NewsConditionTypeKey.SkillQuality, "value", conf.quality);
	}

	private void petUnlock(HumanObject humanObj, int petSn){
		ConfPetlevel_0 confPetlevel_0 = ConfPetlevel_0.get(petSn, 1);
		if(confPetlevel_0 == null){
			Log.temp.error("===ConfPetlevel_0配表错误, petSn={},1", petSn);
			return;
		}
		ReasonResult result = ProduceManager.inst().canCostIntArr(humanObj, confPetlevel_0.expend);
		if(!result.success){
			return;
		}
		Map<Integer, Integer> petSnLvMap = humanObj.operation.petData.petSnLvMap;
		if(petSnLvMap.containsKey(petSn)){
			return;
		}
		ProduceManager.inst().costIntArr(humanObj, confPetlevel_0.expend, MoneyItemLogKey.同伴解锁);
		petSnLvMap.put(petSn, 1);
		humanObj.getHuman2().setPetSnLvMap(Utils.mapIntIntToJSON(petSnLvMap));
		PetManager.inst().sendMsg_pet_unlock_s2c(humanObj, petSn);
		PetManager.inst().updatePetPropCalc(humanObj);
		ConfPet confPet = ConfPet.get(petSn);
		Event.fire(EventKey.UPDATE_NEWS, "humanObj", humanObj, "newsCondition", NewsConditionTypeKey.PetQuality, "value", confPet.quality);
	}


	private void useBagType13(HumanObject humanObj, ConfGoods confGoods){
		int itemSn = confGoods.sn;
		int num = humanObj.operation.itemData.getItemNum(itemSn);
		int groupId = confGoods.effect[0][0];
		if(groupId <= 0){
			Log.temp.error("===ConfGoods配表错误， itemSn={}, groupId={}", itemSn, groupId);
			return;
		}
		// 单次处理最大数量
		int maxNum = 100;
		if(num <= maxNum){
			openGift(humanObj, confGoods.sn, num, groupId);
			return;
		}
		int loopNum = (int)Math.ceil(num / maxNum);
		for(int i= 0; i < loopNum; i++){
			Port.getCurrent().getServices(D.SERV_STAGE_DEFAULT).scheduleOnce(new ScheduleTask() {
				@Override
				public void execute() {
					int num = humanObj.operation.itemData.getItemNum(itemSn);
					if(num > maxNum){
						num = maxNum;
					}
					openGift(humanObj, confGoods.sn, num, groupId);
				}
			}, 2 * Time.SEC);
		}
	}

	private void openGift(HumanObject humanObj, int sn, int num, int groupId){
		if(groupId <= 0){
			Log.temp.error("===ConfGoods配表错误， sn={}, groupId={}", sn, groupId);
			return;
		}
		ReasonResult result = ProduceManager.inst().canCostProduce(humanObj, sn, num);
		if(!result.success){
			return;
		}
		ProduceManager.inst().costItem(humanObj, sn, num, MoneyItemLogKey.类型13开礼包);
		Map<Integer, Integer> dropMap = new HashedMap();
		for(int i = 0; i < num; i++){
			dropMap = Utils.mergeMap(dropMap, ProduceManager.inst().getDropMap(groupId));
		}
		ProduceManager.inst().produceAll(humanObj, dropMap, MoneyItemLogKey.类型13开礼包);
	}

	public int getItemNum(HumanObject humanObj, int itemSn) {
		return humanObj.operation.itemData.getItemNum(itemSn);
	}

	@Listener(EventKey.ITEM_CHANGE_ADD)
	public void _on_ITEM_CHANGE_ADD(Param param) {
		HumanObject humanObj = param.get("humanObj");
		int itemSn = param.get("itemSn");
		int addNum = param.get("itemNum");
		ConfGoodsRefresh conf = ConfGoodsRefresh.get(itemSn);
		if(conf != null && conf.type == ItemConstants.AUTO_TYPE_TIMER){
			int itemNum = humanObj.operation.itemData.getItemNum(conf.sn);
			int itemMax = ItemManager.inst().getRefreshGoodMaxNum(humanObj,conf);
			if(itemNum-addNum < itemMax && itemNum >= itemMax){
				setRefreshGoodNextTime(humanObj, conf.sn, 0);
				humanObj.stopCheckItemRecover(conf.sn);
				HumanManager.inst().sendMsg_role_goods_refresh_list_s2c(humanObj);
			}
		}
	}

	@Listener(EventKey.HUMAN_LOGIN_FINISH)
	public void _on_HUMAN_LOGIN_FINISH(Param param) {
		HumanObject humanObj = param.get("humanObj");
		checkPet(humanObj);
		loginItemRecover(humanObj);
	}

	@Listener(EventKey.HUMAN_RESET_FIVE)
	public void _listener_HUMAN_RESET_FIVE(Param param) {
		HumanObject humanObj = param.get("humanObj");
		for (ConfGoodsRefresh conf : ConfGoodsRefresh.findAll()) {
			if (conf.type != ItemConstants.AUTO_TYPE_DAILY) {
				continue;
			}
			int num = getItemNum(humanObj, conf.sn);
			int max = getRefreshGoodMaxNum(humanObj, conf);
			if (num < max) {
				ProduceManager.inst().produceAdd(humanObj, conf.sn, max - num, MoneyItemLogKey.道具恢复);
			}
		}
	}

	/**
	 * 这个不能走事件监听，因为事件触发时，玩家功能解锁列表还没填充
	 */
	public void _listener_HUMAN_RESET_WEEK_5ST(Param param) {
		HumanObject humanObj = param.get("humanObj");
		for (ConfGoodsRefresh conf : ConfGoodsRefresh.findAll()) {
			if (conf.type != ItemConstants.AUTO_TYPE_WEEK) {
				continue;
			}
			if (!humanObj.isModUnlock(conf.newfuncopen_id)) {
				Log.temp.error("道具恢复时功能未解锁, sn={}", conf.newfuncopen_id);
				continue;
			}
			int extraNum = 0;
			if (conf.sn == ItemConstants.goods_飞宠蛋糕) {
				extraNum = PrivilegeManager.inst().getPrivilegeEffectValue(humanObj, PrivilegeType.FlyPet_CakeRecoverNum);
			}
			int max = getRefreshGoodMaxNum(humanObj, conf);
			if (max == 0) {
				// 没有上限
				int num = conf.init + extraNum;
				ProduceManager.inst().produceAdd(humanObj, conf.sn, num, MoneyItemLogKey.每周道具恢复);
			} else {
				// 有上限
				int itemNum = getItemNum(humanObj, conf.sn);
				if (itemNum >= max) {
					continue;// 本身数量已经超过上限
				}
				int addNum = conf.init + extraNum;
				int num = itemNum + addNum;
				if (num >= max) {
					ProduceManager.inst().produceAdd(humanObj, conf.sn, max - itemNum, MoneyItemLogKey.每周道具恢复);// 新加的数量超过上限
				} else {
					ProduceManager.inst().produceAdd(humanObj, conf.sn, addNum, MoneyItemLogKey.每周道具恢复);// 没有超过上限，直接加
				}
			}
		}
	}

	public void checkPet(HumanObject humanObj){
		Map<Integer, Integer> skillSnLvMap = humanObj.operation.skillData.skillSnLvMap;
		Map<Integer, Integer> petSnLvMap = humanObj.operation.petData.petSnLvMap;

		List<Integer> snList = new ArrayList<>(humanObj.operation.itemData.getItemSnNumMap().keySet());
		for(int sn : snList){
			ConfGoods confGoods = ConfGoods.get(sn);
			if(confGoods == null){
				continue;
			}
			if(confGoods.type == ItemConstants.技能){
				int skillSn = confGoods.effect[0][0];
				if(!skillSnLvMap.containsKey(skillSn)){
					skillUnlock(humanObj, skillSn, 1);
				}
			} else if(confGoods.type == ItemConstants.同伴){
				int petSn = confGoods.effect[0][0];
				if(!petSnLvMap.containsKey(petSn)){
					petUnlock(humanObj, petSn);
				}
			}

		}

	}

	/**
	 * 物品合成
	 * <AUTHOR>
	 * @Date 2024/4/17
	 * @Param once 1一键，0单次；Type 1：伙伴， 0：技能
	 */
	public void _msg_goods_compose_skill_pet_c2s(HumanObject humanObj, int once, int type, List<Define.p_key_value> costListList) {
		switch (type){
			case 0:
				goods_compose_skill(humanObj, once, costListList);
				break;
			case 1:
				goods_compose_pet(humanObj, once, costListList);
				break;
			default:
				break;
		}

	}

	private void goods_compose_skill(HumanObject humanObj, int once, List<Define.p_key_value> costListList){
		List<Define.p_reward> dInfoList = new ArrayList<>();
		int costNum = ConfGlobal.get(ConfGlobalKey.技能满级物品合成消耗数量.SN).value;
		Map<Integer, Integer> skillSnLvMap = humanObj.operation.skillData.skillSnLvMap;
		ItemData itemData = humanObj.operation.itemData;
		for (Define.p_key_value dInfo : costListList) {
			ConfGoods conf = ConfGoods.get(Utils.intValue(dInfo.getK()));
			if(conf == null){
				continue;
			}
			if(conf.type != ItemConstants.技能){
				continue;
			}
			int skillSn = conf.effect[0][0];
			int value = (int)dInfo.getV();
			int level = skillSnLvMap.get(skillSn);
			int maxLv = GlobalConfVal.getSkillMaxLv(skillSn);
			if(level < maxLv){
				continue;
			}
			int num;
			if (once == 1) {
				// 一键
				num = itemData.getItemNum(conf.sn) / costNum;
				if (num <= 0) {
					continue;
				}
			} else {
				// 单个
				num = value;
			}
			int costItemNum = num * costNum;
			if (costItemNum <= 0) {
				continue;
			}
			ReasonResult result = ProduceManager.inst().canCostProduce(humanObj, conf.sn, costItemNum);
			if (!result.success) {
				continue;
			}
			int outputSn = getHaveNextQualityGoodsOutputSn(humanObj, conf.type, conf.quality + 1);
			if (outputSn == 0) {
				continue;
			}
			Map<Integer, Integer> addMap = new HashMap<>();
			for (int i = 0; i < num; i++) {
				Map<Integer, Integer> dropMap = ProduceManager.inst().getDropMap(outputSn);
				addMap = Utils.mergeMap(addMap, dropMap);
			}
			ProduceManager.inst().costItem(humanObj, conf.sn, costItemNum, MoneyItemLogKey.合成);
			ProduceManager.inst().produceAdd(humanObj, addMap, MoneyItemLogKey.合成);
			for (Map.Entry<Integer, Integer> entry : addMap.entrySet()) {
				dInfoList.add(HumanManager.inst().to_p_reward(entry.getKey(), entry.getValue()));
			}
		}
		InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, 0, dInfoList);
		sendMsg_goods_compose_skill_pet_s2c(humanObj, dInfoList);
	}

	private int getHaveNextQualityGoodsOutputSn(HumanObject humanObj, int type, int quality) {
		if (type == ItemConstants.技能) {
			return GlobalConfVal.skillComposeOutputMap.getOrDefault(quality, 0);
		} else if (type == ItemConstants.同伴) {
			return GlobalConfVal.petComposeOutputMap.getOrDefault(quality, 0);
		} else {
			return 0;
		}
	}

	private void goods_compose_pet(HumanObject humanObj, int once, List<Define.p_key_value> costListList){
		List<Define.p_reward> dInfoList = new ArrayList<>();
		int costNum = ConfGlobal.get(ConfGlobalKey.伙伴满级物品合成消耗数量.SN).value;
		Map<Integer, Integer> petSnLvMap = humanObj.operation.petData.petSnLvMap;
		ItemData itemData = humanObj.operation.itemData;
		for (Define.p_key_value dInfo : costListList) {
			ConfGoods conf = ConfGoods.get(Utils.intValue(dInfo.getK()));
			if (conf == null) {
				continue;
			}
			if (conf.type != ItemConstants.同伴) {
				continue;
			}
			int petSn = conf.effect[0][0];
			int value = (int)dInfo.getV();
			int level = petSnLvMap.get(petSn);
			int maxLv = GlobalConfVal.getPetMaxLv(petSn);
			if (level < maxLv) {
				continue;
			}
			int num;
			if (once == 1){
				// 一键
				num = itemData.getItemNum(conf.sn) / costNum;
				if (num <= 0) {
					continue;
				}
			} else {// 单个
				num = value;
			}
			int costItemNum = num * costNum;
			if (costItemNum <= 0) {
				continue;
			}
			ReasonResult result = ProduceManager.inst().canCostProduce(humanObj, conf.sn, costItemNum);
			if (!result.success) {
				continue;
			}
			int outputSn = getHaveNextQualityGoodsOutputSn(humanObj, conf.type, conf.quality + 1);
			if (outputSn == 0) {
				continue;
			}
			ProduceManager.inst().costItem(humanObj, conf.sn, costItemNum, MoneyItemLogKey.合成);
			Map<Integer, Integer> addMap = new HashMap<>();
			for (int i = 0; i < num; i++) {
				Map<Integer, Integer> dropMap = ProduceManager.inst().getDropMap(outputSn);
				addMap = Utils.mergeMap(addMap, dropMap);
			}
			ProduceManager.inst().produceAdd(humanObj, addMap, MoneyItemLogKey.合成);
			for (Map.Entry<Integer, Integer> entry : addMap.entrySet()) {
				dInfoList.add(HumanManager.inst().to_p_reward(entry.getKey(), entry.getValue()));
			}
		}
		sendMsg_goods_compose_skill_pet_s2c(humanObj, dInfoList);
		InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, 0, dInfoList);
	}

	public void sendMsg_goods_compose_skill_pet_s2c(HumanObject humanObj, List<Define.p_reward> dInfoList){
		MsgGoods.goods_compose_skill_pet_s2c.Builder msg = MsgGoods.goods_compose_skill_pet_s2c.newBuilder();
		msg.addAllRewardList(dInfoList);
		humanObj.sendMsg(msg);
	}

	public int getRefreshGoodMaxNum(HumanObject humanObj, ConfGoodsRefresh conf) {
		if(conf.sn == ItemConstants.AUTO_FARM_SEED){
			Farm farm = humanObj.farmData.farm;
			if(farm == null){
				return conf.max;
			}
			int attr = humanObj.getPropPlus().getBigDecimal(PropKey.farm_seed_add.getAttributeSn()).intValue();
			return attr + conf.max;

		}else if(conf.sn == ItemConstants.AUTO_MINE){
			PropCalc propPlus = humanObj.getPropPlus();
			int attr = propPlus.getBigDecimal(PropKey.pickaxe_max.getAttributeSn()).intValue();
			int axeMax = (int)(ConfGlobal.get(602).value + attr);
			return axeMax;
		}
		return conf.max;
	}

	public int getRefreshGoodNextTime(HumanObject humanObj, int sn) {
		Map<Integer,Integer> itemSnTimeMap = Utils.jsonToMapIntInt(humanObj.operation.itemData.getItem().getItemRecoverMap());
		if(itemSnTimeMap.containsKey(sn)){
			return itemSnTimeMap.get(sn);
		}
		return 0;
	}

	public void setRefreshGoodNextTime(HumanObject humanObj, int sn, int time) {
		Map<Integer,Integer> itemSnTimeMap = Utils.jsonToMapIntInt(humanObj.operation.itemData.getItem().getItemRecoverMap());
		itemSnTimeMap.put(sn, time);
		humanObj.operation.itemData.getItem().setItemRecoverMap(Utils.mapIntIntToJSON(itemSnTimeMap));
		humanObj.operation.itemData.getItem().update();
	}

	/**
	 * 登入刷新道具恢复
	 */
	private void loginItemRecover(HumanObject humanObj) {
		for (ConfGoodsRefresh conf : ConfGoodsRefresh.findAll()) {
			if(conf.type != ItemConstants.AUTO_TYPE_TIMER){
				continue;
			}
			checkAndRecoverRefreshGood(humanObj, conf);
		}
		HumanManager.inst().sendMsg_role_goods_refresh_list_s2c(humanObj);
	}

	public ReasonResult costTimerRecoverItem(HumanObject humanObj, int sn, int num, MoneyItemLogKey log) {
		ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj, sn, num, log);
		if(!result.success){
			return result;
		}
		ConfGoodsRefresh conf = ConfGoodsRefresh.get(sn);
		if(conf == null || conf.type != ItemConstants.AUTO_TYPE_TIMER){
			return result;
		}
		if(getRefreshGoodNextTime(humanObj, sn) != 0){
			return result;
		}
		int recoverTime = humanObj.getRecoverTime(conf);
		setRefreshGoodNextTime(humanObj, sn, (int)(Port.getTime()/Time.SEC) + recoverTime);
		checkAndRecoverRefreshGood(humanObj, conf);
		HumanManager.inst().sendMsg_role_goods_refresh_list_s2c(humanObj);
		return result;
	}

	/**
	 * 监测道具恢复
	 */
	public void checkAndRecoverRefreshGood(HumanObject humanObj, ConfGoodsRefresh conf) {
		if(conf.time == 0){
			return;
		}
		int add = 0;
		int now = (int)(Port.getTime()/Time.SEC);
		int refreshTime = ItemManager.inst().getRefreshGoodNextTime(humanObj,conf.sn);
		int itemNum = humanObj.operation.itemData.getItemNum(conf.sn);
		int itemMax = ItemManager.inst().getRefreshGoodMaxNum(humanObj,conf);
		int recoverTime = humanObj.getRecoverTime(conf);
		if (refreshTime < now) {
			add = (now - refreshTime) / recoverTime+1;
			if (itemNum + add > itemMax) {
				add = itemMax - itemNum;
			}
			if(add > 0){
				ProduceManager.inst().produceAdd(humanObj, conf.sn, add, MoneyItemLogKey.道具恢复);
			}
		}
		if (add + itemNum < itemMax) {
			refreshTime = refreshTime + recoverTime * add;
			ItemManager.inst().setRefreshGoodNextTime(humanObj, conf.sn, refreshTime);
			if(refreshTime-now < 0){
				Log.game.error("checkAndRecoverRefreshGood error, nextRefreshTime={}, now={}, itemSn={}, itemNum={}, add={}, itemMax={},humanId={}",refreshTime,now,conf.sn,itemNum,add,itemMax,humanObj.id);
				return;
			}
			humanObj.startCheckItemRecover((refreshTime-now)*Time.SEC, conf.sn);
		} else {
			ItemManager.inst().setRefreshGoodNextTime(humanObj,conf.sn,0);
		}
	}

	public void _msg_goods_use_c2s(HumanObject humanObj, long goodsId, int num, List<Define.p_key_value> extList) {
		_msg_goods_use_c2s(humanObj, goodsId, num, 0, 0, extList);
	}

	public void _msg_goods_use_c2s(HumanObject humanObj, long goodsId, int num, int chooseGtId, int chooseUse) {
		_msg_goods_use_c2s(humanObj, goodsId, num, chooseGtId, chooseUse, new ArrayList<>());
	}

	public void _msg_goods_use_c2s(HumanObject humanObj, long goodsId, int num, int chooseGtId, int chooseUse, List<Define.p_key_value> extList) {
		ConfGoods conf = ConfGoods.get((int)goodsId);
		if (conf == null){
			Log.temp.error("ConfGoods not found sn:{}", goodsId);
			return;
		}
		if(num <= 0 || num == Integer.MAX_VALUE){
			return;
		}
		doUseGoodsLogic(humanObj, conf, num, chooseGtId, extList);
	}



	/**
	 * 执行使用物品逻辑
	 * @param humanObj
	 * @param conf
	 * @param num
	 * @param chooseGtId
	 * @param extList
	 * @return
	 */
	private void doUseGoodsLogic(HumanObject humanObj, ConfGoods conf, int num, int chooseGtId, List<Define.p_key_value> extList){
		MsgGoods.goods_use_s2c.Builder msg = MsgGoods.goods_use_s2c.newBuilder();
		// 能否使用道具
		ReasonResult result = ItemUtils.canUseGoods(humanObj, conf, num, chooseGtId);
		if(!result.success){
			msg.setCode(ErrorTip.ItemNotEnough);
			humanObj.sendMsg(msg);
			Inform.sendMsg_error(humanObj, result.code);
			return;
		}
		// 使用物品的特殊逻辑，直接写在这里
		if (conf.type == ItemConstants.活动可用道具) {
			ReasonResult rr = ProduceManager.inst().checkAndCostItem(humanObj, conf.sn, num, MoneyItemLogKey.道具使用);
			if (!rr.success) {
				msg.setCode(ErrorTip.ItemNotEnough);
				humanObj.sendMsg(msg);
				return;
			}
			int actType = conf.effect[0][0];
			if (!ActivityManager.inst().isActivityOpen(humanObj, actType)) {
				Log.activity.error("使用了活动道具, 但活动没有开启, humanId={}, sn={}, actType={}", humanObj.getHuman().getId(), conf.sn, actType);
				return;
			}
			IActivityControl control = ActivityControlTypeFactory.getTypeData(actType);
			if (control == null) {
				Log.activity.error("使用了活动道具, 但找不到对应的处理类, humanId={}, sn={}, actType={}", humanObj.getHuman().getId(), conf.sn, actType);
				return;
			}
			control.itemUse(humanObj, conf.sn, num);
		}
		else{
			// 执行使用物品会得到物品的通用逻辑
			doUseGoodsGiveItemLogic(humanObj, conf, num, chooseGtId, extList);
		}
	}

	/**
	 * 执行使用物品会得到物品的通用逻辑
	 * @param humanObj
	 * @param conf
	 * @param num
	 * @param chooseGtId
	 * @param extList
	 * @return
	 */
	private void doUseGoodsGiveItemLogic(HumanObject humanObj, ConfGoods conf, int num, int chooseGtId, List<Define.p_key_value> extList){
		MsgGoods.goods_use_s2c.Builder msg = MsgGoods.goods_use_s2c.newBuilder();
		// 计算获得的物品
		Map<Integer, Integer> rewardMap = ItemUtils.calcUseGoodsReward(conf, num, chooseGtId, extList);
		if(rewardMap == null || rewardMap.isEmpty()){
			Log.item.warn("使用道具错误，获得的物品数量为0");
			return;
		}
		// 先扣除道具本身
		ReasonResult rr = ProduceManager.inst().checkAndCostItem(humanObj, conf.sn, num, MoneyItemLogKey.道具使用);
		if(!rr.success){
			msg.setCode(ErrorTip.ItemNotEnough);
			humanObj.sendMsg(msg);
			Inform.sendMsg_error(humanObj, ErrorTip.ItemNotEnough);
			return;
		}
		// 再给获得的物品
		ProduceManager.inst().produceAdd(humanObj, rewardMap, MoneyItemLogKey.道具使用);
		if(conf.type != ItemConstants.战令){
			InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewardMap);
		}

		msg.setCode(0);
		msg.setGoodsId(conf.sn);
		List<Define.p_reward.Builder> rewardList = ItemUtils.to_p_reward_list(humanObj, rewardMap);
		for (Define.p_reward.Builder rewardBuilder : rewardList) {
			msg.addGoodsList(rewardBuilder.build());
		}
		humanObj.sendMsg(msg);
	}

}
