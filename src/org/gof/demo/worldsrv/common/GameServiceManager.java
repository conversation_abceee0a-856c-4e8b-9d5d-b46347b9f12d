package org.gof.demo.worldsrv.common;

import org.gof.core.Node;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.support.*;
import org.gof.core.support.observer.Listener;
import org.gof.core.utils.StrUtil;
import org.gof.demo.seam.main.ServCheck;
import org.gof.demo.worldsrv.arena.ArenaCrossService;
import org.gof.demo.worldsrv.arena.ArenaCrossServiceProxy;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.observer.EventKey;

import java.lang.reflect.InvocationTargetException;
import java.util.*;

public class GameServiceManager extends ManagerBase {

	public List<String> services = new ArrayList<>();

	/**
	 * 游戏启动时 开启本服务
	 * @param params
	 * @throws Exception 
	 */
	@Listener(EventKey.GAME_STARTUP_BEFORE)
	public void onGameStartupBefore(Param params) {
		Log.game.info("开始初始化service");

		try{
		Node node = params.get("node");
		
		for(int i = 0; i < D.PORT_GAME_STARTUP_NUM; i++) {
			//拼PortId
			String portId = D.PORT_GAME_PREFIX + i;
			
			//验证启动Node
			String nodeId = Distr.getNodeId(portId);
			if(!node.getId().equals(nodeId)) {
				continue;
			}
			
			//启动服务
			GamePort portGlobal = new GamePort(portId);
			portGlobal.startup(node);
			
			//初始化下属服务
			initService(portGlobal);
		}
		}catch(Exception e){
			throw new SysException(e);
		}
	}
	
	/**
	 * 初始化下属服务
	 * @throws SecurityException 
	 * @throws NoSuchMethodException 
	 * @throws InvocationTargetException 
	 * @throws IllegalArgumentException 
	 * @throws IllegalAccessException 
	 * @throws Exception 
	 */
	private void initService(GamePort port)  {
		// 获取源文件夹下的所有类
		Set<Class<?>> clazzSet = PackageClass.find(true);
		Map<Class<?>,String> servIdMap = new HashMap<>();
		// 遍历所有类，取出ServiceCommonBase的子类，并进行初始化
		for(Class<?> clazz : clazzSet) {
			//只处理GameServiceBase的子类
			if(!GameServiceBase.class.isAssignableFrom(clazz)) {
				continue;
			}
			
			//必须有@DistrClass注解
			if(!clazz.isAnnotationPresent(DistrClass.class)) {
				continue;
			}
			
			//根据注解信息查看是否为本Port启动
			DistrClass anno = clazz.getAnnotation(DistrClass.class);

			//进行初始化
			try {
				GameServiceBase serv = null;
				String servId = anno.servId();

				// 是否不启动服务
				if(isContinue(servId)){
					Log.game.info("{}服不启动服务：{}", Config.SERVER_ID, servId);
					continue;
				}

				if (StrUtil.isNullOrEmpty(servId)) {
					servId = servIdMap.get(clazz);
					if(servId==null) {
						serv = (GameServiceBase) clazz.getConstructor(GamePort.class).newInstance(port);
						servId = serv.getId().toString();
						servIdMap.put(clazz,servId);
					}
				}
				String portId = Distr.getPortId(servId);
				if (!port.getId().equals(portId)) {
					continue;
				}
				if (serv == null) {
					serv = (GameServiceBase) clazz.getConstructor(GamePort.class).newInstance(port);
				}
				//标记记录一下
				ServCheck.addServ(servId);
				serv.startupLocal();
			} catch (Exception e) {
				throw new SysException(e);
			}
		}
	}

	private boolean isContinue(String servId){
		if(Utils.isDebugMode()){
			return false;
		}
		if(S.isCross){
			Integer crossServerId = Config.SERVER_ID - GlobalConfVal.cross_server_id_base;
			if(crossServerId == 0){
				if(!S.isAdmin){
					Log.temp.error("===配置错误， serverId={}, crossServerId={}, servId={}", Config.SERVER_ID, crossServerId, servId);
				}
				return true;
			}
			if(D.SERV_ARENA_RANKED.equals(servId) || D.SERV_MATCH_ALLOT.equals(servId)){
				ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.跨服排位赛功能运行跨服集合.SN);
				if(confGlobal == null || confGlobal.intArray == null){
					return true;
				}
				List<Integer> crossList = Utils.intArrToList(confGlobal.intArray);
				if(!crossList.contains(crossServerId)) {
					return true;
				}
				return false;
			}

			if(D.SERV_GUILD_LEAGUE.equals(servId)){
				ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.跨服乱斗功能运行跨服集合.SN);
				if(confGlobal == null || confGlobal.intArray == null){
					return true;
				}
				List<Integer> crossList = Utils.intArrToList(confGlobal.intArray);
				if(!crossList.contains(crossServerId)) {
					return true;
				}
				return false;
			}

			if(D.SERV_ARENA_CROSS.equals(servId)){
				ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.跨服竞技场功能运行跨服集合.SN);
				if(confGlobal == null || confGlobal.intArray == null){
					return true;
				}
				List<Integer> crossList = Utils.intArrToList(confGlobal.intArray);
				if(!crossList.contains(crossServerId)) {
					return true;
				}
				return false;
			}

			if(D.SERV_CROSS.equals(servId)){
				ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.跨服战功能运行跨服集合.SN);
				if(confGlobal == null || confGlobal.intArray == null){
					return true;
				}
				List<Integer> crossList = Utils.intArrToList(confGlobal.intArray);
				if(!crossList.contains(crossServerId)) {
					return true;
				}
				return false;
			}

			if (D.SERV_ACTIVITY_CROSS.equals(servId)) {
				ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.跨服活动服务器开启区间.SN);
				if (confGlobal == null || confGlobal.intArray == null) {
					return true;
				}
				Set<Integer> crossList = new HashSet<>(Utils.intArrToList(confGlobal.intArray));
				// 跨服黑市的服务器也要启动跨服活动
				ConfGlobal confGlobal1 = ConfGlobal.get(ConfGlobalKey.跨服黑市服务器开启区间.SN);
				if (confGlobal1 != null && confGlobal1.intArray != null) {
					crossList.addAll(Utils.intArrToList(confGlobal1.intArray));
				}
				return !crossList.contains(crossServerId);
			}

			if (D.SERV_BLACK_MARKET.equals(servId)) {
				ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.跨服黑市服务器开启区间.SN);
				if (confGlobal == null || confGlobal.intArray == null) {
					return true;
				}
				List<Integer> crossList = Utils.intArrToList(confGlobal.intArray);
				return !crossList.contains(crossServerId);
			}
		}

		return false;
	}

}
