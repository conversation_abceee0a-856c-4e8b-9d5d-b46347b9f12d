package org.gof.demo.worldsrv.common;

import com.alibaba.fastjson.JSONArray;
import io.vertx.core.json.JsonArray;
import org.apache.commons.collections.CollectionUtils;
import org.gof.core.Port;
import org.gof.core.Record;
import org.gof.core.Service;
import org.gof.core.dbsrv.DB;
import org.gof.core.dbsrv.entity.IdAllot;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.dbsrv.redis.Tool;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.core.scheduler.ScheduleMethod;
import org.gof.core.scheduler.ScheduleTask;
import org.gof.core.support.*;
import org.gof.core.support.S;
import org.gof.demo.support.DataPreloader;
import org.gof.demo.support.IPersistObj;
import org.gof.demo.support.statistics.StatisticsHuman;
import org.gof.demo.support.statistics.StatisticsStage;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.config.ConfNews;
import org.gof.demo.worldsrv.config.ConfServerGroup;
import org.gof.demo.worldsrv.entity.ServerGlobal;
import org.gof.demo.worldsrv.entity.ServerNews;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.human.HumanSettingConstants;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.inform.InformManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.*;
import org.gof.demo.worldsrv.entity.RemoveHuman;
import org.gof.demo.worldsrv.entity.ServerGlobal;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.guild.GuildService;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.mail.MailManager;
import org.gof.demo.worldsrv.rank.RankInfo;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.*;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.HumanScopeKey;
import org.gof.demo.worldsrv.support.enumKey.NewsConditionTypeKey;
import org.gof.platform.ConstPf;

import java.util.*;


@DistrClass(servId = D.SERV_GAME_VALUE, importClass = {List.class, Map.class, ServerGlobal.class,JSONArray.class}, localOnly = false)
public class GameService extends Service {

    private TickTimer timer = new TickTimer(Time.MIN * 10);

    private static List<String> lotteryRecordList = new ArrayList<>();

    private static final String PURCHASE = "purchaseLimit/lottery";

    private static long endTime  = 0L;

    private static final String HTTPS = Config.GAME_GMT_IP;
//    private static final String HTTPS = "http://*************:4002/";

    private static final String Rank = "rankRecord/hour";


    private static final Map<Integer, ServerGlobal> serverDataMap = new HashMap<>();
    // Map<服id, Map<组id, List<玩家id>>>
    private static final Map<Integer, Map<Integer, List<Long>>> serverNewsHumanIdMap = new HashMap<>();
    // Map<服id, Map<组id, ServerNews>
    private static final Map<Integer, Map<Integer, ServerNews>> serverNewsMap = new HashMap<>();

    private List<Integer> serverIdListNow = new ArrayList<>();



    private TickTimer checkServerIdTT = new TickTimer(5 * Time.MIN);//调度的处理


    @Override
    public Object getId() {
        return ConstPf.SERV_GAME_VALUE;
    }

    /**
     * 构造函数
     *
     * @param port
     */
    public GameService(Port port) {
        super(port);
    }

    public void init() {
        Log.temp.info("===初始化游戏服务 gameValue");
        // 起服的时候也要更新一下redis过期时间设置
        updateReidsExprieTimeVariable();
        checkServerId();
        loadRomoveHuman();
        loadServerNews();
        loadRemoveId();
        Log.temp.info("===删除玩家id数量总，{}", GlobalConfVal.getRemoveIdSet().size());
    }


    public void loadRemoveId(){
        PersistRemoveHuman persist = new PersistRemoveHuman();
        DB db = DB.newInstance(persist.getTable());
        db.countAll(false);
        Param paramCount = db.waitForResult();
        if(paramCount == null){
            Log.temp.info("===db={}, tableName={}", db, persist.getTable());
            return;
        }
        int count = paramCount.get();
        if (count > 0) {
            int page = count / persist.getPageNum();
            for (int i = 0; i <= page; i++) {
                db.findBy(false, i * persist.getPageNum(), persist.getPageNum());
                Param params = db.waitForResult();
                List<Record> records = params.get();
                for (Record r : records) {
                    persist.load(r);
                }
            }
        }
    }
    private class PersistRemoveHuman implements IPersistObj {
        public String getTable() {
            return RemoveHuman.tableName;
        }

        public int getPageNum() {
            return 1000;
        }

        public void load(Record r) {
            RemoveHuman removeHuman = new RemoveHuman(r);
            GlobalConfVal.addRemoveId(removeHuman.getId());
        }
    }


    private void loadServerNews() {
        List<Integer> serverList = Util.getServerTagList(C.GAME_SERVER_ID);
        if (serverList.isEmpty()) {
            Log.temp.error("===没有获取到serverId列表，请检查配置,{}", C.GAME_SERVER_ID);
            return;
        }
        serverNewsHumanIdMap.clear();
        serverNewsMap.clear();
        for (Integer serverId : serverIdListNow) {
//            int serverId = C.GAME_SERVER_ID;
            DB db = DB.newInstance(ServerNews.tableName);
            db.findBy(false, ServerNews.K.serverId, serverId);
            Param param = db.waitForResult();
            if (param == null) {
                Log.game.info("[新闻播报]起服查询数据为空, serverId={}", serverId);
                return;
            }
            List<Record> recordList = param.get();
            if (recordList == null || recordList.isEmpty()) {
                Log.game.info("[新闻播报]起服查询数据为空, serverId={}", serverId);
                return;
            }
            Log.game.info("[新闻播报]起服查询数据, serverId={}, 数据量={}", serverId, recordList.size());
            Map<Integer, ServerNews> groupNewsMap = new HashMap<>();
            Map<Integer, List<Long>> groupHumanIdMap = new HashMap<>();
            for (Record record : recordList) {
                ServerNews news = new ServerNews(record);
                groupNewsMap.put(news.getGroupId(), news);
                groupHumanIdMap.put(news.getGroupId(), Utils.strToLongList(news.getHumanIdList()));
            }
            serverNewsMap.put(serverId, groupNewsMap);
            serverNewsHumanIdMap.put(serverId, groupHumanIdMap);
        }
    }

    private void loadRomoveHuman(){
        DB db = DB.newInstance(RemoveHuman.tableName);
        db.countAll(false);
        Param paramCount = db.waitForResult();
        if(paramCount == null){
            Log.temp.info("===db={}, tableName={}", db, RemoveHuman.tableName);
            return;
        }
        int count = paramCount.get();
        if (count <= 0) {
            Log.temp.info("===没有需要删除的玩家id");
            return;
        }
        int page = count / ParamKey.pageNum;
        for (int i = 0; i <= page; i++) {
            int value = i;
            scheduleOnce(new ScheduleTask() {
                @Override
                public void execute() {
                    db.findBy(false, value * ParamKey.pageNum, ParamKey.pageNum);
                    db.listenResult(this::_result_loadRomoveHuman);
                }
                private void _result_loadRomoveHuman(Param results, Param context) {
                    List<Record> records = results.get();
                    if(records == null || records.isEmpty()){
                        return;
                    }
                    for(Record record : records){
                        RemoveHuman human = new RemoveHuman(record);
                        GlobalConfVal.addRemoveId(human.getId());
                    }
                }
            }, (i / ParamKey.pageNum) * 2000L); // 每2秒发送一批
        }

    }

    @DistrMethod
    public void loadServer(List<Integer> serverIdList){
        Collection<Integer> diffAdd = CollectionUtils.subtract(serverIdList, serverIdListNow);
        List<Long> addServers = new ArrayList<>();
        for(int serverId : diffAdd){
            if(serverIdListNow.contains(serverId)){
                continue;
            }
            serverIdListNow.add(serverId);
            addServers.add(Long.valueOf(serverId));
        }
        EntityManager.batchGetEntity(ServerGlobal.class, addServers,res->{
            if(res.failed()){
                Log.game.error(" batch load ServerGlobal data failed, serverIds={}", addServers,res.cause());
                return;
            }
            List<ServerGlobal> result = res.result();
            Set<Long> findIds = new HashSet<>();
            if(result!=null && !result.isEmpty()){
                for(ServerGlobal serverGlobal : result){
                    if(serverGlobal != null) {
                        long longId = serverGlobal.getId();
                        int serverId = (int) longId;

                        findIds.add(longId);
                        serverDataMap.put(serverId, serverGlobal);
                        if (serverGlobal.getDarkTrialChapterSn() == 0 || !Utils.isSameDay(Port.getTime(), serverGlobal.getDarkTrialTime())) {
                            updateDarkTrialSn(serverId);
                        }
                    }
                }
            }
            for (Long longId : addServers) {
                if (!findIds.contains(longId)) {
                    int serverId = longId.intValue();
                    ServerGlobal serverGlobal = createServerGlobal(serverId);
                    if (serverGlobal.getDarkTrialChapterSn() == 0 || !Utils.isSameDay(Port.getTime(), serverGlobal.getDarkTrialTime())) {
                        updateDarkTrialSn(serverId);
                    }
                }
            }
        });

    }

    private ServerGlobal createServerGlobal(int serverId){
        if(serverDataMap.containsKey(serverId)){
            return serverDataMap.get(serverId);
        }
        ServerGlobal serverGlobal = new ServerGlobal();
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.back_trial_chapter_order.SN);
        serverGlobal.setId(serverId);
        serverGlobal.setDarkTrialChapterSn(confGlobal.intArray[0]);
        serverGlobal.setDarkTrialTime(Port.getTime());
        serverGlobal.persist();
        serverDataMap.put(serverId, serverGlobal);
        if(!serverIdListNow.contains(serverId)){
            serverIdListNow.add(serverId);
        }
        return serverGlobal;
    }

    @ScheduleMethod(DataResetService.CRON_DAY_ZERO)
    public void _CRON_DAY_ZERO() {
        // 每天0点更新一下redis过期时间设置
        updateReidsExprieTimeVariable();
        RedisTools.getFullSet(EntityManager.redisClient, RedisKeys.daily_remove_log, ret->{
            if(ret.failed()){
                return;
            }
            JsonArray jsonArray = ret.result();
            List<String> removeKeyList = new ArrayList<>();
            for(int i = 0; i < jsonArray.getList().size(); i++){
                removeKeyList.add(String.valueOf(jsonArray.getList().get(i)));
            }
            RedisTools.del(removeKeyList);
        });
        for (int serverId : serverIdListNow) {
            updateDarkTrialSn(serverId);
        }
    }

    /**
     * 根据开服天数变动redis过期时间
     * 1-3天: 6小时
     * 3-5天: 1天
     * 5-7天: 3天
     * 大于7: 7天
     */
    private void updateReidsExprieTimeVariable() {
//        int day = Utils.getDaysBetween(Port.getTime(), Util.getOpenServerTime(C.GAME_SERVER_ID)) + 1;
//        if (1 <= day && day < 3) {
//            EntityManager.REDIS_EXPIRE_TIME = 6 * Tool.HOUR;
//        } else if (3 <= day && day < 5) {
//            EntityManager.REDIS_EXPIRE_TIME = Tool.DAY;
//        } else if (5 <= day && day < 7) {
//            EntityManager.REDIS_EXPIRE_TIME = 3 * Tool.DAY;
//        } else if (7 <= day) {
//            EntityManager.REDIS_EXPIRE_TIME = 7 * Tool.DAY;
//        } else {
//            EntityManager.REDIS_EXPIRE_TIME = 7 * Tool.DAY;
//        }
        EntityManager.REDIS_EXPIRE_TIME = 7 * Tool.DAY;
    }

    private void updateDarkTrialSn(int serverId) {
        if (!serverIdListNow.contains(serverId)) {
            return;
        }
        ServerGlobal serverGlobal = serverDataMap.get(serverId);
        if (serverGlobal == null) {
            EntityManager.getEntityAsync(ServerGlobal.class, serverId, res->{
                if(res.failed()){
                    Log.game.error("updateDarkTrialSn.getEntityAsync failed, entity=ServerGlobal,serverId={}",serverId,res.cause());
                    return;
                }
                ServerGlobal servGlobal =res.result();
                if (servGlobal == null) {
                    servGlobal = createServerGlobal(serverId);
                }
                updateDarkTrialSn(servGlobal);
            });
        } else {
            updateDarkTrialSn(serverGlobal);
        }
    }

    private void updateDarkTrialSn(ServerGlobal serverGlobal){
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.back_trial_chapter_order.SN);
        int newSn = confGlobal.intArray[0];
        for(int i = 0; i < confGlobal.intArray.length; i++){
            if(serverGlobal.getDarkTrialChapterSn() == confGlobal.intArray[i]){
                int index = i+1;
                if(index >= confGlobal.intArray.length){
                    index = 0;
                }
                newSn = confGlobal.intArray[index];
                break;
            }
        }
        serverGlobal.setDarkTrialChapterSn(newSn);
        serverGlobal.setDarkTrialTime(Port.getTime());
        serverGlobal.update();
    }


    @Override
    public void pulseOverride() {
        // 统计信息的输出
        long nowTime = port.getTimeCurrent();
        if(timer.isPeriod(nowTime)){
            StatisticsHuman.showResult();
            StatisticsStage.showResult();
        }

        if(checkServerIdTT.isPeriod(nowTime)){
            checkServerId();
        }

    }

    private void checkServerId(){
        List<Integer> serverList = Util.getServerTagList(C.GAME_SERVER_ID);
        if(serverList.isEmpty()){
            Log.temp.error("===没有获取到serverId列表，请检查配置,{}", C.GAME_SERVER_ID);
            return;
        }
        for(Integer serverId : serverList){
            if(!serverIdListNow.contains(serverId)){
                serverIdListNow.add(serverId);
                DB db = DB.newInstance(ServerGlobal.tableName);
                db.getBy(true, ServerGlobal.K.id, serverId);
                Param param = db.waitForResult();
                if(param == null && S.isRedis){
                    ServerGlobal serverGlobal = EntityManager.getEntity(ServerGlobal.class, serverId);
                    if(serverGlobal != null){
                        serverGlobal.persist();
                        serverDataMap.put(serverId, serverGlobal);
                        if(serverGlobal.getDarkTrialChapterSn() == 0 || !Utils.isSameDay(Port.getTime(), serverGlobal.getDarkTrialTime())){
                            updateDarkTrialSn(serverId);
                        }
                        continue;
                    }
                }
                if(param == null){
                    createServerGlobal(serverId);
                    continue;
                }
                Record r = param.get();
                if(r == null){
                    createServerGlobal(serverId);
                    continue;
                }
                ServerGlobal serverGlobal = new ServerGlobal(r);
                serverDataMap.put(serverId, serverGlobal);
                if(serverGlobal.getDarkTrialChapterSn() == 0 || !Utils.isSameDay(Port.getTime(), serverGlobal.getDarkTrialTime())){
                    updateDarkTrialSn(serverId);
                }
            }
        }

        Log.game.info("服务器列表serverIdListNow={}, serverList={}", serverIdListNow, serverList);
        Collection<Integer> diffAdd = (Collection<Integer>)CollectionUtils.subtract(serverIdListNow, serverList);
        if (diffAdd.isEmpty()) {
            return;
        }
        serverList.clear();
        Log.temp.error("===本服id={},  已经下线serverIds={}", C.GAME_SERVER_ID, diffAdd);
    }

    @DistrMethod
    public void getServerGlobal(int serverIdTemp){
        int serverId = Util.getServerIdReal(serverIdTemp);
        if (!serverIdListNow.contains(serverId)) {
            port.returns("result", new ReasonResult(false, ErrorTip.SystemDefault));
            return;
        }
        ServerGlobal serverGlobal = serverDataMap.computeIfAbsent(serverId, k -> createServerGlobal(serverId));
        if (serverGlobal.getDarkTrialChapterSn() == 0 || !Utils.isSameDay(Port.getTime(), serverGlobal.getDarkTrialTime())) {
            updateDarkTrialSn(serverId);
        }
        port.returns("result", new ReasonResult(true), "serverGlobal", serverGlobal);
    }

    @DistrMethod
    public void checkAndSendNews(long humanId, String humanName, int serverId, int condType, int condValue, boolean isHideName) {
        serverId = Util.getServerIdReal(serverId);
        if (!serverIdListNow.contains(serverId)) {
            Log.game.error("[新闻播报]服务器id不属于服务器列表, serverId={}, serverIdListNow={}", serverId, Utils.listToString(serverIdListNow));
            return;
        }
        if (condType == NewsConditionTypeKey.MineDrill || condType == NewsConditionTypeKey.StatueQuality) {
            // 判定方式为>=，一次满足多少条就触发多少条
            if (!GlobalConfVal.newsConditionValueListMap.containsKey(condType)) {
                return;
            }
            List<Integer> conditionAllValueList = GlobalConfVal.newsConditionValueListMap.get(condType);
            List<Integer> conditionValueList = new ArrayList<>();
            for (Integer value : conditionAllValueList) {
                if (value > condValue) {
                    break;
                }
                conditionValueList.add(value);
            }
            if (conditionValueList.isEmpty()) {
                return;
            }
            List<String> contentList = new ArrayList<>();
            for (Integer value : conditionValueList) {
                String content = singleCheckAndSendNews(humanId, humanName, serverId, condType, value);
                if (content == null || content.isEmpty()) {
                    continue;
                }
                contentList.add(content);
            }
            List<Define.p_key_value_string> argsList = new ArrayList<>();
            argsList.add(Define.p_key_value_string.newBuilder().setK(HumanSettingConstants.BroadCastHideName).setV(isHideName ? 1 : 0).build());
            InformManager.inst().addNewsChatHistory(serverId, 7, contentList, argsList);
            for (String content : contentList) {
                InformManager.inst().sendMsgChatMessage(HumanScopeKey.ALL, 0, Inform.新闻, 1, serverId, 7, content, null, argsList);
            }
        } else {
            // 判定方式为==，一次只触发一条
            // 1.既然是==, 剔除掉值已经超过最大的情况
            if (condType != NewsConditionTypeKey.MountUnlock
                    && condType != NewsConditionTypeKey.WingUnlock
                    && condType != NewsConditionTypeKey.ArtifactUnlock) {
                // 除这几个类型，其他需要做拦截最小最大
                int minValue = GlobalConfVal.newsMinConditionMap.get(condType);
                if (condValue < minValue) {
                    return;// 条件值小于配表内最小条件值，不做处理
                }
                int maxValue = GlobalConfVal.newsMaxConditionMap.get(condType);
                if (condValue > maxValue) {
                    return;// 条件值大于配表内最大条件值，不做处理
                }
            }
            String content = singleCheckAndSendNews(humanId, humanName, serverId, condType, condValue);
            if (content != null && !content.isEmpty()) {
                List<Define.p_key_value_string> argsList = new ArrayList<>();
                argsList.add(Define.p_key_value_string.newBuilder().setK(HumanSettingConstants.BroadCastHideName).setV(isHideName ? 1 : 0).build());
                InformManager.inst().sendMsgChatMessage(HumanScopeKey.ALL, 0, Inform.新闻, 1, serverId, 7, content, null, argsList);
                InformManager.inst().addNewsChatHistory(serverId, 7, Collections.singletonList(content), argsList);
            }
        }
    }

    /**
     * 单条播报发送抽出方法
     */
    private String singleCheckAndSendNews(long humanId, String humanName, int serverId, int condType, int condValue) {
        serverId = Util.getServerIdReal(serverId);
        // 2.根据条件类型和条件值取到组id
        String conditionKey = Utils.createStr("{}_{}", condType, condValue);
        if (!GlobalConfVal.newsGroupMap.containsKey(conditionKey)) {
            return null;// 说明条件值没有配置，也不做处理
        }
        int group = GlobalConfVal.newsGroupMap.get(conditionKey);
        // 3.判断已上榜人数是不是超了
        int rank = 0;
        int maxRank = GlobalConfVal.newsGroupMaxRankMap.get(group);
        if (maxRank != 0) {
            Map<Integer, List<Long>> newHumanIdMap = serverNewsHumanIdMap.getOrDefault(serverId, new HashMap<>());
            List<Long> humanIdList = newHumanIdMap.getOrDefault(group, new ArrayList<>());
            if (humanIdList.size() >= maxRank) {
                return null;// 已经达到最大播报次数，不做处理
            }
            if (humanIdList.contains(humanId)) {
                return null;// 已经播报过，不做处理
            }
            rank = humanIdList.size() + 1;
            humanIdList.add(humanId);
            newHumanIdMap.put(group, humanIdList);
            serverNewsHumanIdMap.put(serverId, newHumanIdMap);
            updateServerNews(serverId, group);
        }
        Log.game.info("[新闻播报]触发，humanId={}, humanName={}, serverId={}, condType={}, condValue={}", humanId, humanName, serverId, condType, condValue);
        // 4.根据组id和排名取到新闻表sn
        String newsSnKey = Util.createStr("{}_{}", group, rank);
        int newsSn = GlobalConfVal.newsGroupSnMap.get(newsSnKey);
        String content = Utils.createStr("{}_{}", newsSn, humanName);
        return content;
    }

    private void updateServerNews(int serverId, int group) {
        Map<Integer, List<Long>> groupHumanIdMap = serverNewsHumanIdMap.getOrDefault(serverId, new HashMap<>());
        List<Long> humanIdList = groupHumanIdMap.getOrDefault(group, new ArrayList<>());
        String humanIdListStr = Utils.listToString(humanIdList);

        Map<Integer, ServerNews> groupNewsMap = serverNewsMap.computeIfAbsent(serverId, k -> new HashMap<>());
        ServerNews entity = groupNewsMap.get(group);
        if (entity == null) {
            entity = new ServerNews();
            entity.setId(Port.applyId());
            entity.setServerId(serverId);
            entity.setGroupId(group);
            entity.setHumanIdList(humanIdListStr);
            entity.persist();
        } else {
            entity.setHumanIdList(humanIdListStr);
        }
        groupNewsMap.put(group, entity);
    }

    @DistrMethod
    public void gmClearNews(int serverId, int condType) {
        Set<Integer> groupSet = new HashSet<>();
        for (ConfNews conf : ConfNews.findAll()) {
            if (conf.condition == condType || condType == 0) {
                groupSet.add(conf.group);
            }
        }
        Log.game.info("[新闻播报]GM删除清除, serverId={}, groupSet={}", serverId, Utils.setToString(groupSet));
        Map<Integer, ServerNews> newsEntityMap = serverNewsMap.get(serverId);
        Map<Integer, List<Long>> humanIdListMap = serverNewsHumanIdMap.get(serverId);
        for (int group : groupSet) {
            if (humanIdListMap != null) {
                humanIdListMap.remove(group);
            }
            if (newsEntityMap != null && newsEntityMap.containsKey(group)) {
                ServerNews entity = newsEntityMap.remove(group);
                long id = entity.getId();
                entity.remove();
                Log.game.info("[新闻播报]GM删除ServerNews, serverId={}, group={}, id={}", serverId, group, id);
            }
        }
        String key = Utils.createStr("{}_server:{}", (RedisKeys.chatChannelLog + Inform.新闻), serverId);
        RedisTools.del(EntityManager.redisClient, key);
    }

    @DistrMethod
    public void update(Object... obj){
        updateDarkTrialSn(Utils.intValue(obj[0]));
    }

    @DistrMethod
    public void update1(String json){

    }

    @DistrMethod
    public void update2(Param param){

    }

    @ScheduleMethod(DataResetService.CRON_WEEK_MON_FIVE_HOUR)
    public void CRON_WEEK_MON_FIVE_HOUR() {
        List<String> removeRedisKeyList = new ArrayList<>();
//        removeRedisKeyList.add(RedisKeys.guild_league_week_road_log+"*");
//        removeRedisKeyList.add(RedisKeys.world_nodeid_addr+"*");
//        removeRedisKeyList.add(RedisKeys.bridge_nodeid_addr+"*");
//        removeRedisKeyList.add(RedisKeys.bridge_nodeid_addr_pvp+"*");
        removeRedisKeyList.add(RedisKeys.guild_human_kill+"*");

        RedisTools.getFullSet(EntityManager.redisClient, RedisKeys.week_remove_log, ret->{
            if(ret.failed()){
                return;
            }
            JsonArray jsonArray = ret.result();
            List<String> removeKeyList = new ArrayList<>();
            for(int i = 0; i < jsonArray.getList().size(); i++){
                removeKeyList.add(String.valueOf(jsonArray.getList().get(i)));
            }
            RedisTools.del(removeKeyList);

        });
        RedisTools.getFullSet(EntityManager.redisClient, RedisKeys.guild_league_remove_list, ret->{
            if(ret.failed()){
                return;
            }
            JsonArray jsonArray = ret.result();
            List<String> removeKeyList = new ArrayList<>();
            for(int i = 0; i < jsonArray.getList().size(); i++){
                removeKeyList.add(String.valueOf(jsonArray.getList().get(i)));
            }
            RedisTools.del(removeKeyList);
        });


        // TODO 待定
//        keyList.add(RedisKeys.bridge_date_group_grade_human_rank+"*");
//        keyList.add(RedisKeys.bridge_date_group_grade_guild_rank+"*");
//        keyList.add(RedisKeys.bridge_pvp_group_guild_rank+"*");
//        keyList.add(RedisKeys.bridge_pvp_group_human_rank+"*");
//        keyList.add(RedisKeys.bridge_pvp_guild_top_log+"*");


        EntityManager.redisClient.del(removeRedisKeyList, h-> {
            if (h.succeeded()) {
                Log.temp.info("===清除竞技场周排行榜成功。keyList={}", removeRedisKeyList);
            }
        });
    }

    @DistrMethod
    public void getMergeVer(int serverId){
        ServerGlobal serverGlobal = serverDataMap.computeIfAbsent(serverId, k -> createServerGlobal(serverId));
        port.returns(serverGlobal.getServerMergeVer());
    }

}
