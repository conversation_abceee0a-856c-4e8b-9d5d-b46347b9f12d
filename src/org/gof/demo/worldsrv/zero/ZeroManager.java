package org.gof.demo.worldsrv.zero;

import org.gof.core.Port;
import org.gof.core.support.ManagerBase;
import org.gof.core.support.Param;
import org.gof.core.support.observer.Listener;
import org.gof.demo.worldsrv.accumulatedRecharge.AccumulatedRechargeManager;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.back.BackManager;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.charm.CharmManager;
import org.gof.demo.worldsrv.guild.GuildManager;
import org.gof.demo.worldsrv.home.Fish.HomeFishManager;
import org.gof.demo.worldsrv.mall.MallManager;
import org.gof.demo.worldsrv.mall.PayMallTypeKey;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskManager;

/**
 * 如果需要顺序要求，这里的一定是最后的
 */
public class ZeroManager extends ManagerBase {
    /**
     * 获取实例
     *
     * @return
     */
    public static ZeroManager inst() {
        return inst(ZeroManager.class);
    }


    @Listener(EventKey.HUMAN_RESET_ZERO)
    public void _HUMAN_RESET_ZERO(Param param) {
        HumanObject humanObj = param.get("humanObj");
        //long timeOpen = System.nanoTime();
        humanObj.getHuman3().setZeroResetTime(Port.getTime());
        // 公会相关数据
        GuildManager.inst().sendGuildZeroInfo(humanObj);
        // 0点，在sendMsg_task_all_s2c之前，检查武道会任务，因为可能会赛季重置
        humanObj.operation.taskData.checkKungFuRaceTask(humanObj);
        HomeFishManager.inst().resetDaily(humanObj);
        TaskManager.inst().sendMsg_task_all_s2c(humanObj);
        TaskManager.inst().sendMsg_task_daily_point_s2c(humanObj);
        // 检查回归任务
        humanObj.operation.taskData.checkBackTask(humanObj, false);
        BackManager.inst().sendReturnInfo(humanObj);
        MallManager.inst()._msg_shop_info_c2s(humanObj, MallManager.MallType_Gem);

        ActivityManager.inst().checkActivityCloseOrEndShow(humanObj);
        humanObj.checkHumanActivityOpen();
        ActivityManager.inst().dailyResetActivityData(humanObj);
        // 推活动日历信息
        ActivityManager.inst().sendMsg_calendar_info_s2c(humanObj);
        MallManager.inst().sendMsg_pay_mall_info_s2c(humanObj);
        MallManager.inst().sendMsg_pay_mall_info_s2c(humanObj, PayMallTypeKey.PAY_Type_7);
        // 0点刷新累充轮次
        AccumulatedRechargeManager.inst().refreshAccumulatedRechargeRound(humanObj);
        // 推累充info
        AccumulatedRechargeManager.inst()._msg_accumulated_recharge_info_c2s(humanObj);
        // 发送美观值信息
        CharmManager.inst().sendCharmInfo(humanObj);
        //long timeEnd = System.nanoTime();
        //Log.temp.info("====时间计算耗时， useTime={}, openTime={}, endTime={}", timeEnd - timeOpen, timeOpen, timeEnd);
    }

}
