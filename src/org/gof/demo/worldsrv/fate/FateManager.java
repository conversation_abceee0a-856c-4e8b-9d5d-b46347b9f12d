package org.gof.demo.worldsrv.fate;

import com.pwrd.op.LogOp;
import org.gof.core.Port;
import org.gof.core.support.ManagerBase;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.observer.Listener;
import org.gof.demo.battlesrv.manager.CombatChangeLog;
import org.gof.demo.battlesrv.manager.PropManager;
import org.gof.demo.battlesrv.msgHandler.SkillManager;
import org.gof.demo.battlesrv.support.PropCalc;

import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.character.HumanDailyResetInfo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.common.GameServiceProxy;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.human.EModule;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.item.ItemConstants;
import org.gof.demo.worldsrv.mall.MallManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgFate;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.produce.ProduceVo;
import org.gof.demo.worldsrv.rank.RankManager;
import org.gof.demo.worldsrv.rank.RankParamKey;
import org.gof.demo.worldsrv.support.*;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.DailyResetTypeKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.enumKey.NewsConditionTypeKey;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;

import java.util.*;
import java.util.stream.Collectors;

public class FateManager extends ManagerBase {
    /**
     * 获取实例
     *
     * @return
     */
    public static FateManager inst() {
        return inst(FateManager.class);
    }

    public static final int FATE_POS_NUM = 8;
    public static final int POS_CLOSE = 0;
    public static final int POS_OPEN = 1;
    public static final int POS_EQUIP = 2;
    public static final int UPDATE_ADD = 1;
    public static final int UPDATE_DEL = 2;

    public static final int FATE_BAG_REST_NUM = 20;

    public static final int AUTO_DISMANTLE_OFF = 0;
    public static final int AUTO_DISMANTLE_ON = 1;

    public static final Map<Integer, List<Integer>> QUALITY_DECOMPOSITION_MAP =
            Collections.unmodifiableMap(new HashMap<Integer, List<Integer>>() {{
                put(1, Arrays.asList(2, 3, 4)); // 品质2、3、4都分解
                put(3, Arrays.asList(2));       // 品质2分解
                put(2, Arrays.asList(2, 3));    // 品质2、3分解
            }});

    public void init(HumanObject humanObj) {
        Fate data = humanObj.fate.fate;
        if(data == null){
            return;
        }
        String fateBagStr = data.getFateBagMap();
        if (data.getIsZip() == 1) {
            fateBagStr = StringZipUtils.unzip(fateBagStr);
        }
        humanObj.fate.fateBagMap = FateVo.fromMapJsonString(fateBagStr);
        //初始化武魂ID生成器是fateMap中最大的ID+1
        int idGenFate = 0;
        for (Map.Entry<Long, FateVo> entry : humanObj.fate.fateBagMap.entrySet()) {
            if (entry.getKey() > idGenFate) {
                idGenFate = entry.getKey().intValue();
            }
        }
        humanObj.fate.setMergeRewardMap(data.getMergeRewardMap());
        humanObj.fate.setIdGen(idGenFate + 1);
    }

    /**
     * 保存武魂背包
     */
    public void saveFateBag(HumanObject humanObj) {
        Fate fate = humanObj.fate.fate;
        if (fate == null) {
            Log.temp.error("===fate=null");
            return;
        }
        String fateBagStr = FateVo.mapJsonString(humanObj.fate.fateBagMap);
        if (fateBagStr.length() > 1000) {
            fateBagStr = StringZipUtils.zip(fateBagStr);
            fate.setIsZip(1);
        } else if (fate.getIsZip() == 1) {
            fate.setIsZip(0);
        }
        fate.setFateBagMap(fateBagStr);
        fate.setMergeRewardMap(humanObj.fate.getMergeReardMapStr());
    }

    /**
     * 武魂信息
     */
    public void handleFateInfoC2S(HumanObject humanObj) {
        FateData data = humanObj.fate;
        if (data.fate == null) {
            return;
        }

        //发送武魂格子信息
        MsgFate.fate_info_s2c.Builder msg = MsgFate.fate_info_s2c.newBuilder();
        Map<Integer, Long> posMap = humanObj.fate.getPosMap();
        for (int i = 1; i <= FATE_POS_NUM; i++) {
            Define.p_fate_pos.Builder pos = Define.p_fate_pos.newBuilder();
            pos.setPosId(i);
            if (posMap.containsKey(i)) {
                pos.setState(posMap.get(i) == 0 ? POS_OPEN : POS_EQUIP);
                pos.setId(posMap.get(i));
            } else {
                pos.setState(POS_CLOSE);
                pos.setId(0);
            }
            msg.addFatePosList(pos);
        }

        //发送武魂背包信息
        for (Map.Entry<Long, FateVo> entry : humanObj.fate.fateBagMap.entrySet()) {
            msg.addFateList(entry.getValue().toProto(entry.getKey()));
        }

        msg.setShowPosId(data.fate.getShowPos());
        humanObj.sendMsg(msg);
    }

    /**
     * 武魂镶嵌
     *
     * @param type   类型
     * @param posId 位置ID
     * @param id     ID
     */
    public void handleFateInlayC2S(HumanObject humanObj, int type, int posId, long id) {
        if (humanObj.fate.fate == null) {
            return;
        }

        Map<Integer, Long> posMap = humanObj.fate.getPosMap();
        if (!posMap.containsKey(posId)) {
            return;
        }

        Map<Long, FateVo> fateBagMap = humanObj.fate.fateBagMap;
        FateVo fateVo = fateBagMap.get(id);
        if (fateVo == null) {
            return;
        }

        ConfFate confFate = ConfFate.get(fateVo.sn);
        if (confFate == null) {
            Log.game.error("玩家id{}武魂镶嵌错误，配置表中没有ID为{}的武魂", humanObj.id, fateVo.sn);
            return;
        }
        List<Define.p_passive_skill> addPassiveSkillList = new ArrayList<>();
        List<Define.p_passive_skill> delPassiveSkillList = new ArrayList<>();
        if (type == 0) {
            List<Integer> exclusiveSnList = Arrays.stream(confFate.mutually_exclusive).boxed().collect(Collectors.toList());
            boolean isReplace = false;
            int realPos = posId;// 实际的格子，如果有装备互斥的武魂，是直接变成替换到那个格子上
            for (Map.Entry<Integer, Long> entry : posMap.entrySet()) {
                long equipFateId = entry.getValue();
                if (equipFateId == 0L) {
                    continue;
                }
                FateVo equipFateVo = fateBagMap.get(equipFateId);
                if (equipFateVo == null) {
                    Log.game.error("武魂镶嵌出错, 穿戴的武魂已经丢失, humanId={}, humanName={}, equipFateId={}", humanObj.id, humanObj.name, equipFateId);
                    continue;
                }
                if (exclusiveSnList.contains(equipFateVo.sn)) {
                    if (isReplace) {
                        Inform.sendMsg_error(humanObj, ErrorTip.FateMutualExclusion);
                        return;
                    }
                    realPos = entry.getKey();
                    isReplace = true;
                }
            }
            long replaceId = posMap.getOrDefault(realPos, 0l);// 获取被换下的武魂id
            posMap.put(realPos, id);
            humanObj.fate.fate.setPosMap(humanObj.fate.getPosMapStr(posMap));
            humanObj.getHuman2().setFateShow(getFateShowSn(posMap, fateBagMap));
            Define.p_fate_pos.Builder pos = Define.p_fate_pos.newBuilder();
            pos.setPosId(posId);
            pos.setState(POS_EQUIP);
            pos.setId(id);
            MsgFate.fate_inlay_s2c.Builder msg = MsgFate.fate_inlay_s2c.newBuilder();
            msg.setFatePos(pos);
            humanObj.sendMsg(msg);

            if (replaceId != 0l) {
                // 处理被换下的武魂的被动技能
                FateVo replaceFateVo = humanObj.fate.fateBagMap.get(replaceId);
                if (replaceFateVo.skillSn != 0) {
                    Define.p_passive_skill.Builder skillMsg = Define.p_passive_skill.newBuilder();
                    skillMsg.setSkillId(replaceFateVo.skillSn);
                    skillMsg.setSkillLv(replaceFateVo.skillLevel);
                    delPassiveSkillList.add(skillMsg.build());
                }
                ConfFateLevel_0 confFateLevel = ConfFateLevel_0.get(replaceFateVo.sn, replaceFateVo.level);
                if (confFateLevel.skill != null) {
                    for (int i = 0; i < confFateLevel.skill.length; i++) {
                        Define.p_passive_skill.Builder skillMsg = Define.p_passive_skill.newBuilder();
                        skillMsg.setSkillId(confFateLevel.skill[i][0]);
                        skillMsg.setSkillLv(confFateLevel.skill[i][1]);
                        delPassiveSkillList.add(skillMsg.build());
                    }
                }
            }
            if (fateVo.skillSn != 0) {
                // 处理装备的武魂的被动技能
                Define.p_passive_skill.Builder skillMsg = Define.p_passive_skill.newBuilder();
                skillMsg.setSkillId(fateVo.skillSn);
                skillMsg.setSkillLv(fateVo.skillLevel);
                addPassiveSkillList.add(skillMsg.build());
            }
            //
            ConfFateLevel_0 confFateLevel = ConfFateLevel_0.get(fateVo.sn, fateVo.level);
            if (confFateLevel.skill != null) {
                for (int i = 0; i < confFateLevel.skill.length; i++) {
                    Define.p_passive_skill.Builder skillMsg = Define.p_passive_skill.newBuilder();
                    skillMsg.setSkillId(confFateLevel.skill[i][0]);
                    skillMsg.setSkillLv(confFateLevel.skill[i][1]);
                    addPassiveSkillList.add(skillMsg.build());
                }
            }
        } else if (type == 1) {
            int realPos = 0;
            if (posMap.get(posId) == id) {
                realPos = posId;
            } else {
                // 出现不一致的情况，以解除武魂为主
                for (Map.Entry<Integer, Long> entry : posMap.entrySet()) {
                    if (entry.getValue() == id) {
                        realPos = entry.getKey();
                    }
                }
            }
            if (realPos == 0) {
                return;
            }
            posMap.put(realPos, 0L);
            humanObj.fate.fate.setPosMap(humanObj.fate.getPosMapStr(posMap));
            humanObj.getHuman2().setFateShow(getFateShowSn(posMap, fateBagMap));
            Define.p_fate_pos.Builder pos = Define.p_fate_pos.newBuilder();
            pos.setPosId(realPos);
            pos.setState(POS_OPEN);
            pos.setId(0L);
            MsgFate.fate_inlay_s2c.Builder msg = MsgFate.fate_inlay_s2c.newBuilder();
            msg.setFatePos(pos);
            humanObj.sendMsg(msg);

            if (fateVo.skillSn != 0) {
                Define.p_passive_skill.Builder skillMsg = Define.p_passive_skill.newBuilder();
                skillMsg.setSkillId(fateVo.skillSn);
                skillMsg.setSkillLv(fateVo.skillLevel);
                delPassiveSkillList.add(skillMsg.build());
            }

            ConfFateLevel_0 confFateLevel = ConfFateLevel_0.get(fateVo.sn, fateVo.level);
            if (confFateLevel.skill != null) {
                for (int i = 0; i < confFateLevel.skill.length; i++) {
                    Define.p_passive_skill.Builder skillMsg = Define.p_passive_skill.newBuilder();
                    skillMsg.setSkillId(confFateLevel.skill[i][0]);
                    skillMsg.setSkillLv(confFateLevel.skill[i][1]);
                    delPassiveSkillList.add(skillMsg.build());
                }
            }
        } else {
            return;
        }

        updateFateSkill(humanObj, addPassiveSkillList, delPassiveSkillList);
        updatePropCalcAndPower(humanObj);
    }

    private int getFateShowSn(Map<Integer,Long> posMap, Map<Long, FateVo> fateBagMap) {
        int minPosId = posMap.entrySet().stream()
                .filter(entry -> entry.getValue() != 0L)
                .map(Map.Entry::getKey)
                .min(Integer::compareTo)
                .orElse(0);
        FateVo fateVoShow = fateBagMap.get(minPosId);
        if (fateVoShow != null) {
            return fateVoShow.sn;
        }
        return 0;
    }

    public void updateFateSkill(HumanObject humanObj, List<Define.p_passive_skill> addPassiveSkillList, List<Define.p_passive_skill> delPassiveSkillList) {
        if (!addPassiveSkillList.isEmpty() || !delPassiveSkillList.isEmpty()) {
            // 有一个不为空就要发更新
            SkillManager.inst().sendMsg_skill_passive_update_s2c(humanObj, addPassiveSkillList, delPassiveSkillList);
            // 更新human
            List<int[]> skillList = new ArrayList<>();
            Map<Integer, Long> posMap = humanObj.fate.getPosMap();
            Map<Long, FateVo> fateBagMap = humanObj.fate.fateBagMap;
            for (Long id : posMap.values()) {
                if (id == 0L) {
                    continue;
                }
                FateVo vo = fateBagMap.get(id);
                if (vo == null) {
                    continue;
                }
                if (vo.skillSn != 0) {
                    skillList.add(new int[] {
                            vo.skillSn,
                            vo.skillLevel
                    });
                }
                ConfFateLevel_0 confFateLevel = ConfFateLevel_0.get(vo.sn, vo.level);
                if (confFateLevel.skill != null) {
                    for (int i = 0; i < confFateLevel.skill.length; i++) {
                        int skillSn = confFateLevel.skill[i][0];
                        int skillLv = confFateLevel.skill[i][1];
                        skillList.add(new int[] {
                                skillSn,
                                skillLv
                        });
                    }
                }
            }
            int[][] skills = skillList.toArray(new int[0][]);
            humanObj.getHuman2().setFateSkillMap(Utils.intArray2ToStr(skills));
        }
    }

    /**
     * 副本通关
     */
    @Listener(value = EventKey.INSTANCE_PASS, subInt = InstanceConstants.FATECHAPTER_11)
    public void onInstancePass(Param param) {
        HumanObject humanObj = param.get("humanObj");
        int repSn = param.get("repSn");
        ConfFateChapter conf = ConfFateChapter.get(repSn);
        if (conf == null) {
            Log.temp.error("武魂配置表中没有ID为{}的副本章节", repSn);
            return;
        }
        handleFatePosOpen(humanObj, repSn);
        handleFateGungeon(humanObj, repSn);
        updateFateGungeonRank(humanObj, repSn);
    }

    @Listener(EventKey.HUMAN_LOGIN_FINISH)
    public void _HUMAN_LOGIN_FINISH(Param param) {
        HumanObject humanObj = param.get("humanObj");
        checkFatePosOpen(humanObj);
    }

    private void checkFatePosOpen(HumanObject humanObj) {
        int level = humanObj.operation.getRepMaxDiffculty(InstanceConstants.FATECHAPTER_11);
        if (level <= 0) {
            return;
        }
        int repSn = GlobalConfVal.getRepSn(InstanceConstants.FATECHAPTER_11, level);
        handleFatePosOpen(humanObj, repSn);
    }

    /**
     * 处理武魂格子解锁
     */
    private void handleFatePosOpen(HumanObject humanObj, int repSn) {
        int[] openArr = ConfGlobal.get(401).intArray;

        if(humanObj.fate == null || humanObj.fate.fate == null){
            return;
        }

        boolean canOpen = false;
        Map<Integer, Long> posMap = humanObj.fate.getPosMap();
        for (int i = 0; i < openArr.length; ++i) {
            if (repSn >= openArr[i]) {
                int posId = i + 1;
                if (!posMap.containsKey(posId)) {
                    posMap.put(posId, 0L);
                    canOpen = true;
                }
            }
        }
        if(canOpen){
            humanObj.fate.fate.setPosMap(humanObj.fate.getPosMapStr(posMap));
        }

    }

    /**
     * 更新武魂副本
     */
    private void handleFateGungeon(HumanObject humanObj, int repSn) {
        ConfFateChapter conf = ConfFateChapter.get(repSn);
        int oldValue = humanObj.operation.getRepMaxDiffculty(InstanceConstants.FATECHAPTER_11);
        if (oldValue >= conf.level) {
            return;
        }
        humanObj.operation.setRepMaxDiffculty(InstanceConstants.FATECHAPTER_11, conf.level);

        // 给奖励
        Map<Integer, Integer> rewardMap = new HashMap<>();
        for (int[] rewardSns : conf.first_reward) {
            int rewardSn = rewardSns[0];
            ConfGoods confGoods = ConfGoods.get(rewardSn);
            if (confGoods == null) {
                Log.game.error("ConfGoods表配置有问题，sn={}", rewardSn);
                continue;
            }
            if (confGoods.type == ItemConstants.礼包) {
                for (int i = 0; i < rewardSns[1]; i++) {
                    int[][] dropSnNums = new int[confGoods.effect.length][];
                    for (int j = 0; j < confGoods.effect.length; j++) {
                        dropSnNums[j] = new int[2];
                        dropSnNums[j][0] = confGoods.effect[j][0];
                        dropSnNums[j][1] = confGoods.effect[j].length >= 2 ? confGoods.effect[j][1] : 1;
                    }
                    Utils.mergeMap(rewardMap, ProduceManager.inst().produceAddDrop(humanObj, dropSnNums, MoneyItemLogKey.通关副本类型11));
                }
            } else {
                int addNum = rewardSns.length >= 2 ? rewardSns[1] : 1;
                int num = rewardMap.getOrDefault(rewardSn, 0);
                num += addNum;
                rewardMap.put(rewardSn, num);
                ProduceManager.inst().produceAdd(humanObj, rewardSn, addNum, MoneyItemLogKey.通关副本类型11);
            }
        }
        List<Define.p_reward> rewardList = InstanceManager.inst().to_p_rewardList(rewardMap);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_5001, rewardList);
        InstanceManager.inst().sendMsg_dungeon_result_s2c(humanObj, 0, InstanceConstants.FATECHAPTER_11, repSn, InstanceConstants.win, rewardList, new ArrayList<>(0));
    }

    /**
     * 更新武魂副本排行
     */
    private void updateFateGungeonRank(HumanObject humanObj, int repSn) {
        ConfFateChapter conf = ConfFateChapter.get(repSn);
        ConfRanktype confRanktype = ConfRanktype.get(RankParamKey.rankTypeFateDungeon);
        if (confRanktype == null) {
            return;
        }
        String rankKey = RankManager.inst().getRedisRankTypeKey(humanObj.getHuman().getServerId(), confRanktype);
        RankManager.inst().updateRankWithTime(humanObj, rankKey, confRanktype, conf.level);
    }

    /**
     * 武魂升级
     */
    public void handleFateLevelUpC2S(HumanObject humanObj, long id) {
        if (humanObj.fate.fate == null) {
            return;
        }

        FateVo fateVo = humanObj.fate.fateBagMap.get(id);
        if (fateVo == null) {
            return;
        }

        ConfFateLevel_0 confFate = ConfFateLevel_0.get(fateVo.sn, fateVo.level);
        if (confFate == null || confFate.expend == null || confFate.expend.length == 0) {
            Inform.sendMsg_error(humanObj,24);
            return;
        }

        ConfFateLevel_0 confFateNext = ConfFateLevel_0.get(fateVo.sn, fateVo.level + 1);
        if (confFateNext == null) {
            Log.temp.error("武魂配置表中没有ID为{}的武魂的下一级配置", fateVo.sn, fateVo.level + 1);
            Inform.sendMsg_error(humanObj,24);
            return;
        }

        ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj, confFate.expend[0], confFate.expend[1], MoneyItemLogKey.武魂升级);
        if (!result.success) {
            return;
        }

        fateVo.level += 1;
        saveFateBag(humanObj);
        updatePropCalcAndPower(humanObj);
        List<Define.p_passive_skill> delSkillList = new ArrayList<>();
        List<Define.p_passive_skill> addSkillList = new ArrayList<>();
        if (confFate.skill != null) {
            for (int i = 0; i < confFate.skill.length; i++) {
                Define.p_passive_skill.Builder skillMsg = Define.p_passive_skill.newBuilder();
                skillMsg.setSkillId(confFate.skill[i][0]);
                skillMsg.setSkillLv(confFate.skill[i][1]);
                delSkillList.add(skillMsg.build());
            }
        }
        if (confFateNext.skill != null) {
            for (int i = 0; i < confFateNext.skill.length; i++) {
                Define.p_passive_skill.Builder skillMsg = Define.p_passive_skill.newBuilder();
                skillMsg.setSkillId(confFateNext.skill[i][0]);
                skillMsg.setSkillLv(confFateNext.skill[i][1]);
                addSkillList.add(skillMsg.build());
            }
        }
        updateFateSkill(humanObj, addSkillList, delSkillList);

        Human3 human3 = humanObj.getHuman3();
        long newCurrCost = human3.getFateCurrCost() + confFate.expend[1];
        if (newCurrCost > human3.getFateMaxCost()) {
            long addVal = newCurrCost - human3.getFateMaxCost();
            ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_30, addVal, addVal);
            human3.setFateMaxCost(newCurrCost);
        }
        human3.setFateCurrCost(newCurrCost);

        MsgFate.fate_level_up_s2c.Builder msg = MsgFate.fate_level_up_s2c.newBuilder();
        msg.setFate(fateVo.toProto(id));
        humanObj.sendMsg(msg);
    }

    /**
     * 武魂祈福信息
     */
    public void handleFatePrayInfoC2S(HumanObject humanObj) {
        Fate fate= humanObj.fate.fate;
        if (fate == null) {
            return;
        }
        MsgFate.fate_pray_info_s2c.Builder msg = MsgFate.fate_pray_info_s2c.newBuilder();
        msg.setPrayPoint(fate.getBless());
        msg.setIsAutoDismantle(fate.getIsAutoDismantle());
        if(fate.getDismantleType() == 0){
            fate.setDismantleType(1);
        }
        msg.setDismantleType(fate.getDismantleType());
        humanObj.sendMsg(msg);
    }

    /**
     * 武魂祈福
     *
     * @param type 类型
     */
    public void handleFatePrayC2S(HumanObject humanObj, int type) {
        if (humanObj.fate.fate == null) {
            return;
        }

        ConfGlobal confGlobal = ConfGlobal.get(402);
        int itemSn = Utils.intValue(confGlobal.strArray[0]);
        int itemOnceCostNum = Utils.intValue(confGlobal.strArray[1]);
        int prayNum;
        int drawNum;
        if (type == 1) {
            drawNum = 1;
            prayNum = 1;
        } else if (type == 10) {
            drawNum = 10;
            prayNum = 10;
        } else {
            Log.game.error("祈愿类型不正确：{}", type);
            return;
        }

        if (checkFateBagSizeFull(humanObj, drawNum)) {
            Inform.sendMsg_error(humanObj, ErrorTip.FateBagFull);
            return;
        }
        int itemNum = itemOnceCostNum * drawNum;
        ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj, itemSn, itemNum, MoneyItemLogKey.武魂祈福);
        if (!result.success) {
            result = ProduceManager.inst().checkAndCostItemPrice(humanObj, itemSn, itemNum, MoneyItemLogKey.武魂祈福);
            if (!result.success) {
                return;
            }
        }

        int bless = humanObj.fate.fate.getBless();

        int isAutoDismantle = humanObj.fate.fate.getIsAutoDismantle();
        int dismantleType = humanObj.fate.fate.getDismantleType();
        // TODO 是否根据bless < confGlobal.value 先循环所有
        List<ConfFateDraw> confGuaranteeList = new ArrayList<>();
        List<Integer> fateOneProListNew = new ArrayList<>();

        List<ConfFateDraw> confFateDrawList = new ArrayList<>();
        List<Integer> fateAllProListNew = new ArrayList<>();

        for (ConfFateDraw conf : ConfFateDraw.findAll()) {
            if (conf.is_guaranteed == 1) {
                fateOneProListNew.add(conf.weights);
                confGuaranteeList.add(conf);
            }
            confFateDrawList.add(conf);
            fateAllProListNew.add(conf.weights);
        }

        ConfFateDraw confFateDraw = null;
        List<Param> addFateParamList = new ArrayList<>();
        List<Integer> dismantleList = new ArrayList<>();
        List<Define.p_fate_pray> rewardMsgList = new ArrayList<>();
        for (int i = 0; i < prayNum; i++) {
            if (bless >= confGlobal.value) {
                int index = Utils.getRandRange(fateOneProListNew);
                confFateDraw = confGuaranteeList.get(index);
            } else {
                int index = Utils.getRandRange(fateAllProListNew);
                confFateDraw = confFateDrawList.get(index);
            }
            bless = confFateDraw.is_guaranteed == 1 ? 0 : bless + 1;

            // 根据武魂抽奖的奖励次数循环
            for (int j = 0; j < confFateDraw.reward.length; j += 2) {
                int goodsSn = confFateDraw.reward[j];
                ConfGoods conf = ConfGoods.get(goodsSn);
                for (int k = 0; k < confFateDraw.reward[j + 1]; k++) {
                    // 根据物品决定给几个武魂（这个循环长度基本都是1）
                    Define.p_fate_pray.Builder rewardMsg = Define.p_fate_pray.newBuilder();
                    if (isAutoDismantle(humanObj, isAutoDismantle, dismantleType, conf)) {
                        dismantleList.add(goodsSn);
                        rewardMsg.setIsDismantle(AUTO_DISMANTLE_ON);
                    }else {
                        rewardMsg.setIsDismantle(AUTO_DISMANTLE_OFF);
                        for (int l = 0; l < conf.effect.length; l++) {
                            addFateParamList.add(new Param("sn", conf.effect[l][0], "level", conf.effect[l][1]));
                        }
                    }
                    rewardMsg.setFate(Define.p_reward.newBuilder().setGtid(goodsSn).setNum(1));
                    rewardMsgList.add(rewardMsg.build());
                }
            }
        }
        int dismantleSn = 0;
        int dismantleNum = 0;
        for (Integer goodsSn : dismantleList) {
            int[][] effect = ConfGoods.get(goodsSn).effect;
            if (effect.length == 0) {
                continue;
            }
            int sn = effect[0][0];
            int lv = 1;
            if (effect[0].length > 1) {
                lv = effect[0][1];
            }
            ConfFateLevel_0 conf = ConfFateLevel_0.get(sn, lv);
            if (conf == null) {
                Log.game.error("祈愿分解的物品没有对应的武魂配置：sn={},lv={}", sn, lv);
                continue;
            }
            if (dismantleSn == 0) {
                dismantleSn = conf.breakdown_reward[0];
            }
            dismantleNum += getDecreaseNum(sn, lv);
        }
        if(dismantleNum > 0){
            ProduceManager.inst().produceAdd(humanObj, dismantleSn, dismantleNum, MoneyItemLogKey.武魂祈福);
        }
        humanObj.fate.fate.setBless(bless);
        MsgFate.fate_pray_s2c.Builder msgReward = MsgFate.fate_pray_s2c.newBuilder();
        msgReward.setPrayPoint(bless);
        msgReward.setType(UPDATE_ADD);
        msgReward.addAllRewardList(rewardMsgList);
        msgReward.setDismantleNum(dismantleNum);
        humanObj.sendMsg(msgReward);

        MsgFate.fate_update_s2c.Builder msgUpdate = MsgFate.fate_update_s2c.newBuilder();
        Map<Long, FateVo> fateVoMap = addFateVo(humanObj, addFateParamList.toArray(new Param[0]));
        for (Long id : fateVoMap.keySet()) {
            msgUpdate.addFateList(fateVoMap.get(id).toProto(id));
        }
        msgUpdate.setType(UPDATE_ADD);
        humanObj.sendMsg(msgUpdate);

        confGuaranteeList.clear();
        fateOneProListNew.clear();
        confFateDrawList.clear();
        fateAllProListNew.clear();
        addFateParamList.clear();

        
        HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyDraw3Count.getType());
        info.setValue(info.getValue() + drawNum);
        humanObj.saveDailyResetRecord();

        MallManager.inst().pushLimitGift26Draw(humanObj, 3);
    }

    private boolean isAutoDismantle(HumanObject humanObj, int isAutoDismantle, int dismantleType, ConfGoods confGoods) {
        if (isAutoDismantle == 0) {
            return false;
        }
        if (confGoods.effect.length == 0) {
            return false;
        }
        ConfFate confFate = ConfFate.get(confGoods.effect[0][0]);
        if (confFate == null) {
            return false;
        }
        return QUALITY_DECOMPOSITION_MAP.getOrDefault(dismantleType,new ArrayList<>()).contains(confFate.quality);
    }

    /**
     * 武魂分解
     */
    public void handleFateDismantleC2S(HumanObject humanObj, List<Long> idListList) {
        if (humanObj.fate.fate == null) {
            return;
        }

        MsgFate.fate_dismantle_s2c.Builder msg = MsgFate.fate_dismantle_s2c.newBuilder();

        List<Long> posIdList = new ArrayList<>(humanObj.fate.getPosMap().values());

        long decreaseCostNum = 0;
        List<int[]> rewardList = new ArrayList<>();
        for (Long id : idListList) {
            FateVo fateVo = humanObj.fate.fateBagMap.get(id);
            if (fateVo == null) {
                continue;
            }
            if (posIdList.contains(id)) {
                continue;
            }
            ConfFate confFate = ConfFate.get(fateVo.sn);
            if (confFate == null) {
                Log.game.error("玩家id{}武魂分解错误，配置表中没有ID为{}的武魂", humanObj.id, fateVo.sn);
                continue;
            }
            ConfFateLevel_0 conf = ConfFateLevel_0.get(fateVo.sn, fateVo.level);
            if (conf == null) {
                Log.game.error("玩家id{}武魂分解错误，配置表中没有ID为{}等级为{}的武魂", humanObj.id, fateVo.sn, fateVo.level);
                continue;
            }
            rewardList.add(new int[] {
                    conf.breakdown_reward[0],
                    conf.breakdown_reward[1]
            });
            // 计算消耗回退的数量
            if (fateVo.level > 1) {
                ConfFateLevel_0 confLevel0 = ConfFateLevel_0.get(fateVo.sn, 1);
                decreaseCostNum += conf.breakdown_reward[1] - confLevel0.breakdown_reward[1];
            }
            humanObj.fate.fateBagMap.remove(id);
            msg.addIdList(id);
        }
        saveFateBag(humanObj);
        int[][] rewards = rewardList.toArray(new int[0][]);
        ProduceManager.inst().produceAdd(humanObj, rewards, MoneyItemLogKey.武魂分解);

        // 回退邪眼当前养成消耗
        if (decreaseCostNum != 0) {
            Human3 human3 = humanObj.getHuman3();
            long currCost = human3.getFateCurrCost() - decreaseCostNum;
            human3.setFateCurrCost(Math.max(currCost, 0));
        }

        for (int[] reward : rewards) {
            Define.p_reward.Builder rewardMsg = Define.p_reward.newBuilder();
            rewardMsg.setGtid(reward[0]);
            rewardMsg.setNum(reward[1]);
            msg.addRewardList(rewardMsg);
        }
        humanObj.sendMsg(msg);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, 0, msg.getRewardListList());
    }

    private int getDecreaseNum(int sn, int level) {
        if (level < 1) {
            return 0;
        }

        ConfFateLevel_0 confLevel = ConfFateLevel_0.get(sn, level);
        if (level == 1) {
            return confLevel.breakdown_reward[1];
        }
        ConfFateLevel_0 confLevel1 = ConfFateLevel_0.get(sn, 1);

        if (confLevel == null || confLevel1 == null) {
            Log.game.error("计算武魂分解消耗回退错误，配置表中没有ID为{}等级为{}的武魂", sn, level);
            return 0;
        }

        return confLevel.breakdown_reward[1] - confLevel1.breakdown_reward[1];
    }

    /**
     * 武魂融合
     */
    public void handleFateFusionC2S(HumanObject humanObj, int sn, List<Long> idList) {
        if (humanObj.fate.fate == null || idList.isEmpty()) {
            return;
        }

        ConfFateFusion conf = ConfFateFusion.get(sn);
        if (conf == null) {
            Log.game.error("武魂融合sn找不到配表，sn={}", sn);
            return;
        }

        if (!humanObj.fate.fateBagMap.keySet().containsAll(idList)) {
            // 选择有不存在于背包的武魂id
            Log.game.error("武魂融合中的材料武魂不在背包内，humanId={}", humanObj.id);
            return;
        }

        Collection<Long> equipIdList = humanObj.fate.getPosMap().values();
        for (Long id : idList) {
            if (equipIdList.contains(id)) {
                // 选择的武魂id还穿在身上
                Log.game.error("武魂融合中的材料武魂还装备在身上，humanId={}", humanObj.id);
                return;
            }
        }

        boolean canFusion = false;
        List<Integer> sns = idList.stream().map((id) -> (humanObj.fate.fateBagMap.get(id).sn)).collect(Collectors.toList());
        for (int[] materialFateSns : conf.material_fate_id) {
            if (sns.size() != materialFateSns.length) {
                continue;
            }
            if (Arrays.stream(materialFateSns).boxed().collect(Collectors.toList()).containsAll(sns)) {
                canFusion = true;
                break;
            }
        }

        if (!canFusion) {
            Log.game.error("武魂融合条件不满足，humanId={}", humanObj.id);
            return;
        }

        // 判断是新增武魂还是给武魂刷词条
        long fateId = -1;
        for (Long id : idList) {
            FateVo fateVo = humanObj.fate.fateBagMap.get(id);
            if (fateVo.sn == conf.get_fate_id) {
                fateId = id;
                break;
            }
        }
        if (fateId == -1) {
            // 纯新增武魂
            // 移除武魂，计算返回的材料
            Map<Integer, Integer> returnMaterialMap = new HashMap<>();
            for (Long id : idList) {
                FateVo fateVo = humanObj.fate.fateBagMap.remove(id);
                if (fateVo.level != 1) {
                    Utils.mergeMap(returnMaterialMap, caculateFateLevelCost(fateVo));// 把几个武魂的返回材料数量合并起来
                }
            }
            // 生成新武魂
            Map<Long, FateVo> newFateVoMap = addFateVo(humanObj, new Param("sn", conf.get_fate_id, "level", 1, "skillSn", getRandomSkill(conf)));
            // 根据返回材料给新武魂升级
            boolean hasOneUpdate = updateFateLevelByReturn(newFateVoMap, returnMaterialMap);
            if (hasOneUpdate) {
                saveFateBag(humanObj);// 有武魂更新等级，重新保存背包
            }
            // 发送消息
            MsgFate.fate_fusion_s2c.Builder msg = MsgFate.fate_fusion_s2c.newBuilder();
            msg.addAllIdList(idList);
            for (Long id : newFateVoMap.keySet()) {
                msg.setNewFate(newFateVoMap.get(id).toProto(id));
            }
            // 剩余材料作为奖励
            if (returnMaterialMap.size() != 0) {
                ProduceManager.inst().produceAdd(humanObj, returnMaterialMap, MoneyItemLogKey.武魂融合);
                for (Map.Entry<Integer, Integer> entry : returnMaterialMap.entrySet()) {
                    Define.p_reward.Builder rewardMsg = Define.p_reward.newBuilder();
                    rewardMsg.setGtid(entry.getKey());
                    rewardMsg.setNum(entry.getValue());
                    msg.addRewardList(rewardMsg);
                }
                // 回退邪眼当前养成消耗
                int decreaseCostNum = returnMaterialMap.values().stream().mapToInt(Integer::intValue).sum();
                if (decreaseCostNum != 0) {
                    Human3 human3 = humanObj.getHuman3();
                    long currCost = human3.getFateCurrCost() - decreaseCostNum;
                    human3.setFateCurrCost(Math.max(currCost, 0));
                }
            }
            humanObj.sendMsg(msg);
        } else {
            // 刷被动和升级
            FateVo oldFateVo = humanObj.fate.fateBagMap.get(fateId);
            oldFateVo.newSkillSn = getRandomSkill(conf, oldFateVo.skillSn);
            List<Long> removeIdList = new ArrayList<>(1);
            Map<Integer, Integer> returnMaterialMap = new HashMap<>();
            for (Long id : idList) {
                if (id != fateId) {
                    removeIdList.add(id);
                    FateVo fateVo = humanObj.fate.fateBagMap.remove(id);
                    if (fateVo.level != 1) {
                        Utils.mergeMap(returnMaterialMap, caculateFateLevelCost(fateVo));// 把几个武魂的返回材料数量合并起来
                    }
                }
            }
            Map<Long, FateVo> fateVoMap = new HashMap<>();
            fateVoMap.put(fateId, oldFateVo);
            updateFateLevelByReturn(fateVoMap, returnMaterialMap);
            saveFateBag(humanObj);

            MsgFate.fate_fusion_info_s2c.Builder msg = MsgFate.fate_fusion_info_s2c.newBuilder();
            msg.setOldFate(oldFateVo.toProto(fateId));
            msg.setNewFate(oldFateVo.toProtoNewSkill(fateId));
            msg.setType(1);
            msg.addAllIdList(removeIdList);

            humanObj.sendMsg(msg);
            if (returnMaterialMap.size() != 0) {
                // 先存起来，等后面词条选定在给
                humanObj.fate.fateMergeRewardMap.put(fateId, returnMaterialMap);
                saveFateBag(humanObj);
                // 回退邪眼当前养成消耗
                int decreaseCostNum = returnMaterialMap.values().stream().mapToInt(Integer::intValue).sum();
                if (decreaseCostNum != 0) {
                    Human3 human3 = humanObj.getHuman3();
                    long currCost = human3.getFateCurrCost() - decreaseCostNum;
                    human3.setFateCurrCost(Math.max(currCost, 0));
                }
            }
        }
    }

    /**
     * 根据returnMaterialMap决定武魂能升到几级
     */
    private boolean updateFateLevelByReturn(Map<Long, FateVo> newFateVoMap, Map<Integer, Integer> returnMaterialMap) {
        boolean hasOneUpdate = false;
        for (Map.Entry<Long, FateVo> entry : newFateVoMap.entrySet()) {
            if (returnMaterialMap.isEmpty()) {
                break;
            }
            FateVo fateVo = entry.getValue();
            ConfFateLevel_0 confFate = ConfFateLevel_0.get(fateVo.sn, fateVo.level);
            if (confFate == null || confFate.expend == null || confFate.expend.length == 0) {
                continue;
            }
            int itemSn = confFate.expend[0];
            int condNum = confFate.expend[1];
            if (!returnMaterialMap.containsKey(itemSn)) {
                continue;
            }
            int itemNum = returnMaterialMap.get(itemSn);
            if (itemNum < condNum) {
                continue;
            }
            fateVo.level += 1;
            itemNum -= condNum;
            if (itemNum > 0) {
                returnMaterialMap.put(itemSn, itemNum);
            } else {
                returnMaterialMap.remove(itemSn);
            }
            hasOneUpdate = true;
        }
        return hasOneUpdate;
    }


    /**
     * 武魂获取随机的被动技能
     */
    public int getRandomSkill(ConfFateFusion conf, int... excludeSkills) {
        List<Integer> excludeSkillList = Arrays.stream(excludeSkills).boxed().collect(Collectors.toList());
        List<Integer> skillSnList = new ArrayList<>(conf.passive_skill_group.length);
        List<Integer> skillWeightList = new ArrayList<>(conf.passive_skill_group.length);
        for (int[] group : conf.passive_skill_group) {
            int skillSn = group[0];
            int weight = group[2];
            if (excludeSkillList.contains(skillSn)) {
                continue;
            }
            skillSnList.add(skillSn);
            skillWeightList.add(weight);
        }
        int[] weights = skillWeightList.stream().mapToInt(Integer::intValue).toArray();
        int index = Utils.randomByWeight(weights);
        return skillSnList.get(index);
    }

    /**
     * 武魂融合信息
     */
    public void handleFateFusionInfoC2S(HumanObject humanObj) {
        if (humanObj.fate.fate == null) {
            return;
        }

        for (Map.Entry<Long, FateVo> entry : humanObj.fate.fateBagMap.entrySet()) {
            FateVo fateVo = entry.getValue();
            if (fateVo.newSkillSn != 0) {
                MsgFate.fate_fusion_info_s2c.Builder msg = MsgFate.fate_fusion_info_s2c.newBuilder();
                msg.setOldFate(fateVo.toProto(entry.getKey()));
                msg.setNewFate(fateVo.toProtoNewSkill(entry.getKey()));
                msg.setType(1);
                humanObj.sendMsg(msg);
            }
        }
    }

    /**
     * 武魂融合选择
     *
     * @param choose 选择
     */
    public void handleFateFusionChooseC2S(HumanObject humanObj, int choose) {
        if (humanObj.fate.fate == null) {
            return;
        }

        long fateId = 0l;
        FateVo fateVo = null;
        for (Map.Entry<Long, FateVo> entry : humanObj.fate.fateBagMap.entrySet()) {
            FateVo vo = entry.getValue();
            if (vo.newSkillSn != 0) {
                fateId = entry.getKey();
                fateVo = vo;
                break;
            }
        }

        if (fateVo == null) {
            return;
        }
        if (choose == 1) {
            fateVo.skillSn = fateVo.newSkillSn;
        }
        fateVo.newSkillSn = 0;
        Map<Integer, Integer> returnMaterialMap = null;
        if (humanObj.fate.fateMergeRewardMap.containsKey(fateId)) {
            returnMaterialMap = humanObj.fate.fateMergeRewardMap.remove(fateId);
            ProduceManager.inst().produceAdd(humanObj, returnMaterialMap, MoneyItemLogKey.武魂融合);
        }
        saveFateBag(humanObj);

        MsgFate.fate_fusion_choose_s2c.Builder msg = MsgFate.fate_fusion_choose_s2c.newBuilder();
        msg.setNewFate(fateVo.toProto(fateId));
        if (returnMaterialMap != null) {
            for (Map.Entry<Integer, Integer> entry : returnMaterialMap.entrySet()) {
                Define.p_reward.Builder rewardMsg = Define.p_reward.newBuilder();
                rewardMsg.setGtid(entry.getKey());
                rewardMsg.setNum(entry.getValue());
                msg.addRewardList(rewardMsg);
            }
        }
        humanObj.sendMsg(msg);
    }

    /**
     * 武魂显示选择
     *
     * @param choose      选择
     * @param showPosId 显示位置ID
     */
    public void handleFateShowChooseC2S(HumanObject humanObj, int choose, int showPosId) {
        if (humanObj.fate.fate == null) {
            return;
        }
        Map<Integer, Long> posMap = humanObj.fate.getPosMap();
        if (posMap.get(showPosId) == null || posMap.get(showPosId) == 0) {
            return;
        }
        humanObj.fate.fate.setShowPos(showPosId);
        int fateSn = humanObj.fate.fateBagMap.get(posMap.get(showPosId)).sn;
        humanObj.getHuman2().setFateShow(fateSn);
        MsgFate.fate_show_choose_s2c.Builder msg = MsgFate.fate_show_choose_s2c.newBuilder();
        msg.setShowPosId(showPosId);
        humanObj.sendMsg(msg);
    }

    /**
     * 武魂红点已读
     *
     * @param fateId 武魂ID
     */
    public void handleFateRedReadC2S(HumanObject humanObj, long fateId) {
        if (humanObj.fate.fate == null) {
            return;
        }
        FateVo fateVo = humanObj.fate.fateBagMap.get(fateId);
        if (fateVo == null) {
            return;
        }
        fateVo.isRed = 0;
        saveFateBag(humanObj);

        MsgFate.fate_red_read_s2c.Builder msg = MsgFate.fate_red_read_s2c.newBuilder();
        msg.setFate(fateVo.toProto(fateId));
        humanObj.sendMsg(msg);
    }

    /**
     * 武魂重置等级
     *
     * @param fateId 武魂ID
     */
    public void handleFateResetC2S(HumanObject humanObj, long fateId) {
        if (humanObj.fate.fate == null) {
            return;
        }
        FateVo fateVo = humanObj.fate.fateBagMap.get(fateId);
        if (fateVo == null) {
            return;
        }
        ConfFate confFate = ConfFate.get(fateVo.sn);
        if (confFate == null) {
            Log.game.error("玩家id{}武魂重置错误，配置表中没有ID为{}的武魂", humanObj.id, fateVo.sn);
            return;
        }
        ConfGlobal confGlobal = ConfGlobal.get(403);
        if (confFate.quality < confGlobal.value) {
            return;
        }
        ConfFateLevel_0 confFateLevel = ConfFateLevel_0.get(fateVo.sn, 1);
        if (confFateLevel == null) {
            return;
        }

        ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj, confGlobal.intArray[0], confGlobal.intArray[1], MoneyItemLogKey.武魂重置);
        if (!result.success) {
            return;
        }

        // 返还升级消耗
        int[][] costs = caculateFateLevelCostWithoutCombine(fateVo);

        fateVo.level = 1;
        saveFateBag(humanObj);
        updatePropCalcAndPower(humanObj);

        if (costs != null && costs.length != 0) {
            ProduceManager.inst().produceAdd(humanObj, costs, MoneyItemLogKey.武魂重置);
            InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, costs);
            // 回退当前养成消耗
            long allCostValue = 0L;
            for (int[] cost : costs) {
                if (cost[0] == ItemConstants.goods_邪眼改造) {
                    allCostValue += cost[1];
                }
            }
            Human3 human3 = humanObj.getHuman3();
            long currCost = human3.getFateCurrCost() - allCostValue;
            human3.setFateCurrCost(Math.max(currCost, 0));
        }

        MsgFate.fate_reset_s2c.Builder msg = MsgFate.fate_reset_s2c.newBuilder();
        Define.p_fate.Builder fate = fateVo.toProto(fateId);
        msg.setFate(fate);
        humanObj.sendMsg(msg);
    }

    /**
     * 武魂生成
     */
    public void produceFate(HumanObject humanObj, List<ProduceVo> voList, MoneyItemLogKey log) {
        if(humanObj.fate.fate == null){
            createFate(humanObj);
        }
        if (checkFateBagSizeFull(humanObj, voList.size())) {
            Inform.sendMsg_error(humanObj, ErrorTip.FateBagFull);
            return;
        }

        MsgFate.fate_update_s2c.Builder msg = MsgFate.fate_update_s2c.newBuilder();
        msg.setType(UPDATE_ADD);
        for(ProduceVo vo : voList){
            if(vo.type != ItemConstants.武魂){
                Log.temp.error("===类型错误，不是武魂，humanId={}. voList={}, log={}", humanObj.id, voList, log);
                continue;
            }
            for(Object obj : vo.objList){
                if(obj instanceof FateVo){
                    FateVo fateVo = (FateVo)obj;
                    humanObj.fate.fateBagMap.put(fateVo.id, fateVo);
                    msg.addFateList(fateVo.toProto(fateVo.id));

                    Human human = humanObj.getHuman();
                    LogOp.log("addFate",
                            human.getId(),
                            Port.getTime(),
                            Utils.formatTime(Port.getTime(), "yyyy-MM-dd"),
                            human.getAccount(),
                            human.getName(),
                            human.getLevel(),
                            human.getServerId(),
                            fateVo.id,
                            fateVo.sn,
                            1
                    );
                    ConfFate conf = ConfFate.get(fateVo.sn);
                    Event.fire(EventKey.UPDATE_NEWS, "humanObj", humanObj, "newsCondition", NewsConditionTypeKey.FateQuality, "value", conf.quality);
                } else {
                    Log.temp.error("武魂对象不对，id={}, list={}， vo={}, log={}", humanObj.id, vo.objList, vo, log);
                }
            }
        }
        saveFateBag(humanObj);
        humanObj.sendMsg(msg);
        if (checkFateBagSizeFull(humanObj, FATE_BAG_REST_NUM)) {
            Inform.sendMsg_error(humanObj, ErrorTip.FateBagAlmostFull);// 背包快满的时候发个提示
        }
    }

    public int bagSize(HumanObject humanObj) {
        return humanObj.fate.fateBagMap.size();
    }

    /**
     * 更新玩家属性
     */
    public void updatePropCalcAndPower(HumanObject humanObj){
        PropCalc propCalc = new PropCalc();
        int power = 0;
        if (humanObj.fate.fate != null) {
            Map<Integer, Long> posMap = humanObj.fate.getPosMap();
            for (Map.Entry<Integer, Long> entry : posMap.entrySet()) {
                long id = entry.getValue();
                FateVo fateVo = humanObj.fate.fateBagMap.get(id);
                if (fateVo == null) {
                    continue;
                }
                ConfFateLevel_0 confFateLevel0 = ConfFateLevel_0.get(fateVo.sn,fateVo.level);
                if (confFateLevel0 == null) {
                    continue;
                }
                propCalc.plus(confFateLevel0.attr);
                power += confFateLevel0.power;
            }
            List<Define.p_passive_skill> passSkillList = getPassSkill(humanObj.getHuman2().getFateSkillMap());
            if (passSkillList != null) {
                for (Define.p_passive_skill msg : passSkillList) {
                    ConfSkillLevel_0 confSkillLevel0 = ConfSkillLevel_0.get(msg.getSkillId(), msg.getSkillLv());
                    if (confSkillLevel0 == null) {
                        continue;
                    }
                    if (confSkillLevel0.attrType == null || confSkillLevel0.attrType[0] != 1) {
                        propCalc.plus(confSkillLevel0.ownEffect);
                    }
                }
            }
        }
        humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.fate, propCalc.toJSONStr());
        HumanManager.inst().updatePowerPar(humanObj, EModule.fate, power);
        PropManager.inst().propCalc(humanObj, CombatChangeLog.武魂);
    }

    /**
     * 添加新的武魂
     */
    public Map<Long, FateVo> addFateVo(HumanObject humanObj, Param... addFateParams) {
        Human human = humanObj.getHuman();
        Map<Long, FateVo> newFateVoMap = new LinkedHashMap<>();
        for (Param fateParam : addFateParams) {
            FateVo vo = new FateVo();
            vo.sn = fateParam.get("sn");
            vo.level = fateParam.get("level");
            if (fateParam.containsKey("skillSn")) {
                vo.skillSn = fateParam.get("skillSn");
                vo.skillLevel = fateParam.containsKey("skillLevel") ? fateParam.get("skillLevel") : 1;
            }
            long id = humanObj.fate.genId();
            humanObj.fate.fateBagMap.put(id, vo);
            newFateVoMap.put(id, vo);

            LogOp.log("addFate",
                    human.getId(),
                    Port.getTime(),
                    Utils.formatTime(Port.getTime(), "yyyy-MM-dd"),
                    human.getAccount(),
                    human.getName(),
                    human.getLevel(),
                    human.getServerId(),
                    id,
                    vo.sn,
                    1
            );

            ConfFate conf = ConfFate.get(vo.sn);
            Event.fire(EventKey.UPDATE_NEWS, "humanObj", humanObj, "newsCondition", NewsConditionTypeKey.FateQuality, "value", conf.quality);
        }
        saveFateBag(humanObj);
        if (checkFateBagSizeFull(humanObj, FATE_BAG_REST_NUM)) {
            Inform.sendMsg_error(humanObj, ErrorTip.FateBagAlmostFull);// 背包快满的时候发个提示
        }
        return newFateVoMap;
    }

    /**
     * 计算武魂从1级升级到现在等级的消耗
     */
    public Map<Integer, Integer> caculateFateLevelCost(FateVo fateVo) {
        Map<Integer, Integer> result = new HashMap<>();
        for (int i = 1; i < fateVo.level; ++i) {
            ConfFateLevel_0 conf = ConfFateLevel_0.get(fateVo.sn, i);
            if (conf == null) {
                continue;
            }
            result.put(conf.expend[0], conf.expend[1] + result.getOrDefault(conf.expend[0], 0));
        }
        return result;
    }

    /**
     * 计算武魂从1级升级到现在等级的消耗
     */
    public int[][] caculateFateLevelCostWithoutCombine(FateVo fateVo) {
        List<int[]> costList = new ArrayList<>();
        for (int i = 1; i < fateVo.level; ++i) {
            ConfFateLevel_0 conf = ConfFateLevel_0.get(fateVo.sn, i);
            if (conf == null) {
                continue;
            }
            costList.add(new int[] {
                    conf.expend[0],
                    conf.expend[1]
            });
        }
        return costList.toArray(new int[0][]);
    }

    /**
     * 获取武魂激活的被动效果
     */
    public List<Define.p_passive_skill> getPassSkill(String skillStr) {
        if (Utils.isNullOrEmptyJSONString(skillStr)) {
            return null;
        }
        List<Define.p_passive_skill> skillList = new ArrayList<>();
        if (Utils.isJSONString(skillStr)) {
            // 旧的格式，就是json存储
            Map<Integer, Integer> skillMap = Utils.jsonToMapIntInt(skillStr);
            for (Map.Entry<Integer, Integer> entry : skillMap.entrySet()) {
                Define.p_passive_skill.Builder skillMsg = Define.p_passive_skill.newBuilder();
                skillMsg.setSkillId(entry.getKey());
                skillMsg.setSkillLv(entry.getValue());
                skillList.add(skillMsg.build());
            }
        } else {
            // 使用新的格式解析，二维数组的字符串
            int[][] skills = Utils.parseIntArray2(skillStr);
            if (skills == null) {
                return null;
            }
            for (int[] skill : skills) {
                Define.p_passive_skill.Builder skillMsg = Define.p_passive_skill.newBuilder();
                skillMsg.setSkillId(skill[0]);
                skillMsg.setSkillLv(skill[1]);
                skillList.add(skillMsg.build());
            }
        }
        return skillList;
    }

    /**
     * 功能解锁
     */
    @Listener(EventKey.FUNCTION_OPEN)
    public void unlock(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if(humanObj.fate.fate != null){
            return;
        }
        if(!humanObj.isModUnlock(52)){
            return;
        }
        createFate(humanObj);
    }

    private void createFate(HumanObject humanObj) {
        FateData data = humanObj.fate;
        data.fate = new Fate();
        data.fate.setId(humanObj.id);
        if (ConfGlobal.get(401).intArray[0] == 0) {
            Map<Integer, Long> posMap = new HashMap<>();
            posMap.put(1, 0L);
            data.fate.setPosMap(data.getPosMapStr(posMap));
        }
        data.fate.persist();
        init(humanObj);
    }

    public boolean checkFateBagSizeFull(HumanObject humanObj, int addNum) {
        return (FateManager.inst().bagSize(humanObj) + addNum) >= ConfGlobal.get(ConfGlobalKey.fate_bag_limit.SN).value;
    }

    public boolean checkFateDungeonDropFullBag(HumanObject humanObj, int level) {
        ConfFateChapter conf = ConfFateChapter.get(level);
        if(conf == null){
            Log.temp.error("ConfFateChapter配置表中没有{}级的章节配置", level);
            return false;
        }
        int num = 0;
        for (int[] items : conf.first_reward) {
            int itemSn = items[0];
            ConfGoods confGoods = ConfGoods.get(itemSn);
            num += (confGoods.type == ItemConstants.礼包) ? items[1] : 0;
        }
        return checkFateBagSizeFull(humanObj, num);
    }

    public void handleFatePraySetAutoC2S(HumanObject humanObj, int isAutoDismantle, int dismantleType) {
        Fate fate = humanObj.fate.fate;
        if(QUALITY_DECOMPOSITION_MAP.get(dismantleType) == null){
            Log.game.error("dismantleType参数错误");
            return;
        }
        if (isAutoDismantle != fate.getIsAutoDismantle()) {
            fate.setIsAutoDismantle(isAutoDismantle);
        }
        if (dismantleType != fate.getDismantleType()) {
            fate.setDismantleType(dismantleType);
        }
        MsgFate.fate_pray_set_auto_s2c.Builder msg = MsgFate.fate_pray_set_auto_s2c.newBuilder();
        msg.setIsAutoDismantle(isAutoDismantle);
        msg.setDismantleType(dismantleType);
        humanObj.sendMsg(msg);
    }

    public int getFateShowSn(HumanObject humanObj) {
        if (humanObj.fate.fate == null) {
            return 0;
        }
        int showSn = humanObj.getHuman2().getFateShow();
        if (showSn == 0) {
            Map<Integer, Long> posMap = humanObj.fate.getPosMap();
            for (Long id : posMap.values()) {
                FateVo fateVo = humanObj.fate.fateBagMap.get(id);
                if (fateVo != null) {
                    showSn = fateVo.sn;
                    humanObj.getHuman2().setFateShow(showSn);
                    break;
                }
            }
        }
        return showSn;
    }
}
