package org.gof.demo.worldsrv.instance;

import com.pwrd.op.LogOp;
import org.apache.commons.collections.map.HashedMap;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.*;
import org.gof.core.support.function.GofFunction1;
import org.gof.core.support.observer.Listener;
import org.gof.demo.battlesrv.msgHandler.SkillManager;
import org.gof.demo.worldsrv.activity.ActivityControlType;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlChallengeData;
import org.gof.demo.worldsrv.angel.AngelManager;
import org.gof.demo.worldsrv.angel.EAngelType;
import org.gof.demo.worldsrv.artifact.ArtifactManager;
import org.gof.demo.worldsrv.character.HumanDailyResetInfo;
import org.gof.demo.worldsrv.character.HumanDataVO;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.common.GameServiceProxy;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.doubleChapter.DoubleChapterManager;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.fate.FateManager;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.guild.GuildManager;
import org.gof.demo.worldsrv.human.HumanData;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.human.MoneyManager;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.item.ItemConstants;
import org.gof.demo.worldsrv.item.ItemManager;
import org.gof.demo.worldsrv.msg.*;
import org.gof.demo.worldsrv.pet.PetManager;
import org.gof.demo.worldsrv.placingReward.Calculator.AbstractCalculator;
import org.gof.demo.worldsrv.placingReward.Calculator.RewardCalculatorTypeFactory;
import org.gof.demo.worldsrv.placingReward.PlacingRewardType;
import org.gof.demo.worldsrv.placingReward.PlacingRewardVo;
import org.gof.demo.worldsrv.privilege.PrivilegeManager;
import org.gof.demo.worldsrv.privilege.PrivilegeType;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.rank.RankInfo;
import org.gof.demo.worldsrv.rank.RankManager;
import org.gof.demo.worldsrv.rank.RankParamKey;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.TokenItemType;
import org.gof.demo.worldsrv.support.Util;
import org.gof.demo.worldsrv.support.enumKey.*;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;
import org.gof.demo.worldsrv.task.TaskManager;
import org.gof.demo.worldsrv.team.TeamManager;
import org.gof.demo.worldsrv.team.TeamMember;
import org.gof.demo.worldsrv.worldBoss.WorldBossServiceProxy;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

public class InstanceManager extends ManagerBase {

    /**
     * 获取实例
     *
     * @return
     */
    public static InstanceManager inst() {
        return inst(InstanceManager.class);
    }

    public void _msg_dungeon_list_c2s(HumanObject humanObj) {
        HumanManager.inst().sendMsg_dungeon_list_s2c(humanObj);
    }

    /**
     * 通关副本
     *
     * <AUTHOR>
     * @Date 2024/3/11
     * @Param
     */
    public void passRep(HumanObject humanObj, int repSn, int repType) {
        // 先处理通关多组件key事件
        Event.fireEx(EventKey.INSTANCE_PASS, repType, "humanObj", humanObj, "repType", repType, "num", 1, "repSn", repSn);
    }

    @Listener(EventKey.INSTANCE_PASS)
    public void _on_INSTANCE_PASS(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        int repType = Utils.getParamValue(param, "repType", 0);
        int num = Utils.getParamValue(param, "num", 0);
//		int repSn = Utils.getParamValue(param, "repSn", 0);
        if (repType == InstanceConstants.CHAPTER_1) {// 主线副本说不存在，先不记录
            return;
        }

        Map<Integer, Integer> repTypeNumMap = humanObj.operation.repTypeNumMap;
        int yetNum = 0;
        if (repTypeNumMap.containsKey(repType)) {
            yetNum = repTypeNumMap.get(repType);
        }
        int newNum = yetNum + num;
        repTypeNumMap.put(repType, newNum);
    }

    /**
     * 满足条件
     *
     * <AUTHOR>
     * @Date 2024/3/25
     * @Param
     */
    private boolean isMeetConditions(HumanObject humanObj, int repType, String power, int time, int levelDifficulty, boolean isDungeon) {
        if (!isCombat(humanObj.getHuman().getCombat(), power)) {
//            Inform.sendMsg_error(humanObj, 500);//"战力不满足条件，无法通关！"
            Log.temp.error("战力不满足条件，无法通关！");
            return false;
        }
        if (!isTime(humanObj.useTimeSec(repType), time, isDungeon)) {
//            Inform.sendMsg_error(humanObj, 501);//"时间不满足，无法通关！"
            Log.temp.error("时间不满足，无法通关！");
            return false;
        }
        int difficulty = humanObj.operation.getRepMaxDiffculty(repType);
        if (difficulty + 1 < levelDifficulty) {
            Log.temp.error("难度不等，无法通关！{} {}", repType, difficulty);
            return false;
        }
        return true;
    }

    /**
     * 战力是否满足
     *
     * <AUTHOR>
     * @Date 2024/3/25
     * @Param
     */
    private boolean isCombat(String combat, String power) {
        BigDecimal combatBD = new BigDecimal(combat);

        BigDecimal powerBD = new BigDecimal(power);
        int value = ConfGlobal.get(ConfGlobalKey.副本战力检测系数百分比.SN).value;
        // 策划要求除以3
        powerBD = powerBD.multiply(BigDecimal.valueOf(value)).divide(BigDecimal.valueOf(100), RoundingMode.HALF_UP).divide(BigDecimal.valueOf(3), RoundingMode.HALF_UP);
        int result = combatBD.compareTo(powerBD);
        if (result < 0) {// -1 combatBD 小于 powerBD ， 0 相等， 1 combatBD 大于 powerBD
            return false;
        }
        return true;
    }

    /**
     * 时间是否满足
     *
     * <AUTHOR>
     * @Date 2024/3/25
     * @Param
     */
    private boolean isTime(int useSec, int time, boolean isDungeon) {
        if (isDungeon) {
            return true;
        }
        if (useSec <= 0) {
            return true;
        }
        if (time <= 0) {
            return true;
        }
        if (useSec > time) {
            return false;
        }
        return true;
    }

    private void saveRepTypeTenDiff(Human3 human, int repType, int value) {
        Map<Integer, Integer> repTypeTenDiffMap = Utils.jsonToMapIntInt(human.getRepTypeTenDiffMap());
        int old = Utils.intValue(repTypeTenDiffMap.get(repType));
        if (old >= value) {
            return;
        }
        repTypeTenDiffMap.put(repType, value);
        human.setRepTypeTenDiffMap(Utils.mapIntIntToJSON(repTypeTenDiffMap));
    }

    @Listener(value = EventKey.INSTANCE_PASS, subInt = InstanceConstants.CHAPTER_1)
    public void _on_INSTANCE_PASS_CHAPTER_1(Param param) {
        // 主线副本
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        int repType = Utils.getParamValue(param, "repType", 0);
        int num = Utils.getParamValue(param, "num", 0);
        int repSn = Utils.getParamValue(param, "repSn", 0);

        Human2 human = humanObj.getHuman2();
        int repSnNow = human.getRepSn();
        if (repSnNow > repSn) {
//			Map<Integer, Integer> dropAllMap = GlobalConfVal.getPropReward(repSn, false);
//			// 给通关奖励
//			ProduceManager.inst().rewardProduceMap(humanObj, dropAllMap, MoneyItemLogKey.通关副本类型1);
            return;
        }
        human.setRepSn(repSn + 1);
        // 给首通奖励
//		Map<Integer, Integer> dropAllMap = GlobalConfVal.getPropReward(repSn, true);
//		ProduceManager.inst().rewardProduceMap(humanObj, dropAllMap, MoneyItemLogKey.通关副本类型1);

        // 首次通关才检测
        RankManager.inst().repRankUpdate(humanObj);
        SkillManager.inst().checkSkillPos(humanObj);
        PetManager.inst().checkPetPos(humanObj);
        HumanManager.inst().checkFuncOpen(humanObj);

        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_主线, TaskConditionTypeKey.TASK_TYPE_8);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_功能预告, TaskConditionTypeKey.TASK_TYPE_8);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_冒险任务, TaskConditionTypeKey.TASK_TYPE_8);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_成就任务, TaskConditionTypeKey.TASK_TYPE_8);
        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_8, 0);
        TaskManager.inst().checkRepType(humanObj, repType, num);

//		dRewardList.addAll(to_p_rewardList(dropAllMap));
//		sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, dRewardList);
//		human.update();

        Event.fire(EventKey.UPDATE_NEWS, "humanObj", humanObj, "newsCondition", NewsConditionTypeKey.MainRepPass, "value", repSn);
    }

    @Listener(value = EventKey.INSTANCE_PASS, subInt = InstanceConstants.COINCHAPTER_2)
    public void _on_INSTANCE_PASS_COINCHAPTER_2(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        int repType = Utils.getParamValue(param, "repType", 0);
        int num = Utils.getParamValue(param, "num", 0);
        int repSn = Utils.getParamValue(param, "repSn", 0);
        // 突袭神灯小偷
        ConfCoinChapter conf = ConfCoinChapter.get(repSn);
        if (conf == null) {
            Log.temp.error("===ConfCoinChapter配表错误， not find sn={}", repSn);
            return;
        }
        Human human = humanObj.getHuman();
        if (!isCombat(human.getCombat(), conf.power)) {
//            Inform.sendMsg_error(humanObj, 500);
            Log.temp.error("战力不满足条件，无法通关！");
//			return;
        }
        boolean isDungeon = Utils.getParamValue(param, "isDungeon", false);
        int difficulty = humanObj.operation.getRepMaxDiffculty(repType);
        if (difficulty + 1 < conf.level) {
            return;
        }
        if (!isTime(humanObj.useTimeSec(repType), conf.time, isDungeon)) {
            Inform.sendMsg_error(humanObj, 501);//"时间不满足，无法通关！"
            Log.temp.error("时间不满足，无法通关！");
//			return;
        }
        int[] cost = GlobalConfVal.getRepCost(repType);
        if (cost == null) {
            Log.temp.error("===无消耗？？， repType={}", repType);
            return;
        }
        ReasonResult result = ProduceManager.inst().canCostIntArr(humanObj, cost);
        if (!result.success) {
            return;
        }
        ProduceManager.inst().costIntArr(humanObj, cost, MoneyItemLogKey.通关副本类型2);

        humanObj.operation.setRepMaxDiffculty(repType, conf.sn);
        // 给奖励
        int[][] addReward = AngelManager.inst().getDungeonRewardEffects(humanObj);
        Map<Integer, Integer> rewardMap = Utils.intArrToIntMap(new HashMap<>(), conf.reward);
        for (int[] ints : addReward) {
            if (ints[0] == InstanceConstants.COINCHAPTER_2) {
                if(rewardMap.containsKey(ints[1])) {
                    if(ints[2] == EAngelType.REWARD_PPT) {
                        rewardMap.put(ints[1], rewardMap.get(ints[1]) + rewardMap.get(ints[1]) * ints[3] / 10000);
                    } else {
                        rewardMap.put(ints[1], rewardMap.get(ints[1]) + ints[3]);
                    }
                }
            }
        }
        ProduceManager.inst().produceAdd(humanObj, rewardMap, MoneyItemLogKey.通关副本类型2);
        List<Define.p_reward> rewardList = to_p_rewardList(rewardMap);
        List<Define.p_key_value> extList = new ArrayList<>();
        if (isDungeon) {
            sendMsg_dungeon_sweep_s2c(humanObj, repType, rewardList);
        } else {
            sendMsg_dungeon_result_s2c(humanObj, 0, repType, repSn, InstanceConstants.win, rewardList, extList);
        }
        sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_5001, rewardList);

        TaskManager.inst().checkRepType(humanObj, repType, num);
//		human.update();
    }

    @Listener(value = EventKey.INSTANCE_PASS, subInt = InstanceConstants.DIAMONDCHAPTER_3)
    public void _on_INSTANCE_PASS_DIAMONDCHAPTER_3(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        int repType = Utils.getParamValue(param, "repType", 0);
        int num = Utils.getParamValue(param, "num", 0);
        int repSn = Utils.getParamValue(param, "repSn", 0);
        boolean isDungeon = Utils.getParamValue(param, "isDungeon", false);
        // 挑战冰巢龙穴
        ConfDiamondChapter conf = ConfDiamondChapter.get(repSn);
        if (conf == null) {
            Log.temp.error("===ConfDiamondChapter配表错误, not find sn={}", repSn);
            return;
        }
        if (!isMeetConditions(humanObj, repType, conf.power, conf.time, conf.level, isDungeon)) {
            Log.temp.error("条件不满足，无法通关！");
//			return;
        }
        int[] cost = GlobalConfVal.getRepCost(repType);
        if (cost == null) {
            Log.temp.error("===无消耗？？， repType={}", repType);
            return;
        }
        ReasonResult result = ProduceManager.inst().canCostIntArr(humanObj, cost);
        if (!result.success) {
            return;
        }
        ProduceManager.inst().costIntArr(humanObj, cost, MoneyItemLogKey.通关副本类型3);

        int nextSn = conf.next_part;
        while (nextSn > 0) {
            conf = ConfDiamondChapter.get(nextSn);
            if (conf == null) {
                Log.temp.error("===ConfDiamondChapter配表错误, not find sn={}", nextSn);
                return;
            }
            nextSn = conf.next_part;
        }

        humanObj.operation.setRepMaxDiffculty(repType, conf.level);
        // 给奖励
        ProduceManager.inst().produceAdd(humanObj, conf.reward, MoneyItemLogKey.通关副本类型3);
        List<Define.p_reward> rewardList = to_p_rewardList(conf.reward);
        List<Define.p_key_value> extList = new ArrayList<>();
        if (isDungeon) {
            sendMsg_dungeon_sweep_s2c(humanObj, repType, rewardList);
        } else {
            sendMsg_dungeon_result_s2c(humanObj, 0, repType, repSn, InstanceConstants.win, rewardList, extList);
            sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_5001, rewardList);
        }
        TaskManager.inst().checkRepType(humanObj, repType, num);
//		humanObj.getHuman().update();
    }

    @Listener(value = EventKey.INSTANCE_PASS, subInt = InstanceConstants.LEGACYTEAMCHAPTER_8)
    public void _on_INSTANCE_PASS_LEGACYTEAMCHAPTER_8(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        int repType = Utils.getParamValue(param, "repType", 0);
        int num = Utils.getParamValue(param, "num", 0);
        int repSn = Utils.getParamValue(param, "repSn", 0);
        // 是否是扫荡
        boolean isDungeon = Utils.getParamValue(param, "isDungeon", false);
        if (isDungeon) {
            // 扫荡的时候取已通关的最高难度给奖励
            int diff = humanObj.operation.getRepMaxDiffculty(repType);
            repSn = GlobalConfVal.getRepSn(repType, diff);
        }
        int value = Utils.getParamValue(param, "value", 0);
        // 守卫残垣古城
        ConfLegacyTeamChapter conf = ConfLegacyTeamChapter.get(repSn);
        if (conf == null) {
            Log.temp.error("===ConfLegacyTeamChapter配表错误, not find sn={}", repSn);
            return;
        }
        if (!isMeetConditions(humanObj, repType, conf.power, conf.time, conf.level, isDungeon)) {
            Log.temp.error("条件不满足，无法通关！");
//			return;
        }
        int multiple = 0;
        ConfGlobal confBonus = ConfGlobal.get(ConfGlobalKey.legacy_team_chapter_bonus.SN);
        int[][] intArr = Utils.parseIntArray2(confBonus.strValue);
        if (isDungeon) {
            // 扫荡的value=0，直接取满血的收益百分比
            multiple = intArr[intArr.length - 1][1];
        } else {
            for (int i = 0; i < intArr.length; i++) {
                if (value < intArr[i][0]) {
                    break;
                }
                multiple = intArr[i][1];
            }
        }
        int addValue = multiple - 100;
        if (addValue < 0) {
            addValue = 0;
        }
        boolean isPass = true;

        int[] cost = GlobalConfVal.getRepCost(repType);
        if (cost == null) {
            Log.temp.error("===无消耗？？， repType={}", repType);
            return;
        }

        ReasonResult result = ProduceManager.inst().canCostIntArr(humanObj, cost);
        if (!result.success) {
            isPass = false;
            ConfGlobal confGlobalCost = ConfGlobal.get(ConfGlobalKey.legacy_team_chapter_assist_time.SN);
            result = ProduceManager.inst().canCostProduce(humanObj, confGlobalCost.intArray[0], confGlobalCost.intArray[1]);
            if (!result.success) {
                TeamManager.inst()._msg_team_exit_c2s(humanObj, null);
                return;
            }
        }

        // 1是正常，2是援助
        if (isPass) {
            ProduceManager.inst().costIntArr(humanObj, cost, MoneyItemLogKey.组队副本);
            // 正常
            int nextSn = conf.next_part;
            while (nextSn > 0) {
                conf = ConfLegacyTeamChapter.get(nextSn);
                if (conf == null) {
                    Log.temp.error("===ConfLegacyTeamChapter配表错误, not find sn={}", nextSn);
                    break;
                }
                nextSn = conf.next_part;
            }
            humanObj.operation.setRepMaxDiffculty(repType, conf.level);

            if (value == 10 && !isDungeon) {// 防御值=10才能扫荡
                saveRepTypeTenDiff(humanObj.getHuman3(), repType, conf.level);
            }
            // 给奖励
            ProduceManager.inst().produceAdd(humanObj, conf.reward, multiple / 100, MoneyItemLogKey.通关副本类型8);

            List<Define.p_reward> rewardList = to_p_rewardList(conf.reward, multiple / 100);
            List<Define.p_key_value> extList = new ArrayList<>();
            if (addValue < 200) {
                extList.add(HumanManager.inst().to_p_key_value(ParamKey.repExtType_6, multiple).build());//奖励有加成的时候 K =6
            } else {
                extList.add(HumanManager.inst().to_p_key_value(ParamKey.repExtType_1, addValue).build());//奖励有加成的时候 K =1 V=额外比例
            }
            extList.add(HumanManager.inst().to_p_key_value(ParamKey.repExtType_1, addValue).build());

            if (isDungeon) {
                sendMsg_dungeon_sweep_s2c(humanObj, repType, rewardList);
            } else {
                sendMsg_team_result_s2c(humanObj, 0, InstanceConstants.win, InstanceConstants.rewardType_1, rewardList, extList, value);
            }
            int surplusNum = humanObj.operation.itemData.getItemNum(cost[0]);
            if (surplusNum <= 0) {
                TeamManager.inst()._msg_team_exit_c2s(humanObj, null);
            }
        } else {
            TeamManager.inst()._msg_team_exit_c2s(humanObj, null);
            if (isDungeon) {
                return;
            }
            ConfGlobal confGlobalCost = ConfGlobal.get(ConfGlobalKey.legacy_team_chapter_assist_time.SN);
            if (!ProduceManager.inst().canCostProduce(humanObj, confGlobalCost.intArray[0], confGlobalCost.intArray[1]).success) {
                return;
            }
            //每日两次援助有奖励
            HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.teamAssistNum.getType());
            info.setValue(info.getValue() + 1);
            humanObj.saveDailyResetRecord();
            List<Define.p_reward> rewardList = new ArrayList<>();
            if (info.getValue() <= confGlobalCost.value) {
                // 援助奖励
                ProduceManager.inst().costItem(humanObj, confGlobalCost.intArray[0], confGlobalCost.intArray[1], MoneyItemLogKey.组队副本);
                ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.legacy_team_chapter_assist_reward.SN);
                ProduceManager.inst().produceAdd(humanObj, confGlobal.intArray, MoneyItemLogKey.通关副本类型8);
                rewardList = to_p_rewardList(confGlobal.intArray);
            }
            List<Define.p_key_value> extList = new ArrayList<>();
            sendMsg_team_result_s2c(humanObj, 0, InstanceConstants.win, InstanceConstants.rewardType_2, rewardList, extList, value);
        }
        TaskManager.inst().checkRepType(humanObj, repType, num);
//		humanObj.getHuman().update();


    }

    public void sendMsg_dungeon_sweep_s2c(HumanObject humanObj, int type, List<Define.p_reward> rewardList) {
        MsgDungeon.dungeon_sweep_s2c.Builder msg = MsgDungeon.dungeon_sweep_s2c.newBuilder();
        msg.setDungeonId(type);
        msg.addAllRewardList(rewardList);
        humanObj.sendMsg(msg);
    }

    @Listener(value = EventKey.INSTANCE_PASS, subInt = InstanceConstants.MOUNTCHAPTER_9)
    public void _on_INSTANCE_PASS_MOUNTCHAPTER_9(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        int repType = Utils.getParamValue(param, "repType", 0);
        int num = Utils.getParamValue(param, "num", 0);
        int repSn = Utils.getParamValue(param, "repSn", 0);
        double bossHp = Utils.getParamValue(param, "bossHp", 0.0);
        long hurt = Utils.getParamValue(param, "hurt", 0l);

        // 颠倒时序塔(坐骑副本)
        ConfMountChapter conf = ConfMountChapter.get(repSn);
        if (conf == null) {
            Log.temp.error("===ConfMountChapter配表错误， not find sn={}", repSn);
            return;
        }
        boolean isDungeon = Utils.getParamValue(param, "isDungeon", false);
        if (!isMeetConditions(humanObj, repType, conf.power, conf.time, conf.level, isDungeon)) {
            Log.temp.error("条件不满足，无法通关！");
//			return;
        }
        int[] cost = GlobalConfVal.getRepCost(repType);
        if (cost == null) {
            Log.temp.error("===无消耗？？， repType={}", repType);
            return;
        }

        ReasonResult result = ProduceManager.inst().canCostIntArr(humanObj, cost);
        if (!result.success) {
            return;
        }
        ProduceManager.inst().costIntArr(humanObj, cost, MoneyItemLogKey.通关副本类型9);

        int nextSn = conf.next_part;
        while (nextSn > 0) {
            conf = ConfMountChapter.get(nextSn);
            if (conf == null) {
                Log.temp.error("===ConfMountChapter配表错误, not find sn={}", nextSn);
                return;
            }
            nextSn = conf.next_part;
        }
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.mount_chapter_bonus.SN);
        int[][] arrInt = Utils.parseIntArray2(confGlobal.strValue);
        double multiple = 1;
        for (int i = 0; i < arrInt.length; i++) {
            if (arrInt[i][0] >= bossHp || isDungeon) {
                multiple = arrInt[i][1] / 100;
            }
        }

        if(hurt == 100){// 客户端说进boss一定胜利，给奖励，但hurt大于0不通关 100通关
            humanObj.operation.setRepMaxDiffculty(repType, conf.level);
        }

        // 给奖励
        int[][] addReward = AngelManager.inst().getDungeonRewardEffects(humanObj);
        Map<Integer, Integer> rewardMap = Utils.intArrToIntMap(new HashMap<>(), conf.reward, multiple);
        for (int[] ints : addReward) {
            if (ints[0] == InstanceConstants.MOUNTCHAPTER_9) {
                if(rewardMap.containsKey(ints[1])) {
                    if(ints[2] == EAngelType.REWARD_PPT) {
                        rewardMap.put(ints[1], rewardMap.get(ints[1]) + rewardMap.get(ints[1]) * ints[3] / 10000);
                    } else {
                        rewardMap.put(ints[1], rewardMap.get(ints[1]) + ints[3]);
                    }
                }
            }
        }
        ProduceManager.inst().produceAdd(humanObj, rewardMap, MoneyItemLogKey.通关副本类型9);

        List<Define.p_reward> rewardList = to_p_rewardList(rewardMap);
        List<Define.p_key_value> extList = new ArrayList<>();
        if (multiple < 2) {
            extList.add(HumanManager.inst().to_p_key_value(ParamKey.repExtType_6, (long) ((multiple) * 100)).build());//奖励有加成的时候 K =1 V=额外比例
        } else {
            extList.add(HumanManager.inst().to_p_key_value(ParamKey.repExtType_1, (long) ((multiple - 1) * 100)).build());//奖励有加成的时候 K =1 V=额外比例
        }

        if (isDungeon) {
            sendMsg_dungeon_sweep_s2c(humanObj, repType, rewardList);
        } else {
            sendMsg_dungeon_battle_result_s2c(humanObj, 0, repType, repSn, 0, rewardList, extList);
            sendMsg_dungeon_result_s2c(humanObj, 0, repType, repSn, InstanceConstants.win, rewardList, extList);
        }
        TaskManager.inst().checkRepType(humanObj, repType, num);
//		humanObj.getHuman().update();
    }

    public List<Define.p_reward> to_p_rewardList(int[][] itemNumArr) {
        return to_p_rewardList(itemNumArr, 1);
    }

    public List<Define.p_reward> to_p_rewardList(int[][] itemNumArr, double multiple) {
        if (multiple <= 0 || itemNumArr == null || itemNumArr.length == 0) {
//			Log.temp.error("=====可能出问题了，multiple={}, itemNumArr={}", multiple, itemNumArr);
            return new ArrayList<>();
        }
        List<Define.p_reward> rewardList = new ArrayList<>();
        Map<Integer, Integer> rewardResMap = new HashMap<>();
        rewardResMap = Utils.intArrToIntMap(rewardResMap, itemNumArr);
        for (Map.Entry<Integer, Integer> entry : rewardResMap.entrySet()) {
            Define.p_reward.Builder builder = Define.p_reward.newBuilder();
            builder.setGtid(entry.getKey());
            builder.setNum((int) (entry.getValue() * multiple));
            rewardList.add(builder.build());
        }
        return rewardList;
    }

    /**
     * 与上面的区别是，这个可以重复道具，上面道具sn重复会吞掉
     */
    public List<Define.p_reward> to_p_rewardList2(int[][] itemNumArr) {
        List<Define.p_reward> rewardList = new ArrayList<>();
        for (int[] ints : itemNumArr) {
            Define.p_reward.Builder builder = Define.p_reward.newBuilder();
            builder.setGtid(ints[0]);
            builder.setNum(ints[1]);
            rewardList.add(builder.build());
        }
        return rewardList;
    }

    public List<Define.p_reward> to_p_rewardList(Map<Integer, Integer> itemMap) {
        List<Define.p_reward> rewardList = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : itemMap.entrySet()) {
            Define.p_reward.Builder builder = Define.p_reward.newBuilder();
            builder.setGtid(entry.getKey());
            builder.setNum(entry.getValue());
            rewardList.add(builder.build());
        }
        return rewardList;
    }

    public List<Define.p_reward> to_p_rewardList(int[] itemNumArr) {
        return to_p_rewardList(itemNumArr, 1);
    }

    public List<Define.p_reward> to_p_rewardList(int[] itemNumArr, double multiple) {
        List<Define.p_reward> rewardList = new ArrayList<>();
        Map<Integer, Integer> rewardResMap = new HashMap<>();
        rewardResMap = Utils.intArrToIntMap(rewardResMap, itemNumArr);
        for (Map.Entry<Integer, Integer> entry : rewardResMap.entrySet()) {
            Define.p_reward.Builder builder = Define.p_reward.newBuilder();
            builder.setGtid(entry.getKey());
            builder.setNum((int) (entry.getValue() * multiple));
            rewardList.add(builder.build());
        }
        return rewardList;
    }

    @Listener(value = EventKey.INSTANCE_PASS, subInt = InstanceConstants.ARTIFACTGEMCHAPTER_22)
    public void _on_INSTANCE_PASS_ARTIFACTGEMCHAPTER_22(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        int repType = Utils.getParamValue(param, "repType", 0);
        int num = Utils.getParamValue(param, "num", 0);
        int repSn = Utils.getParamValue(param, "repSn", 0);
        // 探秘焚焰神殿
        ConfArtifactGemChapter conf = ConfArtifactGemChapter.get(repSn);
        if (conf == null) {
            Log.temp.error("===ConfArtifactGemChapter 配表错误, not find sn={}", repSn);
            return;
        }
        boolean isDungeon = Utils.getParamValue(param, "isDungeon", false);
        if (!isMeetConditions(humanObj, repType, conf.power, conf.time, conf.level, isDungeon)) {
            Log.temp.error("条件不满足，无法通关！");// TOOD 不能过滤
        }
        int[] cost = GlobalConfVal.getRepCost(repType);
        if (cost == null) {
            Log.temp.error("===无消耗？？， repType={}", repType);
            return;
        }

        ReasonResult result = ProduceManager.inst().canCostIntArr(humanObj, cost);
        if (!result.success) {
            Log.temp.error("===材料不足, cost={}", cost);
            return;
        }
        ProduceManager.inst().costIntArr(humanObj, cost, MoneyItemLogKey.通关副本类型22);

        humanObj.operation.setRepMaxDiffculty(repType, conf.level);

        Map<Integer, Integer> dropMap = new HashedMap();
        for (int i = 0; i < conf.reward.length; i++) {
            int[] intArr = conf.reward[i];
            for (int m = 0; m < intArr.length; m += 2) {
                int key = intArr[m];
                int value = intArr[m + 1];
                for (int n = 0; n < value; n++) {
                    dropMap = Utils.mergeMap(dropMap, ProduceManager.inst().getDropMap(key));
                }
            }
        }

        // 给奖励
        ProduceManager.inst().produceAdd(humanObj, dropMap, MoneyItemLogKey.通关副本类型22);
        sendMsg_dungeon_result_s2c(humanObj, 0, repType, repSn, InstanceConstants.win, to_p_rewardList(dropMap), new ArrayList<>());
        TaskManager.inst().checkRepType(humanObj, repType, num);
    }

    /**
     * 这是个活动副本
     */
    @Listener(value = EventKey.INSTANCE_PASS, subInt = InstanceConstants.SEVENTRIALCHAPTER_23)
    public void _on_INSTANCE_PASS_SEVENTRIALCHAPTER_23(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        int repType = Utils.getParamValue(param, "repType", 0);
        int repSn = Utils.getParamValue(param, "repSn", 0);
        long hurt = Utils.getParamValue(param, "hurt", 0L);
        ConfSevenTrialChapter confChapter = ConfSevenTrialChapter.get(repSn);
        if (confChapter == null) {
            Log.activity.error("ConfSevenTrialChapter为空，sn={}", repSn);
            return;
        }
        int actType = ActivityControlType.Act_72;
        if (!ActivityManager.inst().isActivityOpen(humanObj, actType)) {
            Log.activity.error("活动没开打副本不算，actType={}", actType);
            return;
        }
        ReasonResult rr = ProduceManager.inst().checkAndCostItem(humanObj, ItemConstants.goods_勇者试炼钥匙, 1, MoneyItemLogKey.勇者试炼);
        if (!rr.success) {
            Inform.sendMsg_error(humanObj, ErrorTip.ItemNotEnough);
            return;
        }
        Map<Integer, Integer> itemMap = new HashMap<>(0);
        int currRep = ConfSevenTrialChapter.get(repSn + 1) == null ? repSn : repSn + 1;// 防一手报错
        long resetHp = hurt;
        long score = 0L;
        for (int sn = 1; sn < currRep; sn++) {
            ConfSevenTrialChapter conf = ConfSevenTrialChapter.get(sn);
            score += conf.point;// 计算积分
            ConfUnit confUnit = ConfUnit.get((long) conf.bossId);// 这边一定要强转long，不然取不到数据
            resetHp -= confUnit.hp;// 扣除血量
            itemMap = Util.intArrToIntMap(itemMap, conf.reward);// 累计奖励
        }
        ConfSevenTrialChapter conf = ConfSevenTrialChapter.get(currRep);
        ConfUnit confUnit = ConfUnit.get((long) conf.bossId);
        score += (resetHp * 1.0f / confUnit.hp) * conf.point;// 根据剩余血量算本层的积分
        itemMap = Utils.mergeMap(itemMap, ProduceManager.inst().producePreDrop(conf.current_reward));

        ProduceManager.inst().produceAdd(humanObj, itemMap, MoneyItemLogKey.勇者试炼);
        List<Define.p_reward> pRewardList = to_p_rewardList(itemMap);

        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_11, 1, InstanceConstants.SEVENTRIALCHAPTER_23, 1);

        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(actType);
        ControlChallengeData data = (ControlChallengeData) controlData.getControlData();
        data.score += score;
        final long addScore = score;
        controlData.updateControlData();

        ConfActivityTerm confActivityTerm = ActivityManager.inst().getActivityTerm(actType, controlData.getActControlData().getRound());
        ConfRanktype confRanktype = ConfRanktype.get(confActivityTerm.rank_id);
        String rankKey = RankManager.inst().getRedisRankTypeKey(humanObj.getHuman().getServerId(), confRanktype);
        RankManager.inst().updateRankScore(humanObj, rankKey, confRanktype, data.score, () -> {
            fillDungeonExtList(humanObj, InstanceConstants.SEVENTRIALCHAPTER_23, (extList) -> {
                extList.add(HumanManager.inst().to_p_key_value(InstanceConstants.dungeonType_5, repSn).build());
                extList.add(HumanManager.inst().to_p_key_value(InstanceConstants.dungeonType_7, addScore).build());
                sendMsg_dungeon_result_s2c(humanObj, 0, repType, repSn, InstanceConstants.win, pRewardList, extList);
            });
        });
    }

    @Listener(value = EventKey.INSTANCE_PASS, subInt = InstanceConstants.DARKTRIALCHAPTER_28)
    public void _on_INSTANCE_PASS_DARKTRIALCHAPTER_28(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        int repType = Utils.getParamValue(param, "repType", 0);
        int repSn = Utils.getParamValue(param, "repSn", 0);
        // 暗黑试炼-战士
        passDarkTrialChapter(humanObj, repType, repSn, MoneyItemLogKey.通关副本类型28);
    }


    @Listener(value = EventKey.INSTANCE_PASS, subInt = InstanceConstants.DARKTRIALCHAPTER_29)
    public void _on_INSTANCE_PASS_DARKTRIALCHAPTER_29(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        int repType = Utils.getParamValue(param, "repType", 0);
        int repSn = Utils.getParamValue(param, "repSn", 0);
        // 暗黑试炼-弓手
        passDarkTrialChapter(humanObj, repType, repSn, MoneyItemLogKey.通关副本类型29);
    }

    @Listener(value = EventKey.INSTANCE_PASS, subInt = InstanceConstants.DARKTRIALCHAPTER_30)
    public void _on_INSTANCE_PASS_DARKTRIALCHAPTER_30(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        int repType = Utils.getParamValue(param, "repType", 0);
        int repSn = Utils.getParamValue(param, "repSn", 0);
        // 暗黑试炼-法师
        passDarkTrialChapter(humanObj, repType, repSn, MoneyItemLogKey.通关副本类型30);
    }

    /**
     * 暗黑试炼
     *
     * <AUTHOR>
     * @Date 2024/3/25
     * @Param
     */
    private void passDarkTrialChapter(HumanObject humanObj, int repType, int repSn, MoneyItemLogKey logKey) {
        // 暗黑试炼
        ConfDarkTrialChapter conf = ConfDarkTrialChapter.get(repSn);
        if (conf == null) {
            Log.temp.error("===ConfDarkTrialChapter配表错误， not find sn={}", repSn);
            return;
        }
        Human3 human = humanObj.getHuman3();
        int serverId = humanObj.getHuman().getServerId();
        GameServiceProxy prx = GameServiceProxy.newInstance();
        prx.getServerGlobal(serverId);
        prx.listenResult((results, context) -> {
            ReasonResult rr = Utils.getParamValue(results, "result", new ReasonResult(false, ErrorTip.SystemDefault));
            if (!rr.success) {
                Log.game.error("暗黑试炼副本通关查询ServerGlobal失败, serverId={}", serverId);
                return;
            }
            ServerGlobal serverGlobal = Utils.getParamValue(results, "serverGlobal", null);
            if (serverGlobal == null || repType != serverGlobal.getDarkTrialChapterSn()) {
                return;
            }
            if (!isMeetConditions(humanObj, repType, conf.power, conf.time, conf.level, false)) {
                Log.temp.error("条件不满足，无法通关！");
//			    return;
            }

            boolean isOne = false;
            int value = humanObj.operation.getRepMaxDiffculty(repType);
            if (value < conf.level) {
                humanObj.operation.setRepMaxDiffculty(repType, conf.level);
                isOne = true;
            }

            HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyDarkTrialChapter.getType());
            if (info.getValue() < conf.level) {
                info.setValue(conf.level);
                humanObj.saveDailyResetRecord();
            }

            Map<Integer, Integer> allMap = new HashMap<>();
            allMap = Utils.intArrToIntMap(allMap, conf.daily_reward);
            // 特权加成
            int privilegeValue = PrivilegeManager.inst().getPrivilegeEffectValue(humanObj, PrivilegeType.DARKTRAIL_REWARD);
            if (privilegeValue > 0) {
                for (Map.Entry<Integer, Integer> entry : allMap.entrySet()) {
                    int key = entry.getKey();
                    int num = entry.getValue();
                    num = (int) (num * (1 + privilegeValue / 100.0));
                    allMap.put(key, num);
                }
            }
            if (isOne) {
                allMap = Utils.intArrToIntMap(allMap, conf.first_reward);
            }

            // 给奖励
            ProduceManager.inst().produceAdd(humanObj, allMap, logKey);
            // 通知
            List<Define.p_reward> rewardList = to_p_rewardList(allMap);
            List<Define.p_key_value> extList = new ArrayList<>();
            sendMsg_dungeon_result_s2c(humanObj, 0, repType, repSn, InstanceConstants.win, rewardList, extList);

            TaskManager.inst().checkRepType(humanObj, repType, 1);
            sendMsg_dungeon_update_s2c(humanObj, repType);
        });
    }


    public void _msg_dungeon_type_c2s(HumanObject humanObj) {
        InstanceManager.inst()._msg_main_chapter_reward_info_c2s_type2(humanObj);
        sendMsg_dungeon_type_s2c(humanObj, InstanceConstants.CHAPTER_1);
    }

    public void sendMsg_dungeon_type_s2c(HumanObject humanObj, int type) {
        MsgDungeon.dungeon_type_s2c.Builder msg = MsgDungeon.dungeon_type_s2c.newBuilder();
        msg.setType(type);
        msg.setDungeonId(getRenTypeValue(humanObj, type));
//		msg.addData();// TODO
        humanObj.sendMsg(msg);
    }

    public int getRenTypeValue(HumanObject humanObj, int repType) {
        int value = 0;
        if (repType == InstanceConstants.CHAPTER_1) {
            value = humanObj.getHuman2().getRepSnClient();
        } else {
            value = humanObj.operation.getRepMaxDiffculty(repType);
        }
        return value;
    }

    public void _msg_main_chapter_result_c2s(HumanObject humanObj, int result, int partId, int manualOperators) {

        if (humanObj.enterTypeTime != 0 && (humanObj.enterTypeTime + 3 * Time.SEC) > Port.getTime()) {
            return;
        }
        Human2 human2 = humanObj.getHuman2();
        Human3 human3 = humanObj.getHuman3();

        int nextPartId = partId + 1;
        int remainder = partId % 5;

        if (result == InstanceConstants.win) {// 胜利
            if (partId != 0 && remainder == 0 && humanObj.getHuman3().getUnlimited() == 1) {
                human3.setUnlimited(0); // boss关胜利才是0
            }
            if (human3.getUnlimited() != 0 && nextPartId >= human2.getRepSn()) {
                int topId = (remainder == 0) ? partId - 5 + 1 : partId - remainder + 1;
                human2.setRepSnClient(topId);
            } else {
                human2.setRepSnClient(partId + 1);
            }
            if (nextPartId > human2.getRepSn()) {
                LogOp.log("Rep", humanObj.id, human2.getRepSn(), nextPartId, humanObj.name, Utils.formatTime(Port.getTime(), "yyyy-MM-dd HH:mm:ss"));
                passRep(humanObj, partId, InstanceConstants.CHAPTER_1);
                human2.setRepSn(nextPartId);
                ConfChapterVO confPreRepSn = GlobalConfVal.getConfChapterVO(partId - 1);
                ConfChapterVO confCurRepSn = GlobalConfVal.getConfChapterVO(nextPartId - 1);
                if (confPreRepSn != null && confCurRepSn != null && !Utils.isEquals(confPreRepSn.online_reward1, confCurRepSn.online_reward1)) {
                    reCalculatePlacingReward(humanObj, PlacingRewardType.ONLINE_REWARD_1, true);
                }
            }
        } else if (result == InstanceConstants.lose) {// 失败
            int topId = (remainder == 0) ? partId - 5 + 1 : partId - remainder + 1;
            human2.setRepSnClient(topId);
            if (remainder == 0) {
                human3.setUnlimited(1); // 是boss关失败才是1
                Log.temp.info("===boss关失败， humanId={}, account={},unlimited={}, repSn={}", humanObj.id, humanObj.account, human3.getUnlimited(), human2.getRepSn());
            }
        }
        human2.update();
        human3.update();
        sendMsg_main_chapter_result_s2c(humanObj, 0);
    }

    private int getTopPartId(ConfChapterVO conf, int partId) {
        int topPartId = conf.sn;
        if (conf.part > 1) {
            topPartId = partId - conf.part + 1;
            ConfChapterVO confTop = GlobalConfVal.getConfChapterVO(topPartId);
            if (confTop == null) {
                Log.temp.error("===ConfChapterVO 配表错误, not find sn={}", topPartId);
                return conf.sn; // 返回默认值
            }
        }
        return topPartId;
    }

    public void sendMsg_main_chapter_result_s2c(HumanObject humanObj, int code) {
        MsgMainChapter.main_chapter_result_s2c.Builder msg = MsgMainChapter.main_chapter_result_s2c.newBuilder();
        msg.setCode(code);
        msg.setPartId(humanObj.getHuman2().getRepSnClient());
        msg.setIsUnlimited(humanObj.getHuman3().getUnlimited());
        humanObj.sendMsg(msg);
        if (S.isTestLog || humanObj.isRepLog) {
            Log.temp.info("msg={}, humanId={}, name={}", msg, humanObj.id, humanObj.name);
        }
    }

    public void sendMsg_dungeon_update_s2c(HumanObject humanObj, int type) {
        sendMsg_dungeon_update_s2c(humanObj, type, false, 0);
    }

    public void sendMsg_dungeon_update_s2c(HumanObject humanObj, int type, boolean isUse, int level) {
        int curLv = humanObj.operation.getRepMaxDiffculty(type);
        int maxLv = curLv + 1;
        int num = 0;
        if (type == InstanceConstants.WORLDBOSS_13) {
            ConfGlobal confGlobalLimit = ConfGlobal.get(ConfGlobalKey.world_boss_challenge_limit.SN);
            num = confGlobalLimit.value - humanObj.getDailyResetTypeValue(DailyResetTypeKey.dailyWorldBossHour.getType());
            if (num < 0) {
                num = 0;
            }
        } else if (type == InstanceConstants.SEVENTRIALCHAPTER_23) {
            num = ItemManager.inst().getItemNum(humanObj, ItemConstants.goods_勇者试炼钥匙);
        } else {
            int[] arr = GlobalConfVal.getRepCost(type);
            if (arr != null && arr.length > 0) {
                num = humanObj.operation.itemData.getItemNum(arr[0]);
            }
        }

        Define.p_dungeon.Builder dInfo = Define.p_dungeon.newBuilder();
        dInfo.setType(type);
        dInfo.setMaxLevel(maxLv);
        dInfo.setCurLevel(curLv);
        dInfo.setDayTimes(num);

        if (type == InstanceConstants.DARKTRIALCHAPTER_28 || type == InstanceConstants.DARKTRIALCHAPTER_29 || type == InstanceConstants.DARKTRIALCHAPTER_30) {
            int dailyMaxLv = humanObj.getDailyResetTypeValue(DailyResetTypeKey.dailyDarkTrialMaxLv.getType());
            maxLv = curLv;
            curLv = humanObj.getDailyResetTypeValue(DailyResetTypeKey.dailyDarkTrialChapter.getType()) + 1; // 今日打的进度
            curLv = Math.min(curLv, dailyMaxLv);
            dInfo.setMaxLevel(maxLv);
            dInfo.setCurLevel(curLv);
        }
        if (type == InstanceConstants.LEAGUESOLOCHAPTER_6 || type == InstanceConstants.SEVENTRIALCHAPTER_23) {
            dInfo.setMaxLevel(1);
            dInfo.setCurLevel(1);
        }
        if (isUse) {
            dInfo.setCurLevel(level);
        }

        int confMaxLv = GlobalConfVal.getRepTypeMaxLv(type);
        if (dInfo.getMaxLevel() > confMaxLv && confMaxLv != 0) {
            dInfo.setMaxLevel(confMaxLv);
        }

        fillDungeonExtList(humanObj, type, (extList) -> {
            dInfo.addAllExt(extList);
            MsgDungeon.dungeon_update_s2c.Builder msg = MsgDungeon.dungeon_update_s2c.newBuilder();
            msg.setDungeon(dInfo);
            humanObj.sendMsg(msg);
        });
    }

    public void fillDungeonExtList(HumanObject humanObj, int type, GofFunction1<List<Define.p_key_value>> callBack) {
        List<Define.p_key_value> extList = new ArrayList<>();
        if (type == InstanceConstants.SEVENTRIALCHAPTER_23) {
            ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(ActivityControlType.Act_72);
            if (controlData == null) {
                extList.add(HumanManager.inst().to_p_key_value(InstanceConstants.dungeonType_4, -1).build());
                extList.add(HumanManager.inst().to_p_key_value(InstanceConstants.dungeonType_8, 0).build());
                if (callBack != null) {
                    callBack.apply(extList);
                }
            } else {
                ControlChallengeData data = (ControlChallengeData) controlData.getControlData();
                extList.add(HumanManager.inst().to_p_key_value(InstanceConstants.dungeonType_8, data.score).build());

                ConfActivityTerm confActivityTerm = ActivityManager.inst().getActivityTerm(ActivityControlType.Act_72, controlData.getActControlData().getRound());
                ConfRanktype confRanktype = ConfRanktype.get(confActivityTerm.rank_id);
                String rankKey = RankManager.inst().getRedisRankTypeKey(humanObj.getHuman().getServerId(), confRanktype);
                RankManager.inst().getMyRankAndScore(EntityManager.getRedisClient(), rankKey, humanObj.id, handler -> {
                    int rank = -1;
                    if (handler.failed()) {
                        Log.activity.error("勇者试炼计算个人榜单信息错误, e={}", handler.cause(), handler.cause());
                    } else {
                        List<String> resultList = handler.result();
                        rank = Integer.parseInt(resultList.get(0));
                    }
                    extList.add(HumanManager.inst().to_p_key_value(InstanceConstants.dungeonType_4, rank).build());
                    if (callBack != null) {
                        callBack.apply(extList);
                    }
                });
            }
        } else {
            extList.addAll(HumanManager.inst().getDungeonExtList(humanObj, type));
            if (callBack != null) {
                callBack.apply(extList);
            }
        }
    }

    public int getDarkTrialChapterMaxSn(int type, int oldDifficulty) {
        int maxLv = 0;
        int sn = GlobalConfVal.getDarkTrialTypeDiffTopSn(type, oldDifficulty);
        ConfDarkTrialChapter conf = ConfDarkTrialChapter.get(sn);
        if (conf != null) {
            maxLv = conf.level;
        }
        return maxLv;
    }

    public int getDarkTrialChapterTopSn(int type, int oldLevel) {
        int maxSn = 1;
        int sn = GlobalConfVal.getDarkTrialLevelTypeMap(oldLevel, type);
        ConfDarkTrialChapter conf = ConfDarkTrialChapter.get(sn);
        if (conf != null) {
            maxSn = conf.sn;
        }
        return maxSn;
    }

    public boolean isFiveStar(int type, int oldLevel) {
        if (oldLevel <= 0) {
            return false;
        }
        int sn = GlobalConfVal.getRepSn(type, oldLevel);
        ConfDarkTrialChapter conf = ConfDarkTrialChapter.get(sn);
        return (conf != null && conf.star == 5);
    }

    public void _msg_main_chapter_enter_c2s(HumanObject humanObj, int partId) {
        Human2 human = humanObj.getHuman2();
        Human3 human3 = humanObj.getHuman3();
        if (humanObj.enterTypeTime != 0 && (humanObj.enterTypeTime + 3 * Time.SEC) > Port.getTime()) {
            return;
        }
        if (partId > 0 && (partId == human.getRepSn() || partId % 5 == 0)) {// 直接挑战boss关
            sendMsg_main_chapter_enter_s2c(humanObj, partId, 0);
            return;
        }
        if (partId == 0) {
            partId = human.getRepSnClient();
            if (partId == 0) {
                partId = human.getRepSn();
            }
            sendMsg_main_chapter_enter_s2c(humanObj, partId, human3.getUnlimited());
            return;
        }

        if (partId > human.getRepSn()) {
            errorRectifyRep(humanObj);
            return;
        }
        sendMsg_main_chapter_enter_s2c(humanObj, partId, human3.getUnlimited());
    }

    private void errorRectifyRep(HumanObject humanObj) {
        humanObj.isRepLog = true;
        Human2 human = humanObj.getHuman2();
        int partId = human.getRepSnClient();
        if (partId == 0) {
            partId = human.getRepSn();
        }
        sendMsg_main_chapter_enter_s2c(humanObj, partId, humanObj.getHuman3().getUnlimited());
    }

    public void sendMsg_main_chapter_enter_s2c(HumanObject humanObj, int partId, int unlimited) {
        MsgMainChapter.main_chapter_enter_s2c.Builder msg = MsgMainChapter.main_chapter_enter_s2c.newBuilder();
        msg.setPartId(partId);
        msg.setIsUnlimited(unlimited);
        msg.setBattleCheckout(0);
        msg.setRandomSeed(Utils.getTimeSec());
        humanObj.sendMsg(msg);
    }

    public void _msg_main_chapter_kill_reward_c2s(HumanObject humanObj, int partId, long unitId, Define.p_pos pos) {
        try {
            sendMsg_main_chapter_kill_reward_s2c(humanObj, partId, unitId, pos);
            humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_主线, TaskConditionTypeKey.TASK_TYPE_16);
            ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_16, 1, 1);
            humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_每日, TaskConditionTypeKey.TASK_TYPE_16);
            humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_战令, TaskConditionTypeKey.TASK_TYPE_16);
        }catch (Exception e){
            Log.human.error("===_msg_main_chapter_kill_reward_c2s", e);
        }
    }

    private void sendMsg_main_chapter_kill_reward_s2c(HumanObject humanObj, int partId, long unitId, Define.p_pos pos) {
        MsgMainChapter.main_chapter_kill_reward_s2c.Builder msg = MsgMainChapter.main_chapter_kill_reward_s2c.newBuilder();
        msg.setPartId(0);
        msg.setUnitId(Utils.intValue(unitId));
        msg.setPos(pos);

        HumanExtInfo extInfo = humanObj.getHumanExtInfo();
        Map<Integer, List<Long>> mainRepKillIdMap = humanObj.operation.mainRepKillIdMap;

        int chapter = partId / 50 + (partId % 50 == 0 ? 0 : 1);

        List<Long> killMonsterIdList = mainRepKillIdMap.get(chapter);
        boolean isNew = false;
        if (killMonsterIdList == null) {
            // 新章节 只保留一个章节
            mainRepKillIdMap.clear();
            killMonsterIdList = new ArrayList<>();
            isNew = true;
            mainRepKillIdMap.put(chapter, killMonsterIdList);
        }

        Map<Integer, Integer> killMap = new HashMap<>(GlobalConfVal.getKillReward(unitId));
        if (!killMonsterIdList.contains(unitId)) {
            killMap = Utils.mergeMap(killMap, GlobalConfVal.getKillOneReward(unitId));
            killMonsterIdList.add(unitId);
        }
        Map<Integer, Integer> rewardMap = ProduceManager.inst().producePreDrop(killMap);
        msg.addAllRewardList(to_p_rewardList(rewardMap));
        humanObj.sendMsg(msg);
        if (!rewardMap.isEmpty()) {
            ProduceManager.inst().produceAdd(humanObj, rewardMap, MoneyItemLogKey.击杀掉落);
        }
        if (isNew) {
            extInfo.setMainRepMonsterIdListMap(Utils.mapIntListLongToJSON(humanObj.operation.mainRepKillIdMap));
            extInfo.update();
        }
        killMap.clear();
    }

    public void _msg_main_chapter_reward_info_c2s(HumanObject humanObj, int type) {
        PlacingRewardVo placingRewardVo = reCalculatePlacingReward(humanObj, type, true);
        MsgMainChapter.main_chapter_reward_info_s2c.Builder msg = MsgMainChapter.main_chapter_reward_info_s2c.newBuilder();
        msg.setType(type);
        msg.setTime(placingRewardVo.placingTime);
        msg.addAllResList(placingRewardVo.resToBuilder());
        msg.addAllItemList(placingRewardVo.itemToBuilder());
        humanObj.sendMsg(msg);
    }

    public void _msg_main_chapter_reward_info_c2s_type2(HumanObject humanObj) {
        PlacingRewardVo placingRewardVo = reCalculatePlacingReward(humanObj, PlacingRewardType.OFFLINE_REWARD_1, true);
        if (placingRewardVo.rewardItemMap.size() == 0) {
            return;
        }
        MsgMainChapter.main_chapter_reward_info_s2c.Builder msg = MsgMainChapter.main_chapter_reward_info_s2c.newBuilder();
        msg.setType(PlacingRewardType.OFFLINE_REWARD_1);
        msg.setTime(placingRewardVo.placingTime);
        msg.addAllResList(placingRewardVo.resToBuilder());
        msg.addAllItemList(placingRewardVo.itemToBuilder());
        humanObj.sendMsg(msg);
    }

    /**
     * @param humanObj
     * @param type     放置奖励领取
     * @return
     */
    public void _msg_main_chapter_claim_reward_c2s(HumanObject humanObj, int type) {
        PlacingRewardVo placingRewardVo = reCalculatePlacingReward(humanObj, type, true);
        Map<Integer, Integer> rewardMap = new HashMap<>();
        if (!placingRewardVo.rewardResMap.isEmpty()) {
            rewardMap.putAll(placingRewardVo.rewardResMap);
            if(type == PlacingRewardType.OFFLINE_REWARD_1){
                ProduceManager.inst().produceAdd(humanObj, placingRewardVo.rewardResMap, MoneyItemLogKey.离线放置奖励);
            }else {
                ProduceManager.inst().produceAdd(humanObj, placingRewardVo.rewardResMap, MoneyItemLogKey.在线放置奖励);
            }

            placingRewardVo.rewardResMap.clear();
        }
        if (!placingRewardVo.rewardItemMap.isEmpty()) {
            Utils.mergeMap(rewardMap, placingRewardVo.rewardItemMap);
            if (type == PlacingRewardType.OFFLINE_REWARD_1) {
                ProduceManager.inst().produceAdd(humanObj, placingRewardVo.rewardItemMap, MoneyItemLogKey.离线放置奖励);
            }else {
                ProduceManager.inst().produceAdd(humanObj, placingRewardVo.rewardItemMap, MoneyItemLogKey.在线放置奖励);
            }
            placingRewardVo.rewardItemMap.clear();
        }
        placingRewardVo.placingTime = 0;
        savePlacingRewardMap(humanObj);
        MsgMainChapter.main_chapter_claim_reward_s2c.Builder msg = MsgMainChapter.main_chapter_claim_reward_s2c.newBuilder();
        msg.setType(type);
        humanObj.sendMsg(msg);

        if (type != 2) {
            MsgMainChapter.main_chapter_reward_info_s2c.Builder msg2 = MsgMainChapter.main_chapter_reward_info_s2c.newBuilder();
            msg2.setType(type);
            msg2.setTime(placingRewardVo.placingTime);
            humanObj.sendMsg(msg2);
        }

        if (!rewardMap.isEmpty()) {
            InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, InstanceManager.inst().to_p_rewardList(rewardMap));
        }
    }

    /**
     * @param humanObj
     * @param type     放置奖励类型
     * @return
     */
    public PlacingRewardVo reCalculatePlacingReward(HumanObject humanObj, int type, boolean isSave) {
        AbstractCalculator calculator = RewardCalculatorTypeFactory.getTypeData(humanObj, type);
        if (calculator != null) {
            return calculator.reCalculatePlacingReward(humanObj, isSave);
        }
        return new PlacingRewardVo();
    }

    @Listener(EventKey.HUMAN_LOGIN)
    public void onHumanLogin(Param param) {
        HumanObject humanObj = param.get("humanObj");
        long timeLastLogin = param.getLong("timeLoginLast");
        //顶号不处理
        if (humanObj.getHuman().getTimeLogout() < timeLastLogin) {
            return;
        }
        if (humanObj.isModUnlock(80)) {
            initPlacingReward(humanObj, PlacingRewardType.ONLINE_REWARD_2);
            reCalculatePlacingReward(humanObj, PlacingRewardType.OFFLINE_REWARD_2, true);
        }
        initPlacingReward(humanObj, PlacingRewardType.ONLINE_REWARD_1);
        reCalculatePlacingReward(humanObj, PlacingRewardType.OFFLINE_REWARD_1, true);

    }

    @Listener(EventKey.HUMAN_LOGOUT)
    public void onHumanLogout(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj.isModUnlock(80)) {
//			initPlacingReward(humanObj, PlacingRewardType.OFFLINE_REWARD_2);
            reCalculatePlacingReward(humanObj, PlacingRewardType.ONLINE_REWARD_2, true);
        }
//		initPlacingReward(humanObj, PlacingRewardType.OFFLINE_REWARD_1);
        reCalculatePlacingReward(humanObj, PlacingRewardType.ONLINE_REWARD_1, true);
    }

    @Listener(EventKey.HUMAN_RESET_ZERO)
    public void onOpenServerDayChange(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (!humanObj.operation.placingRewardMap.containsKey(PlacingRewardType.ONLINE_REWARD_2) && humanObj.isModUnlock(80)) {
            initPlacingReward(humanObj, PlacingRewardType.ONLINE_REWARD_2);
            savePlacingRewardMap(humanObj);
        }
    }

    public void initPlacingReward(HumanObject humanObj, int type) {
        AbstractCalculator calculator = RewardCalculatorTypeFactory.getTypeData(humanObj, type);
        calculator.initPlacingReward();
    }

    public void initPlacingRewardOffline(HumanObject humanObj) {
        AbstractCalculator calculator = RewardCalculatorTypeFactory.getTypeData(humanObj, PlacingRewardType.OFFLINE_REWARD_1);
        calculator.initPlacingRewardOffline();
        AbstractCalculator calculator2 = RewardCalculatorTypeFactory.getTypeData(humanObj, PlacingRewardType.OFFLINE_REWARD_2);
        calculator2.initPlacingRewardOffline();

    }

    public void savePlacingRewardMap(HumanObject humanObj) {
        String mapStr = PlacingRewardVo.toJsonStrMap(humanObj.operation.placingRewardMap);
        HumanExtInfo humanExt = humanObj.getHumanExtInfo();
        String compressedMapStr = Utils.compressJson(mapStr);
        if (compressedMapStr != null && compressedMapStr.length() < mapStr.length()) {
            mapStr = Utils.createStr("{}{}", PlacingRewardType.NEW_COMPRESSION_PREFIX, compressedMapStr);
        }
        if (mapStr.length() > 2048) {
            Log.game.error("===mapStr.length() > 2048, mapStr={}, humanId={}", mapStr, humanExt.getId());
            return;
        }
        humanExt.setPlacingRewardMap(mapStr);
    }

    public void sendMsg_dungeon_battle_result_s2c(HumanObject humanObj, int code, int type, int dungeonId, int result,
                                                  List<Define.p_reward> dInfoList, List<Define.p_key_value> extList) {
        MsgDungeon.dungeon_battle_result_s2c.Builder msg = MsgDungeon.dungeon_battle_result_s2c.newBuilder();
        msg.setCode(code);
        msg.setType(type);
        msg.setDungeonId(dungeonId);
        msg.setResult(result);
        msg.addAllRewardList(dInfoList);
        msg.addAllExt(extList);
        humanObj.sendMsg(msg);
    }

    public boolean isOpenWorldBoss() {
        long timeNow = Port.getTime();
        int hour = Utils.getHourOfTime(timeNow);
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.world_boss_open_time.SN);
        long timeEnd = Utils.getDayTime(timeNow, hour, confGlobal.value, 0);
        if (timeNow < timeEnd) {
            return true;
        }
        return false;
    }

    /**
     * 穿越深渊之门WorldBoss表
     *
     * <AUTHOR>
     * @Date 2024/5/11
     * @Param
     */
    @Listener(value = EventKey.INSTANCE_PASS, subInt = InstanceConstants.WORLDBOSS_13)
    public void _on_INSTANCE_PASS_WORLDBOSS_13(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        int repType = Utils.getParamValue(param, "repType", InstanceConstants.WORLDBOSS_13);
//		int num = Utils.getParamValue(param, "num", 0);
        int repSn = Utils.getParamValue(param, "repSn", 0);
        long hurt = Utils.getParamValue(param, "hurt", 0L);

        HumanDailyResetInfo infoHour = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyWorldBossHour.getType());
        infoHour.setValue(infoHour.getValue() + 1);
        humanObj.saveDailyResetRecord();
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_回归任务, TaskConditionTypeKey.TASK_TYPE_11, repType, 1);
        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_11, 1, InstanceConstants.WORLDBOSS_13, 1);
        Log.temp.info("=={}, {}", humanObj.getDailyResetTypeValue(DailyResetTypeKey.dailyWorldBoss.getType()),
                humanObj.getDailyResetTypeValue(DailyResetTypeKey.dailyWorldBossHour.getType()));

        // 穿越深渊之门
        int dailyNum = humanObj.getDailyResetTypeValue(DailyResetTypeKey.dailyWorldBoss.getType());
        if (dailyNum > 0) {
            List<Define.p_key_value> extList = new ArrayList<>();
            extList.add(HumanManager.inst().to_p_key_value(ParamKey.repExtType_2, hurt).build());
            WorldBossServiceProxy proxy = WorldBossServiceProxy.newInstance();
            proxy.getWorldInfo(humanObj.id, humanObj.getHuman().getServerId());
            proxy.listenResult(this::_result_getWorldInfo2, "humanObj", humanObj, "extList", extList,
                    "rewardList", new ArrayList<>(), "repType", repType, "repSn", repSn);
            return;
        }
        // 每日首次才有奖励
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.world_boss_daily_reward.SN);
        if (confGlobal.strValue == null) {
            return;
        }

        HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyWorldBoss.getType());
        info.setValue(info.getValue() + 1);
        humanObj.saveDailyResetRecord();

        int[][] arr = Utils.parseIntArray2(confGlobal.strValue);
        ProduceManager.inst().produceAdd(humanObj, arr, MoneyItemLogKey.世界boss每日首次奖励);

        List<Define.p_reward> rewardList = to_p_rewardList(arr);
        List<Define.p_key_value> extList = new ArrayList<>();
        extList.add(HumanManager.inst().to_p_key_value(ParamKey.repExtType_2, hurt).build());
        WorldBossServiceProxy proxy = WorldBossServiceProxy.newInstance();
        proxy.getWorldInfo(humanObj.id, humanObj.getHuman().getServerId());
        proxy.listenResult(this::_result_getWorldInfo2, "humanObj", humanObj, "extList", extList,
                "rewardList", rewardList, "repType", repType, "repSn", repSn);

    }

    private void _result_getWorldInfo2(Param results, Param context) {
        HumanObject humanObj = Utils.getParamValue(context, "humanObj", null);
        List<Define.p_reward> rewardList = Utils.getParamValue(context, "rewardList", new ArrayList<>());
        int myRank = Utils.getParamValue(results, "myRank", 0);
        long myHurt = Utils.getParamValue(results, "myHurt", 0L);
        List<Define.p_key_value> extList = Utils.getParamValue(context, "extList", new ArrayList<>());
        int repSn = Utils.getParamValue(context, "repSn", 0);
        int repType = Utils.getParamValue(context, "repType", 0);

        extList.add(HumanManager.inst().to_p_key_value(ParamKey.repExtType_4, myRank).build());

        sendMsg_dungeon_result_s2c(humanObj, 0, repType, repSn, InstanceConstants.win, rewardList, extList);

        sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_5001, rewardList);

    }

    public void _msg_dungeon_world_boss_info_c2s(HumanObject humanObj) {
        WorldBossServiceProxy proxy = WorldBossServiceProxy.newInstance();
        proxy.getWorldInfo(humanObj.id, humanObj.getHuman().getServerId());
        proxy.listenResult(this::_result_getWorldInfo, "humanObj", humanObj);
    }

    private void _result_getWorldInfo(Param results, Param context) {
        HumanObject humanObj = Utils.getParamValue(context, "humanObj", null);
        long totalHurt = Utils.getParamValue(results, "totalHurt", 0L);
        int myRank = Utils.getParamValue(results, "myRank", 0);
        long myHurt = Utils.getParamValue(results, "myHurt", 0L);
        int buffSn = Utils.getParamValue(results, "buffSn", 0);
        RankInfo topRankInfo = Utils.getParamValue(results, "topRankInfo", null);

        long timeNow = Port.getTime();
        int hour = Utils.getHourOfTime(timeNow);
        long timeOpen = Utils.getDayTime(timeNow, hour, 0, 0);
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.world_boss_open_time.SN);
        long timeEnd = Utils.getDayTime(timeNow, hour, confGlobal.value, 0);
        MsgDungeon.dungeon_world_boss_info_s2c.Builder msg = MsgDungeon.dungeon_world_boss_info_s2c.newBuilder();
        if (timeNow < timeEnd) {
            msg.setIsOpen(1);
            msg.setStartTime((int) (timeOpen / Time.SEC));
            msg.setEndTime((int) (timeEnd / Time.SEC));
        } else {
            timeOpen += Time.HOUR;
            timeEnd += Time.HOUR;
            msg.setIsOpen(0);
            msg.setStartTime((int) (timeOpen / Time.SEC));
            msg.setEndTime((int) (timeEnd / Time.SEC));
        }
        if (topRankInfo != null) {
            String roleName = topRankInfo.rank_info.getName();
            msg.setRoleName(roleName == null ? "" : roleName);
            msg.setFigure(topRankInfo.rank_info.getFigure());
        } else {
            msg.setRoleName("");
        }

        msg.setTotalHurt(totalHurt);
        msg.setTodayBuff(buffSn);
        msg.setMyRank(myRank);
        msg.setMyHurt(myHurt);

        ConfGlobal confGlobalLimit = ConfGlobal.get(ConfGlobalKey.world_boss_challenge_limit.SN);
        int dailyNum = confGlobalLimit.value - humanObj.getDailyResetTypeValue(DailyResetTypeKey.dailyWorldBossHour.getType());
        if (dailyNum < 0) {
            dailyNum = 0;
        }
        msg.setTimes(dailyNum);
        if (humanObj.getDailyResetTypeValue(DailyResetTypeKey.dailyWorldBoss.getType()) == 0) {
            msg.setIsFirst(0);
        } else {
            msg.setIsFirst(1);
        }
        humanObj.sendMsg(msg);

    }

    public void _msg_dungeon_battle_start_c2s(HumanObject humanObj, int type, int level) {
        sendMsg_dungeon_update_s2c(humanObj, type, true, level);
        humanObj.repTypeEnterTimeMap.put(type, Port.getTime());
        humanObj.enterTypeTime = Port.getTime();
        if (type == InstanceConstants.ACTIVITY_CHAPTER_SLIME_120) {
            ActivityManager.inst().on_dungeon_battle_start_c2s(humanObj, type, level);
            return;
        }

        if (type == InstanceConstants.LEAGUESOLOCHAPTER_6) {
            GuildManager.inst()._msg_dungeon_battle_start_c2s(humanObj, type, level);
            return;
        }

        List<Define.p_key_value> extList = getDungeonStartExtList(humanObj, type);
        if (type == InstanceConstants.FATECHAPTER_11) {
            if (FateManager.inst().checkFateDungeonDropFullBag(humanObj, level)) {
                Inform.sendMsg_error(humanObj, 182);
                sendMsg_dungeon_battle_start_s2c(humanObj, 182, type, level, 0, 0, new ArrayList<>(), extList);
                return;
            }
        }

        if (type == InstanceConstants.ARTIFACTGEMCHAPTER_22) {
            if ((ArtifactManager.inst().bagSize(humanObj)) >= ConfGlobal.get(202).value) {
                Inform.sendMsg_error(humanObj, 504);
                return;
            }
        }

        int diff = humanObj.operation.getRepMaxDiffculty(type);
        if (diff + 1 < level) {
            int repSn = GlobalConfVal.getRepSn(type, diff + 1);

            List<Define.p_battle_role> roleList = new ArrayList<>();
            HumanDataVO vo = new HumanDataVO(humanObj);
            roleList.add(vo.p_battle);
            sendMsg_dungeon_battle_start_s2c(humanObj, type, repSn, 0, Utils.getTimeSec(), roleList, extList);
            return;
        }
        int repSn = GlobalConfVal.getRepSn(type, level);
        if (type == InstanceConstants.ARTIFACTGEMCHAPTER_22) {
            ConfArtifactGemChapter conf = ConfArtifactGemChapter.get(repSn);
            if (conf == null) {
                return;
            }
            int num = 0;
            for (int i = 0; i < conf.reward.length; i++) {
                int[] intArr = conf.reward[i];
                for (int m = 0; m < intArr.length; m += 2) {
                    int key = intArr[m];
                    int value = intArr[m + 1];
                    ConfGoods confGoods = ConfGoods.get(key);
                    if (confGoods == null) {
                        continue;
                    }
                    if (confGoods.type == ItemConstants.符石) {
                        num += value;
                    }
                }
            }
            if ((ArtifactManager.inst().bagSize(humanObj) + num) > ConfGlobal.get(202).value) {
                Inform.sendMsg_error(humanObj, 504);
                sendMsg_dungeon_battle_start_s2c(humanObj, 504, type, repSn, 0, 0, new ArrayList<>(), extList);
                return;
            }
        }

        HumanDataVO vo = new HumanDataVO(humanObj);
        List<Define.p_battle_role> roleList = new ArrayList<>();
        if (type == InstanceConstants.LEAGUEGVECHAPTER_7) {
            roleList.add(vo.to_p_battle_role(ParamKey.objType_1, 1010, 1300, 1011, 1300));
        } else {
            roleList.add(vo.p_battle);
        }

        sendMsg_dungeon_battle_start_s2c(humanObj, type, repSn, 0, 0, roleList, extList);
    }

    public List<Define.p_key_value> getDungeonStartExtList(HumanObject humanObj, int type) {
        List<Define.p_key_value> extList = new ArrayList<>();
        if (type == InstanceConstants.FATECHAPTER_11) {
            extList.add(HumanManager.inst().to_p_key_value(ParamKey.repExtType_1, Utils.getDaysBetween(Port.getTime(), Util.getOpenServerTime(humanObj.getHuman().getServerId())) + 1).build());
        } else if (type == InstanceConstants.DARKTRIALCHAPTER_28
                    || type == InstanceConstants.DARKTRIALCHAPTER_29
                    || type == InstanceConstants.DARKTRIALCHAPTER_30) {
            extList.add(HumanManager.inst().to_p_key_value(ParamKey.repExtType_1, Utils.getDaysBetween(Port.getTime(), Util.getOpenServerTime(humanObj.getHuman().getServerId())) + 1).build());
        }
        return extList;
    }

    public void sendMsg_dungeon_battle_start_s2c(HumanObject humanObj, int type, int dungeonId, int battleCheckOut,
                                                 int randomSeed, List<Define.p_battle_role> roles, List<Define.p_key_value> ext) {
        sendMsg_dungeon_battle_start_s2c(humanObj, 0, type, dungeonId, battleCheckOut, randomSeed, roles, ext);
    }

    public void sendMsg_dungeon_battle_start_s2c(HumanObject humanObj, int code, int type, int dungeonId, int battleCheckOut,
                                                 int randomSeed, List<Define.p_battle_role> roles, List<Define.p_key_value> ext) {
        MsgDungeon.dungeon_battle_start_s2c.Builder msg = MsgDungeon.dungeon_battle_start_s2c.newBuilder();
        msg.setCode(code);
        msg.setType(type);
        msg.setDungeonId(dungeonId);
        msg.setBattleCheckout(battleCheckOut);
        msg.setRandomSeed(randomSeed);
        msg.addAllRoles(roles);
        msg.addAllExt(ext);
        humanObj.sendMsg(msg);
    }

    public void _msg_dungeon_battle_result_c2s(HumanObject humanObj, int type, int dungeonId, int result,
                                               int manualOperators, List<Define.p_battle_operator> operatorsList,
                                               List<Define.p_key_value> argsList) {
        humanObj.enterTypeTime = 0;
        if (type == InstanceConstants.LEAGUESOLOCHAPTER_6) {
            GuildManager.inst()._msg_dungeon_battle_result_c2s(humanObj, type, dungeonId, result, manualOperators, operatorsList, argsList);
            return;
        }
        if (type == InstanceConstants.ACTIVITY_CHAPTER_SLIME_120) {
            ActivityManager.inst().on_dungeon_battle_result_c2s(humanObj, type, dungeonId, result, argsList);
            return;
        }
        long hurt = 0;
        if (type == InstanceConstants.WORLDBOSS_13) {
            for (Define.p_key_value dInfo : argsList) {
                if (dInfo.getK() == 2) {
                    hurt = dInfo.getV();
                }
            }
            WorldBossServiceProxy proxy = WorldBossServiceProxy.newInstance();
            proxy.updateBossHurt(humanObj.getHuman().getServerId(), new RankInfo(humanObj, hurt));
        }

        if (type == InstanceConstants.SEVENTRIALCHAPTER_23) {
            for (Define.p_key_value dInfo : argsList) {
                if (dInfo.getK() == 2) {
                    hurt = dInfo.getV();
                }
            }
            if (hurt == 0) {
                return;
            }
        } else if (type == InstanceConstants.DOUBLELADDERCHAPTERCHAPTER_32) {
            DoubleChapterManager.inst().handleDoubleChapterBattleResult(humanObj, dungeonId, result, manualOperators, operatorsList, argsList);
            return;
        } else {
            if (result == InstanceConstants.lose) {
                if (type == InstanceConstants.LEGACYTEAMCHAPTER_8) {
                    TeamManager.inst()._msg_team_exit_c2s(humanObj, null);
                }
                return;
            }
        }
        // 先处理通关多组件key事件
        Event.fireEx(EventKey.INSTANCE_PASS, type, "humanObj", humanObj, "repType", type, "num", 1, "repSn", dungeonId, "hurt", hurt);

        switch (type) {
            case InstanceConstants.WORLDBOSS_13: {
                sendMsg_dungeon_update_s2c(humanObj, type);
                _msg_dungeon_world_boss_info_c2s(humanObj);
            }
            break;
            case InstanceConstants.SEVENTRIALCHAPTER_23: {
                // 23类型的相关处理都通过Event.fireEx
            }
            break;
            default: {
                sendMsg_dungeon_update_s2c(humanObj, type);
            }
            break;
        }
    }

    @Listener(EventKey.HUMAN_RESET_EVERY_HOUR)
    public void _HUMAN_RESET_EVERY_HOUR(Param param) {
        HumanObject humanObj = param.get("humanObj");
        checkWorldBossReset(humanObj);
    }

    @Listener(EventKey.HUMAN_LOGIN_FINISH)
    public void _HUMAN_LOGIN_FINISH(Param param) {
        HumanObject humanObj = param.get("humanObj");
        checkWorldBossReset(humanObj);
    }

    private void checkWorldBossReset(HumanObject humanObj) {
        HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyWorldBossHour.getType());

        long timeNow = Port.getTime();
        boolean isReset = false;
        int hourNow = Utils.getHourOfDay(timeNow);
        if (info.getResetTime() == 0 || !Utils.isSameWeek(timeNow, info.getResetTime()) ||
                hourNow != Utils.getHourOfDay(info.getResetTime())) {
            isReset = true;
        }
        if (!isReset) {
            return;
        }
        info.setValue(0);
        info.setResetTime(timeNow);
        humanObj.saveDailyResetRecord();

    }

    public void sendMsg_dungeon_result_s2c(HumanObject humanObj, int code, int type, int dungeonId, int result,
                                           List<Define.p_reward> dInfoList, List<Define.p_key_value> extList) {
        MsgDungeon.dungeon_result_s2c.Builder msg = MsgDungeon.dungeon_result_s2c.newBuilder();
        msg.setCode(code);
        msg.setType(type);
        msg.setDungeonId(dungeonId);
        msg.setResult(result);
        msg.addAllRewardList(dInfoList);
        msg.addAllExt(extList);
        humanObj.sendMsg(msg);
    }

    public void sendMsg_team_result_s2c(HumanObject humanObj, int code, int result, int rewardType,
                                        List<Define.p_reward> dInfoList, List<Define.p_key_value> extList, int defence) {
        MsgTeam.team_result_s2c.Builder msg = MsgTeam.team_result_s2c.newBuilder();
        msg.setCode(code);
        msg.setResult(result);
        msg.setRewardType(rewardType);
        msg.addAllRewardList(dInfoList);
        msg.addAllExt(extList);
        msg.setDefence(defence);
        humanObj.sendMsg(msg);
    }

    public void sendMsg_goods_show_s2c(HumanObject humanObj, int type, int... rewards) {
        List<Define.p_reward> rewardList = new ArrayList<>();
        for (int i = 0; i < rewards.length - 1; i += 2) {
            int itemSn = rewards[i];
            int itemNum = rewards[i + 1];
            rewardList.add(HumanManager.inst().to_p_reward(itemSn, itemNum));
        }
        sendMsg_goods_show_s2c(humanObj, type, rewardList);
    }

    public void sendMsg_goods_show_s2c(HumanObject humanObj, int type, int[][] rewards) {
        List<Define.p_reward> rewardList = new ArrayList<>();
        for (int[] reward : rewards) {
            rewardList.add(HumanManager.inst().to_p_reward(reward[0], reward[1]));
        }
        sendMsg_goods_show_s2c(humanObj, type, rewardList);
    }

    public void sendMsg_goods_show_s2c(HumanObject humanObj, int type, Map<Integer, Integer> rewardMap) {
        List<Define.p_reward> rewardList = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : rewardMap.entrySet()) {
            rewardList.add(HumanManager.inst().to_p_reward(entry.getKey(), entry.getValue()));
        }
        sendMsg_goods_show_s2c(humanObj, type, rewardList);
    }

    public void sendMsg_goods_show_s2c(HumanObject humanObj, int type, List<Define.p_reward> rewardList) {
        sendMsg_goods_show_s2c(humanObj, type, rewardList, new ArrayList<>(0));
    }

    public void sendMsg_goods_show_s2c(HumanObject humanObj, int type, List<Define.p_reward> rewardList, List<Define.p_key_value> extList) {
        MsgGoods.goods_show_s2c.Builder msg = MsgGoods.goods_show_s2c.newBuilder();
        msg.setType(type);
        msg.addAllExtList(extList);
        for (Define.p_reward reward : rewardList) {
            Define.p_reward.Builder rewardBuilder = reward.toBuilder();
            if (reward.getGtid() == TokenItemType.Money999) {
                long convertedNum = money999ToOrgional(humanObj, reward.getGtid(), (int) reward.getNum());
                rewardBuilder.setNum(convertedNum);
            }
            msg.addRewardList(rewardBuilder.build());
        }
        humanObj.sendMsg(msg);
    }

    private int money999ToOrgional(HumanObject humanObj, int type, int num) {
        return (int) MoneyManager.inst().getRegionalMoney999(humanObj.getRegional(), num);
    }

    public void _msg_dungeon_wing_quick_pass_c2s(HumanObject humanObj, int type) {
        if (type != InstanceConstants.DARKTRIALCHAPTER_28
                && type != InstanceConstants.DARKTRIALCHAPTER_29
                && type != InstanceConstants.DARKTRIALCHAPTER_30) {
            return;
        }
        int difficulty = humanObj.operation.getRepMaxDiffculty(type);
        if (difficulty <= 0) {
            sendMsg_dungeon_update_s2c(humanObj, type);
            return;
        }
        int sn = getDarkTrialChapterTopSn(type, difficulty);
        ConfDarkTrialChapter confTemp = ConfDarkTrialChapter.get(sn);
        if (confTemp == null) {
            sendMsg_dungeon_update_s2c(humanObj, type);
            return;
        }
        HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyDarkTrialChapter.getType());
        if (info.getValue() >= confTemp.level) {
            sendMsg_dungeon_update_s2c(humanObj, type);
            return;
        }
        Map<Integer, Integer> itemMap = new HashMap<>();
        for (int i = sn; i > 0; i--) {
            ConfDarkTrialChapter conf = ConfDarkTrialChapter.get(i);
            if (conf == null) {
                continue;
            }
            if (info.getValue() >= conf.level) {
                continue;
            }
            if (conf.part_type != type) {
                break;
            }
            itemMap = Utils.intArrToIntMap(itemMap, conf.daily_reward);
        }
        if (info.getValue() < difficulty) {
            info.setValue(difficulty);
            humanObj.saveDailyResetRecord();
        }
        if (itemMap.isEmpty()) {
            sendMsg_dungeon_update_s2c(humanObj, type);
            return;
        }
        // 特权加成
        int privilegeValue = PrivilegeManager.inst().getPrivilegeEffectValue(humanObj, PrivilegeType.DARKTRAIL_REWARD);
        if (privilegeValue > 0) {
            for (Map.Entry<Integer, Integer> entry : itemMap.entrySet()) {
                int key = entry.getKey();
                int num = entry.getValue();
                num = (int) (num * (1 + privilegeValue / 100.0));
                itemMap.put(key, num);
            }
        }

        List<Define.p_reward> rewardList = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : itemMap.entrySet()) {
            Define.p_reward.Builder builder = Define.p_reward.newBuilder();
            builder.setGtid(entry.getKey());
            builder.setNum(entry.getValue());
            rewardList.add(builder.build());
        }

        ProduceManager.inst().produceAdd(humanObj, itemMap, MoneyItemLogKey.暗黑试炼);

        sendMsg_dungeon_update_s2c(humanObj, type);
        sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_5015, rewardList);
    }

    public void _msg_dungeon_battle_more_start_c2s(HumanObject humanObj, int type, int level) {
        humanObj.enterTypeTime = Port.getTime();
        if (type == InstanceConstants.WORLDBOSS_13) {
            WorldBossServiceProxy proxy = WorldBossServiceProxy.newInstance();
            proxy.getBattleRank(humanObj.getHuman().getZone(), humanObj.getHuman().getServerId());
            proxy.listenResult(this::_result_getBattleRank, "humanObj", humanObj, "type", type, "level", level);
        }else if(type == InstanceConstants.DOUBLELADDERCHAPTERCHAPTER_32){
            DoubleChapterManager.inst().handleBattleStart(humanObj, level);
        }
        humanObj.repTypeEnterTimeMap.put(type, Port.getTime());
        humanObj.combatType = type;
    }

    private void _result_getBattleRank(Param results, Param context) {
        HumanObject humanObj = Utils.getParamValue(context, "humanObj", null);
        List<Define.p_battle_rank> resultList = Utils.getParamValue(results, "resultList", new ArrayList<>());
        int type = Utils.getParamValue(context, "type", 0);
        int level = Utils.getParamValue(context, "level", 0);


        int repSn = GlobalConfVal.getRepSn(type, level);
        HumanDataVO vo = new HumanDataVO(humanObj, ParamKey.objType_2, 1);
        List<Define.p_battle_role> roleList = new ArrayList<>();
        roleList.add(vo.to_p_battle_role(ParamKey.objType_1, 1010, 1300, 1011, 1300));
        int num = ConfGlobal.get(ConfGlobalKey.world_boss_player_num.SN).value;

        String lvRedisKey = RankManager.inst().getRedisRankTypeKey(RankParamKey.rankTypeLevel_1001, humanObj.getHuman().getServerId());
        RedisTools.getRankListByIndex(EntityManager.redisClient, lvRedisKey, 0, num - 1, false, ret -> {
            if (!ret.succeeded()) {
                Log.temp.error("===获取等级排行榜前{}名失败，{}", lvRedisKey, num, ret.cause());
                return;
            }
            List<Long> humanIdList = new ArrayList<>();
            for (Object idStr : ret.result()) {
                humanIdList.add(Utils.longValue(idStr));
            }
            HumanData.getList(humanIdList, HumanManager.inst().humanClasses, res -> {
                if (!res.succeeded()) {
                    Log.human.error("MemberCallback getMemberAsync error", res.cause());
                    return;
                }
                int pos = 2;
                List<HumanData> humanDataList = res.result();
                for (HumanData humanData : humanDataList) {
                    TeamMember member = new TeamMember(humanData);
                    List<Define.p_key_value> extList = new ArrayList<>();
                    extList.add(HumanManager.inst().to_p_key_value(ParamKey.extPosType_1, pos).build());
                    roleList.add(member.to_p_battle_role(extList, ParamKey.objType_1, 1010, 1300, 1011, 1300));
                    ++pos;
                    if (roleList.size() >= num) {
                        break;
                    }
                }
                sendMsg_dungeon_battle_more_start_s2c(humanObj, 0, type, repSn, 0,
                        Utils.getTimeSec(), roleList, new ArrayList<>(), resultList);
            });
        });
    }

    public void sendMsg_dungeon_battle_more_start_s2c(HumanObject humanObj, int code, int type, int dungeonId, int battleCheckOut,
                                                      int randomSeed, List<Define.p_battle_role> roles, List<Define.p_key_value> ext, List<Define.p_battle_rank> rankList) {
        MsgDungeon.dungeon_battle_more_start_s2c.Builder msg = MsgDungeon.dungeon_battle_more_start_s2c.newBuilder();
        msg.setCode(code);
        msg.setType(type);
        msg.setDungeonId(dungeonId);
        msg.setBattleCheckout(battleCheckOut);
        msg.setRandomSeed(randomSeed);
        msg.addAllRoles(roles);
        msg.addAllExt(ext);
        msg.addAllRankList(rankList);
        humanObj.sendMsg(msg);
        Log.temp.info("===humanId={}. roles.={}", humanObj.id, roles.size());
    }


    public void _msg_dungeon_mount_battle_result_c2s(HumanObject humanObj, int dungeonId, int result,
                                                     int manualOperators, List<Define.p_battle_operator> operatorsList,
                                                     List<Integer> buffsList, List<Define.p_key_value> argsList) {
        humanObj.enterTypeTime = 0;
        long hurt = 0;
        for (Define.p_key_value dInfo : argsList) {
            if (dInfo.getK() == 3) {
                hurt = dInfo.getV();
            }
        }
        ConfMountChapter conf = ConfMountChapter.get(dungeonId);
        if (conf == null) {
            Log.temp.error("===ConfMountChapter配表错误， sn={}", dungeonId);
            return;
        }
        double bossHp = 0;
        if (conf.part_type == 1) {
            long sn = 1400007;
            if (conf.monster_refresh1 != null) {
                sn = conf.monster_refresh1[0][0];
            }
            ConfUnit confUnit = ConfUnit.get(sn);
            if (confUnit == null) {
                return;
            }
            bossHp = hurt / confUnit.hp * 100;
        }
        int type = InstanceConstants.MOUNTCHAPTER_9;
        if (result == InstanceConstants.lose) {
            if (type == InstanceConstants.LEGACYTEAMCHAPTER_8) {
                TeamManager.inst()._msg_team_exit_c2s(humanObj, null);
            }
            return;
        }
        // 先处理通关多组件key事件
        Event.fireEx(EventKey.INSTANCE_PASS, type, "humanObj", humanObj, "repType", type, "num", 1, "repSn", dungeonId, "bossHp", bossHp, "hurt", hurt);

        sendMsg_dungeon_update_s2c(humanObj, type);
    }

    /**
     * 武魂副本每日奖励领取
     */
    public void _msg_dungeon_fate_daily_reward_c2s(HumanObject humanObj) {
        int value = humanObj.getDailyResetTypeValue(DailyResetTypeKey.dailyFateGungeonReward.getType());
        if (value == RewardStateKey.已领取.getType()) {
            // 已经领取
            return;
        }
        int level = humanObj.operation.getRepMaxDiffculty(InstanceConstants.FATECHAPTER_11);
        ConfFateChapter conf = ConfFateChapter.getBy(ConfFateChapter.K.level, level);
        if (conf == null) {
            Log.game.error("ConfFateChapter根据等级找不到配表，level={}", level);
            return;
        }
        HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyFateGungeonReward.getType());
        info.setValue(RewardStateKey.已领取.getType());
        humanObj.saveDailyResetRecord();
        int[][] dropNums = new int[][]{
                conf.daily_reward
        };
        Map<Integer, Integer> rewardMap = ProduceManager.inst().produceAddDrop(humanObj, dropNums, MoneyItemLogKey.武魂日常奖励);
        sendMsg_goods_show_s2c(humanObj, 0, to_p_rewardList(rewardMap));
        sendMsg_dungeon_update_s2c(humanObj, InstanceConstants.FATECHAPTER_11);
    }

    public void _msg_dungeon_wing_reset_c2s(HumanObject humanObj, int type) {
        int resetNum = PrivilegeManager.inst().getPrivilegeEffectValue(humanObj, PrivilegeType.DARKTRAIL_RESET);
        if (resetNum <= 0) {
            return;
        }
        HumanDailyResetInfo infoReset = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyDarkTrialResetNum.getType());
        if (infoReset.getValue() >= resetNum) {
            return;
        }

        HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyDarkTrialChapter.getType());
        if (info.getValue() == 0) {
            return;
        }
        // 重置
        info.setValue(0);
        info.setResetTime(Port.getTime());
        // 加次数
        infoReset.setValue(infoReset.getValue() + 1);
        // 保存
        humanObj.saveDailyResetRecord();

        // 通知
        sendMsg_dungeon_update_s2c(humanObj, type);

    }
}
