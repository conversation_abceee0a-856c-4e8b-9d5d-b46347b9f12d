package org.gof.demo.worldsrv.mount;

import org.gof.core.support.ManagerBase;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.observer.Listener;
import org.gof.demo.battlesrv.manager.CombatChangeLog;
import org.gof.demo.battlesrv.manager.PropManager;
import org.gof.demo.battlesrv.msgHandler.SkillManager;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.worldsrv.activity.ActivityControlType;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.carPark.CarParkManager;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.charm.CharmManager;
import org.gof.demo.worldsrv.common.GameServiceProxy;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.human.EModule;
import org.gof.demo.worldsrv.human.FuncOpenType;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.human.PlanVo;
import org.gof.demo.worldsrv.item.ItemManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgMount;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.enumKey.NewsConditionTypeKey;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

public class MountManager  extends ManagerBase {
    /**
     * 获取实例
     *
     * @return
     */
    public static MountManager inst() {
        return inst(MountManager.class);
    }

    /**
     * 坐骑信息
     */
    public void handleMountInfoC2S(HumanObject humanObj) {
        Mount mount = humanObj.operation.mount;
        if(mount == null){
            humanObj.sendMsg(MsgMount.mount_info_s2c.newBuilder().setLevel(0).setUse(0).setSkillUse(0).setExp(0));
            return;
        }
        MsgMount.mount_info_s2c.Builder msg = MsgMount.mount_info_s2c.newBuilder();
        msg.setLevel(mount.getLevel());
        msg.setExp(mount.getExp());
        msg.setUse(humanObj.getHuman2().getMountUse());
        Map<Integer,Integer> skinLvMap = Utils.jsonToMapIntInt(mount.getSkinLvMap());
        for (Map.Entry<Integer, Integer> entry : skinLvMap.entrySet()) {
            Define.p_key_value.Builder dP = Define.p_key_value.newBuilder();
            dP.setK(entry.getKey());
            dP.setV(entry.getValue());
            msg.addSkinList(dP);
        }
        msg.setSkillUse(getMountSn(humanObj.getHuman2().getMountSkillSnLv()));
        Map<Integer,Integer> talentLvMap = Utils.jsonToMapIntInt(mount.getTalentLvMap());
        for (Map.Entry<Integer, Integer> entry : talentLvMap.entrySet()) {
            Define.p_key_value.Builder dP = Define.p_key_value.newBuilder();
            dP.setK(entry.getKey());
            dP.setV(entry.getValue());
            msg.addTalentList(dP);
        }
        humanObj.sendMsg(msg);
        //打印坐骑信息

    }

    /**
     * 坐骑升级
     * @param type 类型
     */
    public void handleMountLevupC2S(HumanObject humanObj, int type) {
        Mount mount = humanObj.operation.mount;
        if(mount == null){
            return;
        }

        ConfMountLevel confMountLevel = ConfMountLevel.get(mount.getLevel()+1);
        if(confMountLevel == null){
            Log.game.error("坐骑等级配置错误，等级={}", mount.getLevel());
            return;
        }
        int exp = 0;

        if(type == 0){
            //消耗一个道具升级
            ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj, confMountLevel.expend_goods[0], confMountLevel.expend_goods[1], MoneyItemLogKey.坐骑升级);
            if(!result.success){
                return;
            }
            exp = 1;
        }else if(type == 1){
            //经验升到下一级
            int itemNum = ItemManager.inst().getItemNum(humanObj, confMountLevel.expend_goods[0]);
            if(itemNum == 0){
                return;
            }
            exp = confMountLevel.expend_exp - mount.getExp();
            if(itemNum < exp){
                exp = itemNum;
            }
            ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj, confMountLevel.expend_goods[0], exp, MoneyItemLogKey.坐骑升级);
            if(!result.success){
                return;
            }
        }

        int mountExp = mount.getExp() + exp;
        mount.setExp(mountExp);
        if (mountExp >= confMountLevel.expend_exp){
            mount.setLevel(confMountLevel.sn);
            if(confMountLevel.unlock != 0){
                Map<Integer,Integer> skinLvMap = Utils.jsonToMapIntInt(mount.getSkinLvMap());
                if(skinLvMap.get(confMountLevel.unlock) == null){
                    skinLvMap.put(confMountLevel.unlock,0);
                    mount.setSkinLvMap(Utils.mapIntIntToJSON(skinLvMap));
                    humanObj.getHuman2().setMountUse(confMountLevel.unlock);
                    CarParkManager.inst().addMountVo(humanObj,confMountLevel.unlock);
                    HumanManager.inst().checkCrossSyns(humanObj, HumanBrief.K.roleFigure, HumanBrief.K.battleRole);
                    // 重新计算美观值
                    CharmManager.reCalcCharmValue(humanObj, true);
                }
            }
            updatePorpCalcPower(humanObj);
            handleMountInfoC2S(humanObj);

            ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_26,0);
        }
        MsgMount.mount_levup_s2c.Builder msg = MsgMount.mount_levup_s2c.newBuilder();
        msg.setExp(mountExp);
        humanObj.sendMsg(msg);

        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_6,exp,confMountLevel.expend_goods[0],exp);
    }

    /**
     * 坐骑使用
     * @param use 使用
     */
    public void handleMountUseC2S(HumanObject humanObj, int use) {
        Mount mount = humanObj.operation.mount;
        if(mount == null){
            return;
        }
        Human2 human = humanObj.getHuman2();
        if(human.getMountUse() == use){
            humanObj.sendMsg(MsgMount.mount_use_s2c.newBuilder().setUse(use));
            return;
        }
        if(use == 0){
            human.setMountUse(use);
            humanObj.sendMsg(MsgMount.mount_use_s2c.newBuilder().setUse(use));
            HumanManager.inst().checkCrossSyns(humanObj, HumanBrief.K.roleFigure, HumanBrief.K.battleRole);
            return;
        }
        Map<Integer,Integer> skinLvMap = Utils.jsonToMapIntInt(mount.getSkinLvMap());
        if(skinLvMap.get(use) == null){
            return;
        }
        human.setMountUse(use);
        humanObj.sendMsg(MsgMount.mount_use_s2c.newBuilder().setUse(use));
        HumanManager.inst().checkCrossSyns(humanObj, HumanBrief.K.roleFigure, HumanBrief.K.battleRole);
    }

    /**
     * 坐骑皮肤升级
     * @param mountId 坐骑ID
     */
    public void handleMountUpSkinC2S(HumanObject humanObj, int mountId) {
        Mount mount = humanObj.operation.mount;
        if(mount == null){
            return;
        }
        Map<Integer,Integer> skinLvMap = Utils.jsonToMapIntInt(mount.getSkinLvMap());
        int lv = skinLvMap.getOrDefault(mountId,0);
        ConfMountSkin_0 confMountSkin_0 = ConfMountSkin_0.get(mountId, lv);
        if(confMountSkin_0 == null){
            return;
        }
        ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj,confMountSkin_0.expend[0],confMountSkin_0.expend[1],MoneyItemLogKey.坐骑皮肤升级);
        if(!result.success){
            return;
        }

        lv++;
        skinLvMap.put(mountId,lv);
        mount.setSkinLvMap(Utils.mapIntIntToJSON(skinLvMap));
        if(lv == 1){
            CarParkManager.inst().addMountVo(humanObj,mountId);
            humanObj.getHuman2().setMountUse(mountId);
            HumanManager.inst().checkCrossSyns(humanObj, HumanBrief.K.roleFigure, HumanBrief.K.battleRole);
            humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_成就任务, TaskConditionTypeKey.TASK_TYPE_120);
            Event.fire(EventKey.UPDATE_NEWS, "humanObj", humanObj, "newsCondition", NewsConditionTypeKey.MountUnlock, "value", mountId);
        }

        MsgMount.mount_up_skin_s2c.Builder msg = MsgMount.mount_up_skin_s2c.newBuilder();
        msg.setMountId(mountId);
        msg.setSkinLev(lv);
        humanObj.sendMsg(msg);
        handleMountInfoC2S(humanObj);

        if(getMountSn(humanObj.getHuman2().getMountSkillSnLv()) == mountId){
            humanObj.getHuman2().setMountSkillSnLv(getMountSnLv(mountId,lv));
            List<Define.p_passive_skill> update = getPassSkill(humanObj.getHuman2().getMountSkillSnLv(),mount.getLevel());
            SkillManager.inst().sendMsg_skill_passive_update_s2c(humanObj, update, null);
        }
        updatePorpCalcPower(humanObj);
        // 重新计算美观值
        CharmManager.reCalcCharmValue(humanObj, true);
    }

    public int getMountSnLv(int mountSn, int lv){
        return mountSn*100+lv;
    }

    private int getMountSn(int mountSnLv){
        return mountSnLv/100;
    }

    private int getMountLv(int mountSnLv){
        return mountSnLv%100;
    }

    public void updatePorpCalcPower(HumanObject humanObj){
        Mount mount = humanObj.operation.mount;
        if(mount == null){
            return;
        }
        PropCalc propCalc = new PropCalc();
        int power = 0;
        //坐骑属性
        ConfMountLevel confMountLevel = ConfMountLevel.get(mount.getLevel());
        if(confMountLevel == null || confMountLevel.attr == null || confMountLevel.attr.length < 1){
            return;
        }
        propCalc.plus(confMountLevel.attr);
        power += confMountLevel.power;
        Map<Integer,Integer> telentMap = Utils.jsonToMapIntInt(mount.getTalentLvMap());
        for (Map.Entry<Integer, Integer> entry : telentMap.entrySet()) {
            ConfMountAbility_0 confMountAbility_0 = ConfMountAbility_0.get(entry.getKey(),entry.getValue());
            if(confMountAbility_0 == null||confMountAbility_0.value_plus == null || confMountAbility_0.value_plus.length < 1){
                continue;
            }
            propCalc.plus(confMountAbility_0.value_plus);
            power += confMountAbility_0.power;
        }

        Map<Integer,Integer> skinMap = Utils.jsonToMapIntInt(mount.getSkinLvMap());
        for (Map.Entry<Integer, Integer> entry : skinMap.entrySet()) {
            ConfMountSkin_0 confMountSkin_0 = ConfMountSkin_0.get(entry.getKey(),entry.getValue());
            if(confMountSkin_0 == null||confMountSkin_0.attr == null || confMountSkin_0.attr.length < 2){
                continue;
            }
            for(int i = 0; i < confMountSkin_0.attr.length; i+=2){
                propCalc.plus(confMountSkin_0.attr[i], BigDecimal.valueOf(confMountSkin_0.attr[i+1]));
            }
            power += confMountSkin_0.power;
        }

        for(int i = 0; i < confMountLevel.base_skill.length; i+=2){
            ConfSkill confSkill = ConfSkill.get(confMountLevel.base_skill[i]);
            if(confSkill == null) {
                continue;
            }
            if(confSkill.type == 1){
                continue;
            }
            ConfSkillLevel_0 confSkillLevel_0 = ConfSkillLevel_0.get(confMountLevel.base_skill[i], confMountLevel.base_skill[i+1]);
            if(confSkillLevel_0 == null){
                continue;
            }
            propCalc.plus(confSkillLevel_0.ownEffect);
        }
        int snLv = humanObj.getHuman2().getMountSkillSnLv();
        ConfMountSkin_0 confMountSkin_0 = ConfMountSkin_0.get(getMountSn(snLv), getMountLv(snLv));
        if(confMountSkin_0 != null){
            if(confMountSkin_0.skin_skill != null){
                for (int i = 0; i < confMountSkin_0.skin_skill.length; ++i) {
                    ConfSkillLevel_0 confSkillLevel_0 = ConfSkillLevel_0.get(confMountSkin_0.skin_skill[i][0], confMountSkin_0.skin_skill[i][1]);
                    if(confSkillLevel_0 == null){
                        continue;
                    }
                    if(confSkillLevel_0.attrType == null || confSkillLevel_0.attrType[0] != 1){
                        propCalc.plus(confSkillLevel_0.ownEffect);
                    }
                }
            }
        }
        humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.mount, propCalc.toJSONStr());
        HumanManager.inst().updatePowerPar(humanObj, EModule.MOUNT, power);
        PropManager.inst().propCalc(humanObj, CombatChangeLog.坐骑);
    }

    /**
     * 坐骑使用技能
     * @param skillUse 技能使用
     */
    public void handleMountUseSkillC2S(HumanObject humanObj, int skillUse) {
        changeMountUseSkill(humanObj, skillUse);
    }

    public boolean changeMountUseSkill(HumanObject humanObj, int skillUse){
        Mount mount = humanObj.operation.mount;
        if(mount == null){
            return false;
        }
        Map<Integer,Integer> skinLvMap = Utils.jsonToMapIntInt(mount.getSkinLvMap());
        if(skinLvMap.get(skillUse) == null&&skillUse != 0){
            return false;
        }
        List<Define.p_passive_skill> delete = getPassSkill(humanObj.getHuman2().getMountSkillSnLv(),humanObj.operation.mount.getLevel());
        humanObj.getHuman2().setMountSkillSnLv(getMountSnLv(skillUse,skinLvMap.getOrDefault(skillUse,0)));
        List<Define.p_passive_skill> update = getPassSkill(humanObj.getHuman2().getMountSkillSnLv(),humanObj.operation.mount.getLevel());
        List<Define.p_passive_skill> deleteList = new ArrayList<>();
        List<Define.p_passive_skill> updateList = new ArrayList<>();
        if(delete != null){
            deleteList.addAll(delete);
        }
        if(update != null){
            updateList.addAll(update);
        }
        SkillManager.inst().sendMsg_skill_passive_update_s2c(humanObj, updateList, deleteList);
        humanObj.sendMsg(MsgMount.mount_use_skill_s2c.newBuilder().setSkillUse(skillUse));
        HumanManager.inst().setPlanTab(humanObj, PlanVo.TAB_MOUNT,skillUse);

        updatePorpCalcPower(humanObj);
        return true;
    }

    public List<Define.p_passive_skill> getPassSkill(int snLv, int mountLv){
        List<Define.p_passive_skill> list = new ArrayList<>();
        if(mountLv > 0){
            ConfMountLevel confMountLevel = ConfMountLevel.get(mountLv);
            if(confMountLevel == null || confMountLevel.attr == null || confMountLevel.attr.length < 1){
                return list;
            }
            for(int i = 0; i < confMountLevel.base_skill.length; i+=2){
                ConfSkill confSkill = ConfSkill.get(confMountLevel.base_skill[i]);
                if(confSkill == null) {
                    continue;
                }
                if(confSkill.type == 1){
                    continue;
                }
                Define.p_passive_skill.Builder passive_skill = Define.p_passive_skill.newBuilder();
                passive_skill.setSkillId(confMountLevel.base_skill[i]);
                passive_skill.setSkillLv(confMountLevel.base_skill[i+1]);
                list.add(passive_skill.build());
            }
        }

        ConfMountSkin_0 confMountSkin_0 = ConfMountSkin_0.get(getMountSn(snLv), getMountLv(snLv));
        if(confMountSkin_0 == null || confMountSkin_0.skin_skill == null){
            return list;
        }
        for (int i = 0; i < confMountSkin_0.skin_skill.length; ++i) {
            Define.p_passive_skill.Builder passive_skill = Define.p_passive_skill.newBuilder();
            passive_skill.setSkillId(confMountSkin_0.skin_skill[i][0]);
            passive_skill.setSkillLv(confMountSkin_0.skin_skill[i][1]);
            list.add(passive_skill.build());
        }


        return list;
    }

    /**
     * 坐骑天赋升级
     * @param type 类型
     */
    public void handleMountTalentUpC2S(HumanObject humanObj, int type) {
        Mount mount = humanObj.operation.mount;
        if(mount == null){
            return;
        }
        MsgMount.mount_talent_up_s2c.Builder msg = MsgMount.mount_talent_up_s2c.newBuilder();
        Map<Integer,Integer> talentLvMap = Utils.jsonToMapIntInt(mount.getTalentLvMap());
        if(type == 0){
            //消耗一个道具升级
            mountTalentUp(humanObj,talentLvMap,msg);
        }else {
            int loopNum = 1000;
            //消耗所有道具升级
            while (mountTalentUp(humanObj, talentLvMap,msg)) {
                loopNum--;
                if(loopNum <= 0){
                    break;
                }
            }
        }

        mount.setTalentLvMap(Utils.mapIntIntToJSON(talentLvMap));
        updatePorpCalcPower(humanObj);
        humanObj.sendMsg(msg);
    }

    /**
     * 坐骑天赋升级
     * @param humanObj
     * @param talentLvMap
     * @return 一件升级是否继续
     */
    private boolean mountTalentUp(HumanObject humanObj, Map<Integer,Integer> talentLvMap, MsgMount.mount_talent_up_s2c.Builder msg){
        int lv = 0;

        List<Integer> notMaxLvList = Arrays.stream(ConfGlobal.get(301).intArray).boxed().collect(Collectors.toList());

        for (Map.Entry<Integer, Integer> entry : talentLvMap.entrySet()) {
            lv += entry.getValue();
            if(ConfMountAbility_0.get(entry.getKey(),entry.getValue()+1) == null){
                notMaxLvList.remove(entry.getKey());
            }
        }
        if(notMaxLvList.size() == 0){
            return false;
        }
        ConfMountAbilityCost confMountAbilityCost = ConfMountAbilityCost.get(lv);
        if(confMountAbilityCost == null || confMountAbilityCost.cost == null || confMountAbilityCost.cost.length < 2){
            return false;
        }
        ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj,confMountAbilityCost.cost[0],confMountAbilityCost.cost[1],MoneyItemLogKey.坐骑天赋升级);
        if(result.success == false){
            return false;
        }
        boolean isSucces = Utils.random(10000) < confMountAbilityCost.success_rate;
        if(isSucces){
            int id = notMaxLvList.get(Utils.random(notMaxLvList.size()));
            talentLvMap.put(id,talentLvMap.getOrDefault(id,0)+1);
            msg.setIsSucc(0);
            msg.setTalentId(id);
            msg.setTalentLev(talentLvMap.get(id));
        }else {
            msg.setIsSucc(1);
            msg.setTalentId(1);
            msg.setTalentLev(talentLvMap.get(1));
        }
        return !isSucces;
    }

    /**
     * 坐骑功能解锁
     */
    @Listener(EventKey.FUNCTION_OPEN)
    public void unlock(Param params) {
        HumanObject humanObj = params.get("humanObj");
        List<Integer> snList = Utils.getParamValue(params, "openList", new ArrayList<>());
        if (!snList.contains(FuncOpenType.FUNC_MOUNT)) {
            return;
        }
        if (humanObj.operation.mount != null) {
            return;
        }
//        if (!humanObj.isModUnlock(FuncOpenType.FUNC_MOUNT)) {
//            return;
//        }
        initMount(humanObj);
    }

    @Listener(EventKey.FUNCTION_OPEN_LOGIN)
    public void _listener_FUNCTION_OPEN_LOGIN(Param params) {
        HumanObject humanObj = params.get("humanObj");
        int sn = Utils.getParamValue(params, "sn", 0);
        if (sn != FuncOpenType.FUNC_MOUNT) {
            return;
        }
        if (humanObj.operation.mount != null) {
            return;
        }
        if (!humanObj.isModUnlock(FuncOpenType.FUNC_MOUNT)) {
            return;
        }
        initMount(humanObj);
    }

    private void initMount(HumanObject humanObj) {
        Mount mount = new Mount();
        mount.setId(humanObj.id);
        mount.setLevel(1);
        mount.setExp(0);
        ConfMountLevel confMountLevel = ConfMountLevel.get(1);
        if (confMountLevel.unlock != 0) {
            Map<Integer,Integer> skinLvMap = new HashMap<>();
            skinLvMap.put(confMountLevel.unlock,0);
            mount.setSkinLvMap(Utils.mapIntIntToJSON(skinLvMap));
            humanObj.getHuman2().setMountUse(confMountLevel.unlock);
            CarParkManager.inst().addMountVo(humanObj,confMountLevel.unlock);
        }
        mount.persist();
        humanObj.operation.mount = mount;
        updatePorpCalcPower(humanObj);
        handleMountInfoC2S(humanObj);
        HumanManager.inst().checkCrossSyns(humanObj, HumanBrief.K.roleFigure, HumanBrief.K.battleRole);
    }
}
