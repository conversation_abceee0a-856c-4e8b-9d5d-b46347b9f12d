package org.gof.demo.worldsrv.bridgeEntity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName = "BlackMarket", tableName = "black_market")
public enum EntityBlackMarket {
    @Column(type = int.class, comment = "活动sn", index = true)
    activitySn,
    @Column(type = int.class, comment = "跨服分组", index = true)
    groupId,
    @Column(type = int.class, comment = "黑市表sn")
    sn,
    @Column(type = int.class, comment = "黑市表出售道具下标")
    goodsIndex,
    @Column(type = long.class, comment = "下次刷新时间")
    nextUpdateTime,
    @Column(type = int.class, comment = "限购已购买次数")
    buyNum,
    @Column(type = long.class, comment = "预购开奖时间")
    nextOpenPreTime,
    @Column(type = boolean.class, comment = "预购是否开奖")
    openPre,
    @Column(type = byte[].class, comment = "预购参与玩家列表", length = 65535)
    joinHumanList,
}
