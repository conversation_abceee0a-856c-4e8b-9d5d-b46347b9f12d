package org.gof.demo.worldsrv.home;

import com.alibaba.fastjson.JSONObject;
import io.vertx.core.AsyncResult;
import io.vertx.core.Future;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.*;
import org.gof.core.support.observer.Listener;
import org.gof.demo.battlesrv.manager.CombatChangeLog;
import org.gof.demo.battlesrv.manager.PropManager;
import org.gof.demo.battlesrv.manager.UnitManager;
import org.gof.demo.battlesrv.msgHandler.SkillManager;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.battlesrv.support.PropKey;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.arena.ArenaManager;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.character.HumanObjectServiceProxy;
import org.gof.demo.worldsrv.common.GameServiceProxy;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.friend.FriendManager;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.guild.HumanBriefVO;
import org.gof.demo.worldsrv.human.*;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.item.ItemConstants;
import org.gof.demo.worldsrv.item.ItemManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgHome;
import org.gof.demo.worldsrv.msg.MsgScience;
import org.gof.demo.worldsrv.pocketLine.PocketLineEventSubKey;
import org.gof.demo.worldsrv.privilege.PrivilegeManager;
import org.gof.demo.worldsrv.privilege.PrivilegeType;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.Util;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.enumKey.NewsConditionTypeKey;
import org.gof.demo.worldsrv.support.enumKey.PeriodKey;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;
import org.gof.demo.worldsrv.team.TeamMember;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;

public class HomeManager  extends ManagerBase {
    /**
     * 获取实例
     *
     * @return
     */
    public static HomeManager inst() {
        return inst(HomeManager.class);
    }

    public static int MINE_WIGHT = 6;
    public static int MINE_HIGHT = 7;

    public static int AXE_SN = 4001;//镐子sn
    public static int DRILL_SN = 4002;//钻头sn
    public static int BOMB_SN = 4003;//炸弹sn


    /*******************家园矿洞分割线**********************************/
    // region 家园矿洞
    /**
     * 家园矿洞信息
     */
    public void handleMineInfoC2S(HumanObject humanObj) {
        if(humanObj.operation.mine == null && humanObj.isModUnlock(83)){
            createMine(humanObj);
        }

        if(humanObj.operation.mine == null){
            return;
        }
        Mine mine = humanObj.operation.mine;
        Map<Integer,Integer> areaToCfgMap = Utils.jsonToMapIntInt(mine.getAreaToCfgMap());
        Map<Integer,BlockVo> blockIdToUpdateMap = BlockVo.mapFromJsonString(mine.getBlockIdToUpdateMap());
        List<Integer> activeList = Utils.strToIntList(mine.getActiveList());
        MsgHome.home_mine_info_s2c.Builder msg = MsgHome.home_mine_info_s2c.newBuilder();
        msg.setMaxNum(ItemManager.inst().getRefreshGoodMaxNum(humanObj,ConfGoodsRefresh.get(AXE_SN)));
        msg.setNextTime(ItemManager.inst().getRefreshGoodNextTime(humanObj,ItemConstants.AUTO_MINE));
        msg.setArea(mine.getArea());
        msg.setBaseline(mine.getBaseline());
        for (Integer active:activeList) {
            msg.addActives(active);
        }
        //区域信息
        for (Map.Entry<Integer, Integer> entry : areaToCfgMap.entrySet()) {
            Define.p_key_value.Builder builder = Define.p_key_value.newBuilder();
            builder.setK(entry.getKey());
            builder.setV(entry.getValue());
            msg.addAreaInfo(builder);
        }
        //blockIdToUpdateMap信息
        for (Map.Entry<Integer, BlockVo> entry : blockIdToUpdateMap.entrySet()) {
            msg.addBlocks(entry.getValue().build(entry.getKey()));
        }
        humanObj.sendMsg(msg);
    }

    /**
    * 创建矿洞
    */
    public void createMine(HumanObject humanObj) {
        Map<Integer,Integer> areaToCfgMap = new HashMap<>();
        Map<Integer,BlockVo> blockIdToUpdateMap = new HashMap<>();
        List<Integer> activeList = new ArrayList<>();
        ConfGlobal confGlobal = ConfGlobal.get(602);
        Mine mine = new Mine();
        mine.setId(humanObj.id);
        mine.setAxeMax(confGlobal.value);
        areaToCfgMap.put(1,1001);
        areaToCfgMap.put(2,randomAreaCfgId());
        mine.setAreaToCfgMap(Utils.mapIntIntToJSON(areaToCfgMap));
        //清空开始3行
        ConfMineTemplate confMineTemplate = ConfMineTemplate.get(areaToCfgMap.get(1));
        for(int row = 1; row <= 3; row++){
            for(int col = 1; col <= MINE_WIGHT; col++){
                int blockId = row*100+col;
                activeList.add(blockId);
                int sn = confMineTemplate.arrange[(row-1)*MINE_WIGHT+col-1];
                ConfMineGrid confMineGrid = ConfMineGrid.get(sn);
                if((confMineGrid.goods == null || confMineGrid.goods.length == 0) && confMineGrid.num == 0 && confMineGrid.reward == 0){
                    continue;
                }
                BlockVo blockVo = new BlockVo();
                blockVo.sn = sn;
                blockVo.count = 0;
                blockVo.isReward = EHomeType.noReward;
                blockIdToUpdateMap.put(blockId,blockVo);
            }
        }
        //激活剩下的行。当连接的格子是激活的并且是count=0时，激活。连接的是上下左右相连的格子
        for(int row = 4; row <= MINE_HIGHT; row++){
            activateCells(row,1,areaToCfgMap,blockIdToUpdateMap,activeList,0);
        }
        mine.setBlockIdToUpdateMap(BlockVo.mapToJsonString(blockIdToUpdateMap));
        mine.setAreaToCfgMap(Utils.mapIntIntToJSON(areaToCfgMap));
        mine.setActiveList(Utils.listToString(activeList));
        mine.setBaseline(MINE_HIGHT-1);
        mine.persist();
        humanObj.operation.mine = mine;
    }

    /**
     * 家园矿洞使用物品
     * @param goodsId 使用的物品ID
     * @param blockId 目标矿块ID
     */
    public void handleMineUseGoodsC2S(HumanObject humanObj, int goodsId, int blockId) {
        Mine mine = humanObj.operation.mine;
        if (mine == null) {
            return;
        }
        List<Integer> activeList = Utils.strToIntList(mine.getActiveList());
        if (!activeList.contains(blockId)) {
            return;
        }
        ReasonResult result = ProduceManager.inst().canCostProduce(humanObj, goodsId, 1);
        if (!result.success) {
            return;
        }

        Map<Integer, Integer> areaToCfgMap = Utils.jsonToMapIntInt(mine.getAreaToCfgMap());
        Map<Integer, BlockVo> blockIdToUpdateMap = BlockVo.mapFromJsonString(mine.getBlockIdToUpdateMap());

        ConfMineGrid confMineGrid = getConfMineGrid(blockId, areaToCfgMap);
        int isReward = confMineGrid.goods == null ? EHomeType.noReward : EHomeType.isRward;
        BlockVo blockVo = blockIdToUpdateMap.getOrDefault(blockId, new BlockVo(confMineGrid.sn, confMineGrid.num, isReward));
        List<Integer> oldActiveList = new ArrayList<>(activeList);
        Map<Integer, Integer> rewardMap = new HashMap<>();
        List<Integer> blockIdChangeSet = new ArrayList<>();
        MsgHome.home_mine_use_goods_s2c.Builder msg = MsgHome.home_mine_use_goods_s2c.newBuilder();

        if (goodsId == AXE_SN) {
            handleAxeUse(humanObj, blockId, blockVo, confMineGrid, areaToCfgMap, blockIdToUpdateMap, activeList, rewardMap, blockIdChangeSet, msg);
        } else if (goodsId == DRILL_SN) {
            handleDrillUse(humanObj, blockId, blockVo, mine, areaToCfgMap, blockIdToUpdateMap, activeList, rewardMap, blockIdChangeSet, msg);
        } else if (goodsId == BOMB_SN) {
            handleBombUse(humanObj, blockId, blockVo, areaToCfgMap, blockIdToUpdateMap, activeList, rewardMap, blockIdChangeSet, msg);
        }

        ProduceManager.inst().produceAdd(humanObj, rewardMap, MoneyItemLogKey.挖矿);
        mine.setBlockIdToUpdateMap(BlockVo.mapToJsonString(blockIdToUpdateMap));
        mine.setAreaToCfgMap(Utils.mapIntIntToJSON(areaToCfgMap));
        mine.setActiveList(Utils.listToString(activeList));
        //baseLine是激活的最下面一行-1，是activeList的最大值，取的行
        int maxRow = activeList.stream().map(blockId1 -> blockId1 / 100).max(Integer::compareTo).get();
        int baseLine = maxRow - 1;
        mine.setBaseline(baseLine);
        mine.setArea(baseLine / MINE_HIGHT + 1);
        mine.update();
        msg.setArea(mine.getArea());
        msg.setBaseline(mine.getBaseline());
        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_50, 0);
        //区域信息
        for (Map.Entry<Integer, Integer> entry : areaToCfgMap.entrySet()) {
            Define.p_key_value.Builder builder = Define.p_key_value.newBuilder();
            builder.setK(entry.getKey());
            builder.setV(entry.getValue());
            msg.addAreaInfo(builder);
        }

        List<Integer> newActiveList = new ArrayList<>(activeList);
        newActiveList.removeAll(oldActiveList);
        for (Integer active : newActiveList) {
            msg.addActives(active);
            blockIdChangeSet.add(active);
        }

        for (Integer blockIdChange : blockIdChangeSet) {
            if (blockIdToUpdateMap.containsKey(blockIdChange)) {
                msg.addBlocks(blockIdToUpdateMap.get(blockIdChange).build(blockIdChange));
            }
        }
        humanObj.sendMsg(msg);
        Event.fire(EventKey.UPDATE_NEWS, "humanObj", humanObj, "newsCondition", NewsConditionTypeKey.MineDrill, "value", baseLine);
    }

    private void handleAxeUse(HumanObject humanObj, int blockId, BlockVo blockVo, ConfMineGrid confMineGrid, Map<Integer, Integer> areaToCfgMap, Map<Integer, BlockVo> blockIdToUpdateMap, List<Integer> activeList, Map<Integer, Integer> rewardMap, List<Integer> blockIdChangeSet, MsgHome.home_mine_use_goods_s2c.Builder msg) {
        //使用镐子
        if (blockVo.count == 0) {
            return;
        }
        blockVo.count--;
        if (blockVo.count == 0) {
            blockVo.isReward = EHomeType.noReward;
        }
        if (!ItemManager.inst().costTimerRecoverItem(humanObj, AXE_SN, 1, MoneyItemLogKey.挖矿).success) {
            return;
        }
        if (confMineGrid.goods != null && confMineGrid.goods.length > 0) {
            int num = confMineGrid.goods[1];
            if(confMineGrid.goods[0] == ItemConstants.goods_矿石){
                PropCalc propPlus = humanObj.getPropPlus();
                int attr = propPlus.getBigDecimal(PropKey.mineral_num.getAttributeSn()).intValue();
                num = (int)(num * (10000 + attr) / 10000.0);
            }
            rewardMap.put(confMineGrid.goods[0], num);
            Define.p_mine_reward.Builder builder = Define.p_mine_reward.newBuilder();
            builder.setId(blockId);
            Define.p_reward.Builder reward = Define.p_reward.newBuilder();
            reward.setGtid(confMineGrid.goods[0]);
            reward.setNum(num);
            builder.addReward(reward);
            msg.addReward(builder);
        }
        blockIdToUpdateMap.put(blockId, blockVo);
        if (blockVo.count == 0) {
            activateCells(blockId / 100, blockId % 100, areaToCfgMap, blockIdToUpdateMap, activeList, 0);
        }
        blockIdChangeSet.add(blockId);
    }

    private void handleDrillUse(HumanObject humanObj, int blockId, BlockVo blockVo, Mine mine, Map<Integer, Integer> areaToCfgMap, Map<Integer, BlockVo> blockIdToUpdateMap, List<Integer> activeList, Map<Integer, Integer> rewardMap, List<Integer> blockIdChangeSet, MsgHome.home_mine_use_goods_s2c.Builder msg) {
        //使用钻头，当前页一列和最最后一列的左右两个全部激活并且count变成0，和获得奖励
        if (blockVo.count != 0) {
            return;
        }
        if (!ProduceManager.inst().checkAndCostItem(humanObj, DRILL_SN, 1, MoneyItemLogKey.挖矿).success) {
            return;
        }
        int startRow = mine.getBaseline() - MINE_HIGHT + 1 + 1;
        int endRow = mine.getBaseline() + 1;
        int col = blockId % 100;
        for (int row = startRow; row <= endRow; row++) {
            processBlock(humanObj, row, col, areaToCfgMap, blockIdToUpdateMap, activeList, rewardMap, blockIdChangeSet, msg);
        }
        //末尾左边的格子和右边的格子都要激活
        if (col > 1) {
            processBlock(humanObj, endRow, col - 1, areaToCfgMap, blockIdToUpdateMap, activeList, rewardMap, blockIdChangeSet, msg);
        }
        if (col < MINE_WIGHT) {
            processBlock(humanObj, endRow, col + 1, areaToCfgMap, blockIdToUpdateMap, activeList, rewardMap, blockIdChangeSet, msg);
        }
    }

    private void processBlock(HumanObject humanObj, int row, int col, Map<Integer, Integer> areaToCfgMap, Map<Integer, BlockVo> blockIdToUpdateMap, List<Integer> activeList, Map<Integer, Integer> rewardMap, List<Integer> blockIdChangeSet, MsgHome.home_mine_use_goods_s2c.Builder msg) {
        int tempBlockId = row * 100 + col;
        ConfMineGrid tempConfMineGrid = getConfMineGrid(tempBlockId, areaToCfgMap);
        if (tempConfMineGrid == null) {
            return;
        }
        int hasReward = tempConfMineGrid.goods == null ? EHomeType.noReward : EHomeType.isRward;
        BlockVo tempBlockVo = blockIdToUpdateMap.getOrDefault(tempBlockId, new BlockVo(tempConfMineGrid.sn, tempConfMineGrid.num, hasReward));
        if (tempBlockVo.count != 0) {
            tempBlockVo.isReward = EHomeType.noReward;
            tempBlockVo.count = 0;
            if (tempConfMineGrid.goods != null && tempConfMineGrid.goods.length > 0) {
                int num = tempConfMineGrid.goods[1];
                if(tempConfMineGrid.goods[0] == ItemConstants.goods_矿石){
                    PropCalc propPlus = humanObj.getPropPlus();
                    int attr = propPlus.getBigDecimal(PropKey.mineral_num.getAttributeSn()).intValue();
                    num = (int)(num * (10000 + attr) / 10000.0);
                }
                rewardMap.put(tempConfMineGrid.goods[0], rewardMap.getOrDefault(tempConfMineGrid.goods[0], 0) + num);
                Define.p_mine_reward.Builder builder = Define.p_mine_reward.newBuilder();
                builder.setId(tempBlockId);
                Define.p_reward.Builder reward = Define.p_reward.newBuilder();
                reward.setGtid(tempConfMineGrid.goods[0]);
                reward.setNum(num);
                builder.addReward(reward);
                msg.addReward(builder);
            }
            blockIdToUpdateMap.put(tempBlockId, tempBlockVo);
            blockIdChangeSet.add(tempBlockId);
        }
        activateCells(row, col, areaToCfgMap, blockIdToUpdateMap, activeList, 0);
    }

    private void handleBombUse(HumanObject humanObj, int blockId, BlockVo blockVo, Map<Integer, Integer> areaToCfgMap, Map<Integer, BlockVo> blockIdToUpdateMap, List<Integer> activeList, Map<Integer, Integer> rewardMap, List<Integer> blockIdChangeSets, MsgHome.home_mine_use_goods_s2c.Builder msg) {
        // 使用炸弹，相邻的位移是2以内的全部激活并且count变成0，和获得奖励
        if (blockVo.count != 0) {
            return;
        }
        if (!ProduceManager.inst().checkAndCostItem(humanObj, BOMB_SN, 1, MoneyItemLogKey.挖矿).success) {
            return;
        }
        int[] dir = new int[]{1, -1, 100, -100, 2, -2, 200, -200, -100 - 1, -100 + 1, 100 - 1, 100 + 1, 0};
        for (int i = 0; i < dir.length; i++) {
            int tempBlockId = blockId + dir[i];
            int tempRow = tempBlockId / 100;
            int tempCol = tempBlockId % 100;
            if (tempRow < 1 || tempCol < 1 || tempCol > MINE_WIGHT) {
                continue;
            }
            processBlock(humanObj, tempRow, tempCol, areaToCfgMap, blockIdToUpdateMap, activeList, rewardMap, blockIdChangeSets, msg);
        }
    }

    /**
     * 家园矿洞获取奖励
     * @param blockId 目标矿块ID
     */
    public void handleMineGetRewardC2S(HumanObject humanObj, int blockId) {
        Mine mine = humanObj.operation.mine;
        if(mine == null){
            return;
        }
        List<Integer> activeList = Utils.strToIntList(mine.getActiveList());
        if(!activeList.contains(blockId)){
            return;
        }
        ConfMineGrid confMineGrid = getConfMineGrid(blockId,Utils.jsonToMapIntInt(mine.getAreaToCfgMap()));
        if(confMineGrid == null || confMineGrid.goods == null || confMineGrid.goods.length == 0){
            return;
        }
        Map<Integer,BlockVo> blockIdToUpdateMap = BlockVo.mapFromJsonString(mine.getBlockIdToUpdateMap());
        BlockVo blockVo = blockIdToUpdateMap.getOrDefault(blockId,new BlockVo(confMineGrid.sn,confMineGrid.num,EHomeType.isRward));
        if(blockVo.isReward == EHomeType.noReward){
            return;
        }
        blockVo.isReward = EHomeType.noReward;
        PropCalc propPlus = humanObj.getPropPlus();
        int attr = propPlus.getBigDecimal(PropKey.mineral_num.getAttributeSn()).intValue();
        int num = (int)(confMineGrid.goods[1] * (10000 + attr) / 10000.0);
        ProduceManager.inst().produceAdd(humanObj,confMineGrid.goods[0],num,MoneyItemLogKey.挖矿);
        blockIdToUpdateMap.put(blockId,blockVo);
        mine.setBlockIdToUpdateMap(BlockVo.mapToJsonString(blockIdToUpdateMap));
        mine.update();
        MsgHome.home_mine_get_reward_s2c.Builder msg = MsgHome.home_mine_get_reward_s2c.newBuilder();
        Define.p_mine_reward.Builder builder = Define.p_mine_reward.newBuilder();
        builder.setId(blockId);
        Define.p_reward.Builder reward = Define.p_reward.newBuilder();
        reward.setGtid(confMineGrid.goods[0]);
        reward.setNum(num);
        builder.addReward(reward);
        msg.setReward(builder);
        Define.p_mine_block.Builder blockBuilder = blockVo.build(blockId);
        msg.setBlocks(blockBuilder);
        humanObj.sendMsg(msg);
    }

    /**
     * 家园矿洞自动挖矿
     *
     * autoType1向下优先 2资源优先
     */
    public void handleMineAutoC2S(HumanObject humanObj, int autoType){
        Mine mine = humanObj.operation.mine;
        if (mine == null) {
            return;
        }

        int axeNum = ItemManager.inst().getItemNum(humanObj, AXE_SN);
        if (axeNum < ConfGlobal.get(ConfGlobalKey.mine_auto_min_num.SN).value) {
            return;
        }
        Map<Integer, Integer> areaToCfgMap = Utils.jsonToMapIntInt(mine.getAreaToCfgMap());
        Map<Integer, BlockVo> blockIdToUpdateMap = BlockVo.mapFromJsonString(mine.getBlockIdToUpdateMap());
        List<Integer> activeList = Utils.strToIntList(mine.getActiveList());

        int maxRow = activeList.stream().map(blockId1 -> blockId1 / 100).max(Integer::compareTo).get();
        Map<Integer, Integer> rewardMap = new HashMap<>();
        if (autoType == 1) {
            // 获得当前位置，然后一直向下
            Collections.sort(activeList, Collections.reverseOrder());
            int blockId = activeList.get(0);// 拿到最底下激活的格子
            int row = blockId / 100;// 开始挖的行
            int col = blockId % 100;// 向下挖的列
            for (int i = 0; i < axeNum;) {
                blockId = row * 100 + col;
                ConfMineGrid confMineGrid = getConfMineGrid(blockId, areaToCfgMap);
                int isReward = confMineGrid.goods == null ? EHomeType.noReward : EHomeType.isRward;
                BlockVo blockVo = blockIdToUpdateMap.computeIfAbsent(blockId, k -> new BlockVo(confMineGrid.sn, confMineGrid.num, isReward));
                if (confMineGrid.num != 0) {
                    blockVo.count--;
                    i++;// 只有格子有需要消耗，i才++
                }
                if (blockVo.count <= 0) {
                    blockVo.isReward = EHomeType.noReward;

                    if (confMineGrid.goods != null && confMineGrid.goods.length > 0) {
                        int[] rewards = rewardMineGrid(humanObj, confMineGrid);
                        int num = rewardMap.computeIfAbsent(rewards[0], k -> 0);
                        rewardMap.put(rewards[0], num + rewards[1]);
                    }

                    activateCells(row, col, areaToCfgMap, blockIdToUpdateMap, activeList, 0);
                    row++;// 格子挖掉，行向下加，列不动
                    maxRow++;
                }
            }
        } else {
            //资源优先 2.向下走X/2米，返还每一行的奖励，并清空所有格子（X为铁矿数量）
            int row = axeNum / 2;
            for (int i = 0; i < row; i++, maxRow++) {
                int firstActiveJ = 1;
                // 找到最底行第一个激活的格子
                for (int j = 1; j <= MINE_WIGHT; j++) {
                    int blockId = maxRow * 100 + j;
                    if (activeList.contains(blockId)) {
                        firstActiveJ = j;
                        blockDirectReward(humanObj, blockId, areaToCfgMap, blockIdToUpdateMap, activeList, rewardMap);
                        break;
                    }
                }
                // 从最左边开始直接挖到激活格子
                for (int j = 1; j < firstActiveJ; j++) {
                    int blockId = maxRow * 100 + j;
                    blockDirectReward(humanObj, blockId, areaToCfgMap, blockIdToUpdateMap, activeList, rewardMap);
                }
                // 从激活格子开始挖到最右边的格子
                for (int j = firstActiveJ + 1; j <= MINE_WIGHT; j++) {
                    int blockId = maxRow * 100 + j;
                    blockDirectReward(humanObj, blockId, areaToCfgMap, blockIdToUpdateMap, activeList, rewardMap);
                }
            }
        }
        ItemManager.inst().costTimerRecoverItem(humanObj, AXE_SN, axeNum, MoneyItemLogKey.挖矿);
        ProduceManager.inst().produceAdd(humanObj, rewardMap, MoneyItemLogKey.挖矿);
        mine.setBlockIdToUpdateMap(BlockVo.mapToJsonString(blockIdToUpdateMap));
        mine.setAreaToCfgMap(Utils.mapIntIntToJSON(areaToCfgMap));
        mine.setActiveList(Utils.listToString(activeList));
        int oldBaseLine = mine.getBaseline();
        int baseLine = maxRow - 1;
        mine.setBaseline(baseLine);
        mine.setArea(baseLine / MINE_HIGHT + 1);

        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_50, 0);
        
        MsgHome.home_mine_auto_use_goods_s2c.Builder msg = MsgHome.home_mine_auto_use_goods_s2c.newBuilder();
        msg.setAddBaseline(baseLine - oldBaseLine);
        for (Map.Entry<Integer, Integer> rewardMapEntry : rewardMap.entrySet()) {
            Define.p_reward.Builder pRewardMsg = Define.p_reward.newBuilder();
            pRewardMsg.setGtid(rewardMapEntry.getKey());
            pRewardMsg.setNum(rewardMapEntry.getValue());
            msg.addReward(pRewardMsg);
        }
        humanObj.sendMsg(msg);

        handleMineInfoC2S(humanObj);
        Event.fire(EventKey.UPDATE_NEWS, "humanObj", humanObj, "newsCondition", NewsConditionTypeKey.MineDrill, "value", baseLine);
    }

    /**
     * 格子直接挖掉，不管多少血
     */
    private void blockDirectReward(HumanObject humanObj, int blockId, Map<Integer, Integer> areaToCfgMap, Map<Integer, BlockVo> blockIdToUpdateMap
            , List<Integer> activeList, Map<Integer, Integer> rewardMap) {
        ConfMineGrid confMineGrid = getConfMineGrid(blockId, areaToCfgMap);
        int isReward = confMineGrid.goods == null ? EHomeType.noReward : EHomeType.isRward;
        BlockVo blockVo = blockIdToUpdateMap.computeIfAbsent(blockId, k -> new BlockVo(confMineGrid.sn, confMineGrid.num, isReward));
        blockVo.count = 0;
        blockVo.isReward = EHomeType.noReward;

        if (confMineGrid.goods != null && confMineGrid.goods.length > 0) {
            int[] rewards = rewardMineGrid(humanObj, confMineGrid);
            int num = rewardMap.computeIfAbsent(rewards[0], k -> 0);
            rewardMap.put(rewards[0], num + rewards[1]);
        }

        int row = blockId / 100;
        int col = blockId % 100;
        activateCells(row, col, areaToCfgMap, blockIdToUpdateMap, activeList, 0);
    }

    /**
     * 返回格子奖励，抽出来是为了统一计算矿石的加成
     */
    public int[] rewardMineGrid(HumanObject humanObj, ConfMineGrid confMineGrid) {
        int num = confMineGrid.goods[1];
        if (confMineGrid.goods[0] == ItemConstants.goods_矿石) {
            PropCalc propPlus = humanObj.getPropPlus();
            int attr = propPlus.getBigDecimal(PropKey.mineral_num.getAttributeSn()).intValue();
            num = (int) (num * (10000 + attr) / 10000.0);
        }
        return new int[] {
                confMineGrid.goods[0],
                num
        };
    }

    /**
     * 获得区域
     * @param blockId 目标矿块ID
     */
    public int getArea(int blockId) {
        return (blockId / 100 - 1) / MINE_HIGHT + 1;
    }

    /**
     * 随机获得一个区域配置
     */
    private int randomAreaCfgId() {
        Collection<ConfMineTemplate> confMineTemplates = ConfMineTemplate.findAll();
        int[] weights = new int[confMineTemplates.size()];
        int i = 0;
        for (ConfMineTemplate confMineTemplate : confMineTemplates) {
            weights[i] = confMineTemplate.itemWeights;
            i++;
        }
        int index = Utils.randomByWeight(weights);
        return confMineTemplates.toArray(new ConfMineTemplate[0])[index].sn;
    }

    /**
     * 获得格子配置
     */
    private ConfMineGrid getConfMineGrid(int blockId, Map<Integer,Integer> areaToCfgMap) {
        int area = getArea(blockId);
        if(!areaToCfgMap.containsKey(area)){
            Log.farm.error("areaToCfgMap error blockId={},areaToCfgMap = {}",blockId,areaToCfgMap);
            return null;
        }
        int sn = areaToCfgMap.get(area);
        ConfMineTemplate confMineTemplate = ConfMineTemplate.get(sn);
        int row = blockId/100;
        int rowInArea = row%MINE_HIGHT == 0 ? MINE_HIGHT : row%MINE_HIGHT;
        int index = (rowInArea-1)*MINE_WIGHT+blockId%100-1;
        int snGrid = confMineTemplate.arrange[index];
        return ConfMineGrid.get(snGrid);
    }

    /**
     * 激活格子
     */
    public void activateCells(int row, int col, Map<Integer,Integer> areaToCfgMap, Map<Integer,BlockVo> blockIdToUpdateMap, List<Integer> activeList, int depth) {
        if (row < 1 || col < 1 || col > MINE_WIGHT) {
            return;
        }
        if (depth > 42) {
            Log.farm.error("Recursion depth limit reached in activateCells");
            return;
        }

        int blockId = row * 100 + col;
        int[] dir = new int[]{-100, -1, 100, 1};
        if(!activeList.contains(blockId)){
            for (int i = 0; i < 4; i++) {
                if(i == 3 && col >= MINE_WIGHT){
                    continue;
                }
                if(i == 1 && col == 1){
                    continue;
                }
                if(i == 0 && row == 1){
                    continue;
                }
                int tempBlockId = blockId + dir[i];
                int tempRow = tempBlockId / 100;
                int tempCol = tempBlockId % 100;
                int tempArea = getArea(tempRow, areaToCfgMap, blockIdToUpdateMap, activeList);
                int rowInArea = tempRow%MINE_HIGHT == 0 ? MINE_HIGHT : tempRow%MINE_HIGHT;
                int sn = ConfMineTemplate.get(areaToCfgMap.get(tempArea)).arrange[(rowInArea - 1) * MINE_WIGHT + tempCol - 1];
                int count = ConfMineGrid.get(sn).num;
                if (blockIdToUpdateMap.containsKey(tempBlockId)) {
                    count = blockIdToUpdateMap.get(tempBlockId).count;
                }
                if (activeList.contains(tempBlockId) && count == 0) {
                    activeList.add(blockId);
                    break;
                }
            }
        }

        //激活周围的格子
        if(!activeList.contains(blockId)){
            return;
        }
        for (int i = 0; i < 4; i++) {
            if(i == 3 && col >= MINE_WIGHT){
                continue;
            }
            if(i == 1 && col == 1){
                continue;
            }
            if(i == 0 && row == 1){
                continue;
            }
            int tempBlockId = blockId + dir[i];
            int tempRow = tempBlockId / 100;
            int tempCol = tempBlockId % 100;
            if (activeList.contains(tempBlockId)) {
                continue;
            }
            activateCells(tempRow, tempCol, areaToCfgMap, blockIdToUpdateMap, activeList, depth + 1);
        }
    }

    private int getArea(int row, Map<Integer,Integer> areaToCfgMap, Map<Integer,BlockVo> blockIdToUpdateMap, List<Integer> activeList) {
        int area = (row - 1) / MINE_HIGHT + 1;
        if (!areaToCfgMap.containsKey(area)) {
            areaToCfgMap.put(area, randomAreaCfgId());
            //最小的key不是area-2的返回
            Set<Integer> areaSet = new HashSet<>(areaToCfgMap.keySet());// 再包一层集合，不然等等对map移除不知道会不会影响到
            int maxArea = areaSet.stream().max(Integer::compareTo).get();
            int delArea = maxArea - 4;
            for (Integer areaKey : areaSet) {
                if (areaKey <= delArea) {
                    areaToCfgMap.remove(areaKey);// 原来的代码不删除这个Map，导致每次最小都是1，下面两行的删除其实没有效果
                }
            }
            //同时删除activeList旧的数据
            activeList.removeIf(active -> getArea(active) <= delArea);
            //删掉blockIdToUpdateMap旧的数据
            blockIdToUpdateMap.entrySet().removeIf(entry -> getArea(entry.getKey()) <= delArea);
        }
        return area;
    }


    // endregion 家园矿洞
    /*******************家园矿洞分割线**********************************/



    /*******************家园农场分割线****************************************/

    public void initFarm(HumanObject humanObj) {
        FarmData farmData = humanObj.farmData;
        if(farmData.farm2 != null){
            farmData.tabMap = farmData.tabMapFromJsonString(farmData.farm2.getTabMap());
        }
    }

    /**
     * 家园农场信息
     * @param roleId 农场拥有者角色ID
     */
    public void handleFarmInfoC2S(HumanObject humanObj, long roleId, int reson) {
        if(roleId == 0){
            roleId = humanObj.id;
        }
        if(!Util.isSameZone(humanObj.id,roleId)){
            return;
        }
        long findId = roleId;
        EntityManager.getEntityAsync(Farm.class, findId, (res)-> {
            if(!res.succeeded()){
                Log.farm.error("login load Farm data humanId:{},err:{}",Farm.class.getSimpleName(), findId);
                return;
            }
            Farm farm = (Farm) res.result();
            if(farm == null){
                return;
            }
            if(findId == humanObj.id){
                humanObj.farmData.farm = farm;
            }
            if(reson == EHomeType.UPDATE_REASON_OPEN){
//                checkAndSetLandTimeout(humanObj,farm);
//                checkAndSetBuildingTimeout(farm);
            }
            if (reson == EHomeType.UPDATE_REASON_BUILD){
                updateBuildPorpCalc(humanObj);
                HumanManager.inst().sendMsg_role_goods_refresh_list_s2c(humanObj);
            }
            sendFarmData(humanObj,farm,findId);
        });
    }

    public void sendFarmData(HumanObject humanObj, Farm farm, long roleId) {
        Farm2 farm2 = null;
        if(roleId == humanObj.id){
            farm2 = humanObj.farmData.farm2;
        }
        if(farm == null){
            return;
        }
        if(farm2 != null){
            checkAndSetStealTimeout(farm2);
        }
        for(int i = EHomeType.LAND_1; i <= EHomeType.LAND_MAX; i++){
            LandVo landVo = new LandVo();
            landVo.fromJsonString(getLand(farm,i));
        }
        String name = humanObj.getHuman().getName();
        if(roleId != humanObj.id){
            EntityManager.getEntityAsync(Human.class,roleId,(res)->{
                if(res.failed()){
                    Log.farm.error("sendFarmData error:{}",res.cause().getMessage());
                    return;
                }
                String farmName = name;
                Human human = (Human)res.result();
                if(human != null){
                    farmName = human.getName();
                }else {
                    Log.farm.error("sendFarmData human is null roleId:{}",roleId);
                }
                MsgHome.home_farm_info_s2c.Builder msg = buildFarmInfo(farm,humanObj.farmData.farm2,farmName);
                humanObj.sendMsg(msg);
            });
        }else {
            MsgHome.home_farm_info_s2c.Builder msg = buildFarmInfo(farm,farm2,name);
            humanObj.sendMsg(msg);
        }
    }

    /**
     * 构建家园农场信息不包含名字
     */
    private MsgHome.home_farm_info_s2c.Builder buildFarmInfo(Farm farm, Farm2 farm2, String name) {
        MsgHome.home_farm_info_s2c.Builder msg = MsgHome.home_farm_info_s2c.newBuilder();
        msg.setRoleId(farm.getId());
        msg.setName(name);
        msg.setLevel(farm.getLevel());
        msg.setExp(farm.getExp());
        //菜地
        for(int i = EHomeType.LAND_1; i <= EHomeType.LAND_MAX; i++){
            LandVo landVo = new LandVo();
            landVo.fromJsonString(getLand(farm,i));
            if(landVo.landId == 0){
                continue;
            }
            Define.p_farm_land.Builder land = landVo.build(farm.getId());
            msg.addLandList(land);
        }
        //建筑
        Map<Integer, BuildVo> buildingMap = BuildVo.mapFromJsonString(farm.getBuildMap());
        for (Map.Entry<Integer, BuildVo> entry : buildingMap.entrySet()) {
            msg.addBuildingList(entry.getValue().build());
        }
        //偷取他人
        if(farm2 != null){
            List<StolenVo> stolenList = getStolenList(farm2);
            for (StolenVo stolenVo : stolenList) {
                msg.addSelfStolenList(stolenVo.build());
            }
            msg.setCanHelpBattle(farm2.getHelpTimes()>=EHomeType.DAILY_MAX_HELP_BATTLE ? 0 : 1);
        }else {
            msg.setCanHelpBattle(0);
        }

        return msg;
    }

    private List<StolenVo> getStolenListAndRemove(Farm2 farm2){
        //移除掉过期的返回
        List<StolenVo> stolenList = StolenVo.listFromJsonString(farm2.getStealList());
        int now = (int)(Port.getTime()/Time.SEC);

        // 使用迭代器移除过期的偷取记录
        Iterator<StolenVo> iterator = stolenList.iterator();
        while (iterator.hasNext()) {
            StolenVo stolenVo = iterator.next();
            if (now > stolenVo.endTime) {
                iterator.remove();
            }
        }
        // 更新farm2的偷取列表
        farm2.setStealList(StolenVo.listToJsonString(stolenList));
        farm2.update();
        return stolenList;
    }

    private List<StolenVo> getStolenList(Farm2 farm2){
        List<StolenVo> stolenList = StolenVo.listFromJsonString(farm2.getStealList());
        List<StolenVo> stolenInTimeList = new ArrayList<>();
        int now = (int)(Port.getTime()/Time.SEC);
        for (StolenVo stolenVo : stolenList){
            if (now < stolenVo.endTime) {
                stolenInTimeList.add(stolenVo);
            }
        }
        return stolenInTimeList;
    }

    /**
     * 家园农场种植
     * @param seedId 种子ID
     * @param landId 农田ID
     */
    public void handleFarmPlantC2S(HumanObject humanObj, int seedId, int landId) {
        if(ConfFarmPos.get(landId) == null && landId!=0){
            return;
        }

        long findId = humanObj.id;
        EntityManager.getEntityAsync(Farm.class, findId, (res) -> {
            if(!res.succeeded()){
                Log.farm.error("load Farm data failed humanId:{},err:{}",Farm.class.getSimpleName(), findId);
                return;
            }
            Farm farm = (Farm) res.result();
            if(farm == null){
                return;
            }

            MsgHome.home_farm_plant_s2c.Builder msg = MsgHome.home_farm_plant_s2c.newBuilder();
            List<Integer> landList = new ArrayList<>();
            ConfFarmSeed confFarmSeed = ConfFarmSeed.get(seedId);
            if (confFarmSeed == null) {
                Log.farm.error("confFarmSeed is null seedId={}", seedId);
                return;
            }
            LandVo landVo = new LandVo();
            landVo.fromJsonString(getLand(farm, landId));
            msg.setNewLand(landVo.build(farm.getId()));
            if (landVo.landId == 0) {
                msg.setCode(118);
                humanObj.sendMsg(msg);
                return;
            }
            if (landVo.seedId != 0) {
                msg.setCode(117);
                humanObj.sendMsg(msg);
                return;
            }
            ReasonResult result = ProduceManager.inst().canCostProduce(humanObj, confFarmSeed.sn, 1);
            if (!result.success) {
                msg.setCode(3);
                humanObj.sendMsg(msg);
                return;
            }
            plantCrop(humanObj, farm, landVo, confFarmSeed, landId);
            landList.add(landVo.landId);
            humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_主线, TaskConditionTypeKey.TASK_TYPE_31,landList.size());
            humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_战令, TaskConditionTypeKey.TASK_TYPE_31,landList.size());
            FarmServiceProxy prx = FarmServiceProxy.newInstance();
            prx.farmPlantUpdate(farm, landList);
            prx.listenResult(this::_result_farmPlantUpdate1,"humanObj",humanObj);
            msg.setNewLand(landVo.build(farm.getId()));
            msg.setCode(0);
            humanObj.sendMsg(msg);
        });
    }

    private void _result_farmPlantUpdate1(Param results, Param context) {
    }

    private void _result_farmPlantUpdate(Param results, Param context) {
        HumanObject humanObj = context.get("humanObj");
        Farm farm = results.get("farm");
        farm.reset();
        sendFarmData(humanObj, farm, humanObj.id);
    }

    private void plantCrop(HumanObject humanObj, Farm farm, LandVo landVo, ConfFarmSeed confFarmSeed, int landId) {
        landVo.seedId = confFarmSeed.sn;
        int harvestIndex = Utils.randomByWeight2D(confFarmSeed.harvest, 1);
        landVo.cfgId = confFarmSeed.harvest[harvestIndex][0];
        ConfFarmGreens confFarmGreens = ConfFarmGreens.get(landVo.cfgId);
        if (confFarmGreens == null) {
            Log.farm.error("confFarmGreens is null cfgId={}", landVo.cfgId);
            return;
        }
        landVo.cropId = idGen(humanObj, landVo.cfgId);
        ProduceManager.inst().costItem(humanObj, confFarmSeed.sn, 1, MoneyItemLogKey.家园种植);
        landVo.state = EHomeType.GROWING;
        landVo.startTime = (int) (Port.getTime() / Time.SEC);
        int speedAttr = getBuildingAttr(BuildVo.mapFromJsonString(farm.getBuildMap()), EHomeType.PLANT_SPEED_ATTR);
        int growingTime = (int)(confFarmGreens.time / (1 + speedAttr / 10000.0));
        landVo.endTime = landVo.startTime + growingTime;
        landVo.fruitCount = confFarmGreens.reward[1];
        int addAttr = getBuildingAttr(BuildVo.mapFromJsonString(farm.getBuildMap()), EHomeType.HARVEST_ADD_ATTR);
        landVo.realFruitCount = (int)(confFarmGreens.reward[1] * (1 + addAttr / 10000.0));
        setLand(farm, landId, landVo.toJsonString());
        Define.p_lang_info.Builder content = Define.p_lang_info.newBuilder();
        content.setId(EHomeType.EVENT_PLANT);
        addFarmLog(humanObj.farmData.farm2, humanObj.id, EHomeType.EVENT_PLANT, content, farm.getId());
        farm.update();
        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_31, 1,1);
    }

    private long idGen(HumanObject humanObj, int cfgId){
        return humanObj.idGen() * 10000 + cfgId;
    }

    public ConfFarmGreens getConfFarmGreens(long cropid){
        return ConfFarmGreens.get((int)(cropid % 10000));
    }

    /**
     * 获得建筑属性值
     */
    public int getBuildingAttr(Map<Integer,BuildVo> buildMap, int type) {
        int attr = 0;
        for (Map.Entry<Integer, BuildVo> entry : buildMap.entrySet()) {
            ConfFarmBuildings_0 confFarmBuilding = ConfFarmBuildings_0.get(entry.getKey(),entry.getValue().level);
            if(confFarmBuilding == null||confFarmBuilding.effect == null || confFarmBuilding.effect.length == 0){
                continue;
            }
            for(int i = 0; i < confFarmBuilding.effect.length; i++){
                if(confFarmBuilding.effect[i][0] == type){
                    attr += confFarmBuilding.effect[i][1];
                }
            }
        }
        return attr;
    }

    /**
     * 家园农场施肥
     * @param roleId 农场拥有者角色ID
     * @param landId 农田ID
     * @param fertilizerId 肥料ID
     * @param num 施肥数量
     */
    public void handleFarmFertilizeC2S(HumanObject humanObj, long roleId, int landId, int fertilizerId, int num) {
        ConfGoods confGoods = ConfGoods.get(fertilizerId);
        if(roleId == 0){
            roleId = humanObj.id;
        }
        if(confGoods == null || confGoods.type != ItemConstants.肥料){
            return;
        }
        if ((humanObj.id == roleId && confGoods.effect[0][0] != EHomeType.FERTILIZER_SELF)
                || (humanObj.id != roleId && confGoods.effect[0][0] != EHomeType.FERTILIZER_FRIEND)) {
            return;
        }
        ReasonResult result = ItemManager.inst().costTimerRecoverItem(humanObj,fertilizerId,num,MoneyItemLogKey.家园施肥);
        if(!result.success){
            return;
        }
        FarmServiceProxy prx = FarmServiceProxy.newInstance();
        int accTime = confGoods.effect[1][0]*num;

        prx.farmFertilize(roleId,landId,accTime);
        prx.listenResult(this::_result_farmFertilize,"humanObj",humanObj,"roleId",roleId,"fertilizerId",fertilizerId,"num",num,"accTime",accTime,"accEffect",confGoods.effect[1][0]);
    }

    private void _result_farmFertilize(Param results, Param context) {
        HumanObject humanObj = context.get("humanObj");
        long roleId = context.get("roleId");
        int fertilizerId = context.get("fertilizerId");
        int num = context.get("num");
        int accTime = context.get("accTime");
        int accEffect = context.get("accEffect");
        ReasonResult result = results.get("result");
        int realAccTime = results.containsKey("realAccTime") ? results.get("realAccTime"):0;
        LandVo landVo = results.get("landVo");
        // 计算实际使用的肥料数量
        int realUsedNum = (int)Math.ceil((double)realAccTime / accEffect);

        // 如果实际使用的数量少于预计使用的数量,返还多余的肥料
        if (realUsedNum < num) {
            int returnNum = num - realUsedNum;
            ProduceManager.inst().produceAdd(humanObj, fertilizerId, returnNum, MoneyItemLogKey.家园施肥回滚);
        }
        if(landVo == null){
            return;
        }
        MsgHome.home_farm_fertilize_s2c.Builder msg = MsgHome.home_farm_fertilize_s2c.newBuilder();
        msg.setRoleId(roleId);
        msg.setCode(result.param.get());
        msg.setNewLand(landVo.build(roleId));
        humanObj.sendMsg(msg);
        Farm2 farm2 = humanObj.farmData.farm2;
        if(farm2 != null){
            Define.p_lang_info.Builder content = Define.p_lang_info.newBuilder();
            if (roleId == humanObj.id) {
                content.setId(EHomeType.EVENT_FERTILIZE);
                content.addArgList(Define.p_key_value_name.newBuilder().setK(0).setV(num).setName(""));
                content.addArgList(Define.p_key_value_name.newBuilder().setK(0).setV(accTime/60).setName(""));
                addFarmLog(farm2, humanObj.id, EHomeType.EVENT_FERTILIZE, content, roleId);
            } else {
                content.setId(EHomeType.EVENT_HELP);
                EntityManager.getEntityAsync(Human.class, roleId, (res) -> {
                    if (res.failed()) {
                        Log.farm.error("无法获取玩家Human数据roleId={}", roleId);
                        return;
                    }
                    Human human = (Human) res.result();
                    if (human == null) {
                        return;
                    }
                    content.addArgList(Define.p_key_value_name.newBuilder().setK(3).setV(0).setName(human.getName()));
                    content.addArgList(Define.p_key_value_name.newBuilder().setK(0).setV(num).setName(""));
                    content.addArgList(Define.p_key_value_name.newBuilder().setK(0).setV(accTime / 60).setName(""));
                    addFarmLog(farm2, humanObj.id, EHomeType.EVENT_HELP, content, humanObj.id);
                });
            }
        }

        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_主线, TaskConditionTypeKey.TASK_TYPE_33, 1);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_战令, TaskConditionTypeKey.TASK_TYPE_33, 1);

        if (roleId == humanObj.id) {
            return;
        }
        // 帮忙好友施肥
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_成就任务, TaskConditionTypeKey.TASK_TYPE_105, 1);
        HumanGlobalServiceProxy prx1 = HumanGlobalServiceProxy.newInstance();
        prx1.getInfo(roleId);
        prx1.listenResult(this::_result_humanGlobalInfoFertilize, "humanObj", humanObj, "roleId", roleId, "landVo", landVo, "num", num, "totalAccTime", accTime);
    }

    private void _result_humanGlobalInfoFertilize(Param results, Param context) {
        HumanObject humanObj = context.get("humanObj");
        long roleId = context.get("roleId");
        LandVo landVo = context.get("landVo");
        int num = context.get("num");
        int totalAccTime = context.get("totalAccTime");

        HumanGlobalInfo targetInfo = results.get();
        if (targetInfo != null) {
            // 在线通知写日志
            HumanObjectServiceProxy humanPrx = HumanObjectServiceProxy
                    .newInstance(targetInfo.nodeId, targetInfo.portId,
                            targetInfo.id);
            humanPrx.helpFertilize(humanObj.id, humanObj.getHuman().getName(),landVo, num, totalAccTime);
        }else {
            EntityManager.getEntityAsync(Farm2.class, roleId, (res) -> {
                if (res.failed()) {
                    Log.farm.error("无法获取玩家Farm2数据roleId={}", roleId);
                    return;
                }
                Farm2 farm2 = (Farm2) res.result();
                if (farm2 == null) {
                    Log.farm.error("Farm2数据为空roleId={}", roleId);
                    return;
                }
                Define.p_lang_info.Builder content = Define.p_lang_info.newBuilder();
                content.setId(EHomeType.EVENT_BE_HELP);
                content.addArgList(Define.p_key_value_name.newBuilder().setK(3).setV(0).setName(humanObj.getHuman().getName()));
                content.addArgList(Define.p_key_value_name.newBuilder().setK(0).setV(num).setName(""));
                content.addArgList(Define.p_key_value_name.newBuilder().setK(0).setV(totalAccTime / 60).setName(""));
                HomeManager.inst().addFarmLog(farm2, humanObj.id, EHomeType.EVENT_BE_HELP, content, farm2.getId());
            });
        }
    }

    public void addFarmLog(Farm2 farm2, long selfId, int actionId, Define.p_lang_info.Builder content, long otherId) {
        addFarmLog(farm2, selfId, actionId, content, otherId, 0);
    }


    public void addFarmLog(Farm2 farm2, long selfId, int actionId, Define.p_lang_info.Builder content, long otherId, int time) {
        List<String> farmLogs = Utils.strToStringList(farm2.getFarmLog());

        long newLogId = farmLogs.isEmpty() ? 1 : Utils.fromProtoString(farmLogs.get(farmLogs.size() - 1), Define.p_farm_log.class).getId() + 1;

        buildFarmLogAsync(newLogId, selfId, actionId, content, otherId, time, res -> {
            if (res.succeeded()) {
                Define.p_farm_log.Builder log = res.result();
                farmLogs.add(Utils.toProtoString(log.build()));

                //农场日志只存20条
                while (farmLogs.size() > ConfGlobal.get(ConfGlobalKey.farm_record_limit).value) {
                    farmLogs.remove(0);
                }

                farm2.setFarmLog(String.join(",", farmLogs));
                farm2.update();
            }
        });
    }

    private static void buildFarmLogAsync(long id, long selfId, int actionId, Define.p_lang_info.Builder content, long otherId, int time, Handler<AsyncResult<Define.p_farm_log.Builder>> handler) {
        time = time == 0 ? (int)(Port.getTime() / Time.SEC) : time;
        Define.p_farm_log.Builder log = Define.p_farm_log.newBuilder();
        log.setId(id);
        Define.p_head.Builder head = Define.p_head.newBuilder();
        head.setUrl("");
        head.setId(0);
        head.setFrameId(0);
        log.setHead(head);
        log.setName("");
        log.setLevel(1);
        log.setFarmName("");
        int finalTime = time;
        if (selfId != otherId) {
            EntityManager.getEntityAsync(Human.class, otherId, res -> {
                if (res.failed()) {
                    Log.game.error("无法获取玩家Human数据otherId={}", otherId);
                    handler.handle(Future.failedFuture(res.cause()));
                    return;
                }
                Human human = (Human) res.result();
                if (human != null) {
                    log.setName(human.getName());
                    log.setLevel(human.getLevel());
                    log.setFarmId(human.getCurrentHeadFrameSn());
                    head.setId(human.getHeadSn());
                    head.setFrameId(human.getCurrentHeadFrameSn());
                    log.setHead(head);
                    log.setFarmName(human.getName());
                }
                log.setRoleId(otherId);
                log.setActionId(actionId);
                log.setTime(finalTime);
                log.setContent(content);
                log.setIsRead(0);
                log.setFarmId(otherId);
                handler.handle(Future.succeededFuture(log));
            });
        } else {
            log.setRoleId(otherId);
            log.setActionId(actionId);
            log.setTime(time);
            log.setContent(content);
            log.setIsRead(0);
            log.setFarmId(otherId);
            handler.handle(Future.succeededFuture(log));
        }
    }

    /**
     * 家园农场偷菜
     * @param roleId 农场拥有者角色ID
     * @param landId 农田ID
     */
    public void handleFarmPickC2S(HumanObject humanObj, long roleId, int landId) {
        roleId = roleId == 0 ? humanObj.id : roleId;
        if (humanObj.id == roleId) {
            Farm2 farm2 = humanObj.farmData.farm2;
            if (farm2 == null) {
                return;
            }
            FarmServiceProxy prx = FarmServiceProxy.newInstance();
            prx.pickSelf(humanObj.id, landId);
            prx.listenResult(this::_result_farmPickSelf, "humanObj", humanObj);
            return;
        }
        Farm2 farm2 = humanObj.farmData.farm2;
        if (farm2 == null) {
            return;
        }
        List<StolenVo> stolenList = getStolenListAndRemove(farm2);
        int vipNum = PrivilegeManager.inst().getPrivilegeEffectValue(humanObj, PrivilegeType.STEALVALUE);
        if (stolenList.size() >= 1 + vipNum) {
            MsgHome.home_farm_pick_s2c.Builder msg = MsgHome.home_farm_pick_s2c.newBuilder();
            msg.setCode(163);
            msg.setRoleId(roleId);
            long finalRoleId = roleId;
            EntityManager.getEntityAsync(Farm.class, roleId, (res) -> {
                if (res.failed()) {
                    Log.farm.error("无法获取玩家Farm数据roleId={}", finalRoleId);
                    return;
                }
                Farm farm = res.result();
                if (farm == null) {
                    Log.farm.error("Farm数据为空roleId={}", finalRoleId);
                    return;
                }
                LandVo landVo = new LandVo();
                landVo.fromJsonString(getLand(farm, landId));
                msg.setNewLand(landVo.build(finalRoleId));
                humanObj.sendMsg(msg);
            });
            return;
        }
        RobberVo robberVo = new RobberVo(humanObj.id, humanObj.getHuman().getName(), humanObj.getHuman().getHeadSn(), humanObj.getHuman().getLevel());
        FarmServiceProxy prx = FarmServiceProxy.newInstance();
        prx.farmPickOther(robberVo, roleId, landId);
        prx.listenResult(this::_result_farmPickOther, "humanObj", humanObj, "roleId", roleId, "landId", landId, "farm2", farm2);
    }

    private void _result_farmPickOther(Param results, Param context) {
        HumanObject humanObj = context.get("humanObj");
        long roleId = context.get("roleId");
        Farm2 farm2 = context.get("farm2");

        LandVo landVo = results.get("landVo");
        ReasonResult result = results.get("result");
        MsgHome.home_farm_pick_s2c.Builder msg = MsgHome.home_farm_pick_s2c.newBuilder();
        msg.setRoleId(roleId);
        msg.setNewLand(landVo.build(roleId));
        if(!result.success){
            msg.setCode(result.param.get());
            humanObj.sendMsg(msg);
            return;
        }
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_主线, TaskConditionTypeKey.TASK_TYPE_34,1);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_战令, TaskConditionTypeKey.TASK_TYPE_34,1);
        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_34, 1,1);
        EntityManager.getEntityAsync(Human.class, roleId, (res) -> {
            if (res.failed()) {
                Log.farm.error("无法获取玩家Human数据roleId={}", roleId);
                return;
            }
            Human human = (Human) res.result();
            if (human == null) {
                Log.farm.error("human is null roleId={}", roleId);
                return;
            }
            List<StolenVo> stolenList = StolenVo.listFromJsonString(farm2.getStealList());
            stolenList.add(new StolenVo(landVo.cropId, roleId, landVo.startTime, landVo.endTime, human.getName(), human.getHeadSn(), human.getLevel()));
            farm2.setStealList(StolenVo.listToJsonString(stolenList));
            farm2.update();
            Define.p_farm_land.Builder p_land = landVo.build(roleId);
            MsgHome.home_farm_pick_s2c.Builder msg1 = MsgHome.home_farm_pick_s2c.newBuilder();
            msg1.setCode(0);
            msg1.setRoleId(roleId);
            msg1.setNewLand(p_land);
            humanObj.sendMsg(msg1);
            sendStolenUpdateMessage(humanObj, landVo, human, 0); // 正在偷

            HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
            prx.sendMsg(roleId, msg1.build());
        });
    }

    public void stolenCompleted(HumanObject humanObj, FarmVo farmVo, int landId, RobberVo robberVo) {
        Farm2 farm2 = humanObj.farmData.farm2;
        if(farm2 == null){
            Log.farm.error("farm2 is null humanId={}",humanObj.id);
            return;
        }

        addStolenCompletedLog(farm2,farmVo,landId,robberVo);

        LandVo landVo = farmVo.landMap.get(landId);
        if(landVo == null){
            Log.farm.error("landVo is null landId={}",landId);
            return;
        }
        MsgHome.home_farm_pick_s2c.Builder msg = MsgHome.home_farm_pick_s2c.newBuilder();
        msg.setCode(0);
        msg.setRoleId(humanObj.id);
        msg.setNewLand(landVo.build(humanObj.id));
        humanObj.sendMsg(msg);
    }

    public void addStolenCompletedLog(Farm2 farm2, FarmVo farmVo, int landId, RobberVo robberVo) {
        if(farm2 == null){
            Log.farm.error("farm2 is null humanId={}",farm2.getId());
            return;
        }
        List<Long> enemyList = Utils.strToLongList(farm2.getEnemyList());
        if(!enemyList.contains(robberVo.id)){
            enemyList.add(robberVo.id);
            farm2.setEnemyList(Utils.listToString(enemyList));
        }

        JSONObject json = getStolenCompletedJson(farmVo,landId,robberVo);
        if(json == null){
            return;
        }
        Define.p_lang_info.Builder content = Define.p_lang_info.newBuilder();
        content.setId(EHomeType.EVENT_BE_STEAL);
        content.addArgList(Define.p_key_value_name.newBuilder().setK(3).setV(0).setName(robberVo.name == null ? "" : robberVo.name));
        content.addArgList(Define.p_key_value_name.newBuilder().setK(2).setV(json.getIntValue("rSn")).setName(""));
        content.addArgList(Define.p_key_value_name.newBuilder().setK(0).setV(json.getIntValue("sNum")).setName(""));
        addFarmLog(farm2,farm2.getId(),EHomeType.EVENT_BE_STEAL,content,robberVo.id);
    }

    public JSONObject getStolenCompletedJson(FarmVo farmVo, int landId, RobberVo robberVo) {
        LandVo landVo = farmVo.landMap.get(landId);
        if(landVo == null){
            Log.farm.error("landVo is null landId={}",landId);
            return null;
        }
        int addAttr = getBuildingAttr(farmVo.buildMap,EHomeType.HARVEST_ADD_ATTR);

        ConfFarmGreens confFarmGreens = getConfFarmGreens(landVo.cropId);
        if(confFarmGreens == null){
            Log.farm.error("confFarmGreens is null cfgId={}",landVo.cropId);
            return null;
        }
        int rewardSn = confFarmGreens.reward[0];
        ConfGoods confGoods = ConfGoods.get(rewardSn);
        if(confGoods == null){
            Log.farm.error("confGoods is null sn={}",rewardSn);
            return null;
        }
        int stealRewardNum = (int)(confFarmGreens.reward[1] * confFarmGreens.stolenRatio/10000.0 * (1+addAttr/10000.0));
        int convertNum = stealRewardNum * confGoods.effect[1][0];

        JSONObject json = new JSONObject();
        json.put("rSn",rewardSn);
        json.put("sNum",stealRewardNum);
        json.put("cNum",convertNum);
        json.put("thiefId",robberVo.id);
        json.put("name",robberVo.name);
        json.put("time", (int)(Port.getTime()/Time.SEC));
        return json;
    }

    public void stealCompleted(HumanObject humanObj, FarmVo farmVo, int landId) {
        Farm2 farm2 = humanObj.farmData.farm2;
        if(farm2 == null){
            Log.farm.error("farm2 is null humanId={}",humanObj.id);
            return;
        }
        LandVo landVo = farmVo.landMap.get(landId);
        if(landVo == null){
            Log.farm.error("landVo is null landId={}",landId);
            return;
        }
        removeStolenList(farm2,farmVo.id,landVo.cropId);
        int addAttr = getBuildingAttr(farmVo.buildMap,EHomeType.HARVEST_ADD_ATTR);

        ConfFarmGreens confFarmGreens = getConfFarmGreens(landVo.cropId);
        if(confFarmGreens == null){
            Log.farm.error("confFarmGreens is null cfgId={}",landVo.cropId);
            return;
        }
        int rewardSn = confFarmGreens.reward[0];
        ConfGoods confGoods = ConfGoods.get(rewardSn);
        if(confGoods == null){
            Log.farm.error("confGoods is null sn={}",rewardSn);
            return;
        }
        EntityManager.getEntityAsync(Human.class, farmVo.id, (res) -> {
            if (res.failed()) {
                Log.farm.error("stealCompleted:human is null roleId={}", farmVo.id);
                return;
            }
            Human human = res.result();
            if (human == null) {
                Log.farm.error("stealCompleted:human is null roleId={}", farmVo.id);
                return;
            }

            int stealNum = (int)(confFarmGreens.reward[1] * confFarmGreens.stolenRatio / 10000.0 * (1 + addAttr / 10000.0));
            int convertItemSn = confGoods.effect[0][0];
            int vipNum = PrivilegeManager.inst().getPrivilegeEffectValue(humanObj, PrivilegeType.STEALINCOME);
            int convertNum = stealNum * confGoods.effect[1][0];
            int addConvertNum = (int)(convertNum * vipNum / 100.0);
            ProduceManager.inst().produceAdd(humanObj, convertItemSn, convertNum + addConvertNum, MoneyItemLogKey.家园偷菜);
            InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, EHomeType.showType_10400005, convertItemSn, convertNum + addConvertNum);
            Define.p_lang_info.Builder content = Define.p_lang_info.newBuilder();
            content.addArgList(Define.p_key_value_name.newBuilder().setK(3).setV(0).setName(human.getName()));
            content.addArgList(Define.p_key_value_name.newBuilder().setK(0).setV(stealNum).setName(""));
            content.addArgList(Define.p_key_value_name.newBuilder().setK(2).setV(rewardSn).setName(""));
            content.addArgList(Define.p_key_value_name.newBuilder().setK(0).setV(convertNum).setName(""));
            if (vipNum > 0) {
                content.setId(EHomeType.EVENT_VIP_STEAL);
                content.addArgList(Define.p_key_value_name.newBuilder().setK(0).setV(addConvertNum).setName(""));
                addFarmLog(farm2, humanObj.id, EHomeType.EVENT_VIP_STEAL, content, farmVo.id);
            } else {
                content.setId(EHomeType.EVENT_STEAL);
                addFarmLog(farm2, humanObj.id, EHomeType.EVENT_STEAL, content, farmVo.id);
            }
            sendStolenUpdateMessage(humanObj, landVo, human, 2); // 偷完成
        });
    }

    public void stealFaild(HumanObject humanObj, long farmId, LandVo landVo) {
        Farm2 farm2 = humanObj.farmData.farm2;
        if(farm2 == null){
            Log.farm.error("farm2 is null humanId={}",humanObj.id);
            return;
        }
        removeStolenList(farm2,farmId,landVo.cropId);
        EntityManager.getEntityAsync(Human.class, farmId, (res) -> {
            if (res.failed()) {
                Log.farm.error("stealFaild:无法获取玩家Human数据roleId={}", farmId);
                return;
            }
            Human human = (Human) res.result();
            if (human == null) {
                Log.farm.error("stealFaild:human is null roleId={}", farmId);
                return;
            }
            sendStolenUpdateMessage(humanObj, landVo, human, 1); // 偷失败
        });
    }

    public void sendStolenUpdateMessage(HumanObject humanObj, LandVo landVo, Human landHuman, int type) {
        MsgHome.home_farm_update_self_stolen_s2c.Builder msg = MsgHome.home_farm_update_self_stolen_s2c.newBuilder();
        msg.setType(type);
        Define.p_farm_self_stolen.Builder selfStolen = Define.p_farm_self_stolen.newBuilder();
        selfStolen.setCropId(landVo.cropId);
        selfStolen.setId(landHuman.getId());
        selfStolen.setStartTime(landVo.startTime);
        selfStolen.setEndTime(landVo.endTime);
        selfStolen.setName(landHuman.getName());
        Define.p_head.Builder head = Define.p_head.newBuilder();
        head.setUrl("");
        head.setId(landHuman.getHeadSn());
        head.setFrameId(landHuman.getCurrentHeadFrameSn());
        selfStolen.setHead(head);
        selfStolen.setLevel(landHuman.getLevel());
        msg.setSelfStolen(selfStolen);
        humanObj.sendMsg(msg);
    }

    private StolenVo removeStolenList(Farm2 farm2, long farmId, long cropId) {
        List<StolenVo> stolenList = StolenVo.listFromJsonString(farm2.getStealList());
        Iterator<StolenVo> iterator = stolenList.iterator();
        StolenVo removedStolenVo = null;
        while (iterator.hasNext()) {
            StolenVo stolenVo = iterator.next();
            if (stolenVo.id == farmId && stolenVo.cropId == cropId) {
                removedStolenVo = stolenVo;
                iterator.remove();
                break;
            }
        }
        farm2.setStealList(StolenVo.listToJsonString(stolenList));
        farm2.update();
        return removedStolenVo;
    }

    public void addStealCompletedLog(Farm2 farm2, int rewardSn, int stealNum, int convertNum,int vipConvertNum, StolenVo stolenVo, int time){
        Define.p_lang_info.Builder content = Define.p_lang_info.newBuilder();
        content.addArgList(Define.p_key_value_name.newBuilder().setK(3).setV(0).setName(stolenVo.name));
        content.addArgList(Define.p_key_value_name.newBuilder().setK(0).setV(stealNum).setName(""));
        content.addArgList(Define.p_key_value_name.newBuilder().setK(2).setV(rewardSn).setName(""));
        content.addArgList(Define.p_key_value_name.newBuilder().setK(0).setV(convertNum).setName(""));
        if(vipConvertNum > 0){
            content.setId(EHomeType.EVENT_VIP_STEAL);
            content.addArgList(Define.p_key_value_name.newBuilder().setK(0).setV(vipConvertNum).setName(""));
            addFarmLog(farm2,farm2.getId(),EHomeType.EVENT_VIP_STEAL,content,stolenVo.id,time);
        }else {
            content.setId(EHomeType.EVENT_STEAL);
            addFarmLog(farm2,farm2.getId(),EHomeType.EVENT_STEAL,content,stolenVo.id,time);
        }
    }

    public ReasonResult stealCheckAndSet(LandVo landVo, RobberVo robberVo){
        ReasonResult result = new ReasonResult(true);
        result.setParam(0);//成功
        if(landVo == null || landVo.landId == 0){
            result.success = false;
            result.setParam(118);//该田地未解锁
            return result;
        }
        if (landVo.cropId == 0 || landVo.cfgId == 0) {
            result.success = false;
            result.setParam(119);//该田地未种植
            return result;
        }
        ConfFarmGreens conf = ConfFarmGreens.get(landVo.cfgId);
        if(landVo.state != EHomeType.MATURE){
            result.success = false;
            result.setParam(124);//该田地不可偷菜
            return result;
        }
        if(landVo.robberInfoMap.containsKey(robberVo.id)){
            result.success = false;
            result.setParam(124);//该田地已被偷过
            return result;
        }

        int stealCount = 0;
        for (Map.Entry<Long, Long> entry : landVo.robberInfoMap.entrySet()) {
            stealCount += entry.getValue();
        }
        if(stealCount >= conf.tryStolenLimit){
            result.success = false;
            result.setParam(124);//该田地偷菜次数已达上限
            return result;
        }

        if(landVo.robberInfoMap.size() >= conf.stolenLimit){
            result.success = false;
            result.setParam(124);//该田地偷菜人数已达上限
            return result;
        }

        landVo.robberInfoMap.put(robberVo.id, 1L);
        landVo.state= EHomeType.STEALING;
        landVo.robber = robberVo;
        landVo.startTime = (int)(Port.getTime()/Time.SEC);
        landVo.endTime = landVo.startTime + conf.stolenTime;
        return result;
    }

    @Listener(value = EventKey.POCKET_LINE_HANDLE_ONE, subStr = PocketLineEventSubKey.FARM_STEAL)
    public void pocketLine_FARM_STEAL(Param param) {
        HumanObject humanObj = param.get("humanObj");
        Farm2 farm2 = humanObj.farmData.farm2;
        if(farm2 == null){
            Log.farm.error("farm2 is null humanId={}",humanObj.id);
            return;
        }
        PocketLine p = param.get("pocketLine");
        JSONObject jo = Utils.toJSONObject(p.getParam());
        int t = jo.getIntValue("t");
        long farmId = jo.getLongValue("fId");
        if(t == 1){
            //偷成功
            long cropId = jo.getLongValue("crop");
            int rewardSn = jo.getIntValue("sn");
            int stealNum = jo.getIntValue("sNum");
            int convertNum = jo.getIntValue("cNum");
            int time = jo.getIntValue("time");
            int vipNum = PrivilegeManager.inst().getPrivilegeEffectValue(humanObj, PrivilegeType.STEALINCOME);
            int addConvertNum = (int)(convertNum*vipNum/100.0);
            ConfGoods confGoods = ConfGoods.get(rewardSn);
            if(confGoods != null){
                ProduceManager.inst().produceAdd(humanObj, confGoods.effect[0][0], convertNum+addConvertNum, MoneyItemLogKey.家园偷菜);
            }
            StolenVo stolenVo = removeStolenList(farm2,farmId,cropId);
            if(stolenVo == null){
                return;
            }
            addStealCompletedLog(farm2, rewardSn, stealNum, convertNum, addConvertNum, stolenVo, time);
        }else {
            //偷失败
            String landVoStr = jo.getString("landVo");
            LandVo landVo = new LandVo(landVoStr);
            removeStolenList(farm2,farmId,landVo.cropId);
        }
        farm2.update();
    }

    @Listener(value = EventKey.POCKET_LINE_HANDLE_ONE, subStr = PocketLineEventSubKey.FARM_STOLEN)
    public void pocketLine_FARM_STOLEN(Param param) {
        HumanObject humanObj = param.get("humanObj");
        Farm2 farm2 = humanObj.farmData.farm2;
        if(farm2 == null){
            Log.farm.error("farm2 is null humanId={}",humanObj.id);
            return;
        }

        PocketLine p = param.get("pocketLine");
        JSONObject jo = Utils.toJSONObject(p.getParam());
        int rewardSn = jo.getIntValue("rSn");
        int stealNum = jo.getIntValue("sNum");
        long thiefId = jo.getLongValue("thiefId");
        String name = jo.getString("name");
        int time = jo.getIntValue("time");
        if(name == null){
            return;
        }
        //偷菜完成日志
        Define.p_lang_info.Builder content = Define.p_lang_info.newBuilder();
        content.setId(EHomeType.EVENT_BE_STEAL);
        content.addArgList(Define.p_key_value_name.newBuilder().setK(3).setV(0).setName(name));
        content.addArgList(Define.p_key_value_name.newBuilder().setK(2).setV(rewardSn).setName(""));
        content.addArgList(Define.p_key_value_name.newBuilder().setK(0).setV(stealNum).setName(""));
        addFarmLog(farm2,farm2.getId(),EHomeType.EVENT_BE_STEAL,content,thiefId,time);
    }

    @Listener(value = EventKey.POCKET_LINE_HANDLE_ONE, subStr = PocketLineEventSubKey.FARM_UPGRADE_FINISH)
    public void pocketLine_FARM_UPGRADE_FINISH(Param param) {
        HumanObject humanObj = param.get("humanObj");
        updateBuildPorpCalc(humanObj);
    }

    /**
     * 家园农场收获
     * @param landId 农田ID
     */
    public void handleFarmHarvestC2S(HumanObject humanObj, int landId) {
        Farm2 farm2 = humanObj.farmData.farm2;
        if(farm2 == null){
            return;
        }
        FarmServiceProxy prx = FarmServiceProxy.newInstance();
        prx.farmHarvest(humanObj.id,landId);
        prx.listenResult(this::_result_farmHarvest,"humanObj",humanObj,"landId",landId,"farm2",farm2);
    }

    private void _result_farmHarvest(Param results, Param context) {
        HumanObject humanObj = context.get("humanObj");
        int landId = context.get("landId");
        Farm2 farm2 = context.get("farm2");
        Farm farm = results.get("farm");
        farm.reset();
        humanObj.farmData.farm = farm;
        ReasonResult result = results.get("result");
        LandVo oldLandVo = results.get("oldLandVo");
        List<Integer> upgradeConfList = results.get("upgradeConfList");
        ConfFarmGreens confFarmGreens = ConfFarmGreens.get(oldLandVo.cfgId);
        if(!result.success){
            send_err_home_farm_harvest_s2c(humanObj,result.param.get(),farm,oldLandVo);
            return;
        }
        if(confFarmGreens == null){
            Log.farm.error("confFarmGreens is null cfgId={}",oldLandVo.cfgId);
            return;
        }
        int rewardSn = confFarmGreens.reward[0];
        ConfGoods confGoods = ConfGoods.get(rewardSn);
        if(confGoods == null){
            return;
        }
        int rewardNum = oldLandVo.realFruitCount;
        int convertItemSn = confGoods.effect[0][0];
        int convertNum = rewardNum*confGoods.effect[1][0];
        ProduceManager.inst().produceAdd(humanObj, convertItemSn, convertNum, MoneyItemLogKey.家园收获);
        if (upgradeConfList.size() != 0) {
            unlockLand(humanObj, farm);
            for (Integer confSn : upgradeConfList) {
                ConfFarmLevel confNextLevel = ConfFarmLevel.get(confSn);
                ProduceManager.inst().produceAdd(humanObj, confNextLevel.effect, MoneyItemLogKey.家园升级);
                HumanManager.inst().updatePowerPar(humanObj, EModule.FarmLv, confNextLevel.power);
            }
            ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_53, 0);
            buildingPointFix(humanObj);
        }
        addHarvestLog(farm2, rewardNum, convertNum, confGoods.sn);
        MsgHome.home_farm_harvest_s2c.Builder msg = MsgHome.home_farm_harvest_s2c.newBuilder();
        msg.setCode(result.param.get());
        oldLandVo.clear();
        msg.setNewLand(oldLandVo.build(humanObj.id));
        msg.setLevel(farm.getLevel());
        msg.setExp(farm.getExp());
        Define.p_reward.Builder p_reward = Define.p_reward.newBuilder();
        p_reward.setGtid(rewardSn);
        p_reward.setNum(rewardNum);
        msg.addRewardList(p_reward);
        humanObj.sendMsg(msg);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_主线, TaskConditionTypeKey.TASK_TYPE_32,1);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_战令, TaskConditionTypeKey.TASK_TYPE_32,1);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_回归任务, TaskConditionTypeKey.TASK_TYPE_32, 1);
        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_32, 1,1);
    }

    private void send_err_home_farm_harvest_s2c(HumanObject humanObj, int result, Farm farm, LandVo landVo){
        MsgHome.home_farm_harvest_s2c.Builder msg = MsgHome.home_farm_harvest_s2c.newBuilder();
        msg.setCode(result);
        msg.setNewLand(landVo.build(humanObj.id));
        msg.setLevel(farm.getLevel());
        msg.setExp(farm.getExp());
        humanObj.sendMsg(msg);
    }

    private void addHarvestLog(Farm2 farm2, int harvestNum, int convertNum, int convertItemSn) {
        //"收获了%s个%s，并转化成%s个果蔬贡品"
        Define.p_lang_info.Builder content = Define.p_lang_info.newBuilder();
        content.setId(EHomeType.EVENT_HARVEST);
        content.addArgList(Define.p_key_value_name.newBuilder().setK(0).setV(harvestNum).setName(""));
        content.addArgList(Define.p_key_value_name.newBuilder().setK(2).setV(convertItemSn).setName(""));
        content.addArgList(Define.p_key_value_name.newBuilder().setK(0).setV(convertNum).setName(""));
        addFarmLog(farm2,farm2.getId(),EHomeType.EVENT_HARVEST,content,farm2.getId());
    }

    private void _result_farmPickSelf(Param results, Param context){
        HumanObject humanObj = context.get("humanObj");
        ReasonResult result = results.get("result");
        LandVo landVo = results.get("landVo");
        if(landVo == null){
            Log.farm.error("landVo is null");
            return;
        }
        MsgHome.home_farm_pick_s2c.Builder msg = MsgHome.home_farm_pick_s2c.newBuilder();
        msg.setCode(result.param.get());
        msg.setRoleId(humanObj.id);
        msg.setNewLand(landVo.build(humanObj.id));
        humanObj.sendMsg(msg);
    }

    public void farmUpgrade(HumanObject humanObj, Farm farm, int addExp) {
        int level = farm.getLevel();
        int exp = farm.getExp() + addExp;
        farm.setExp(exp);
        boolean isUpgrade = false;
        ConfFarmLevel confNextLevel = ConfFarmLevel.get(level + 1);
        while (confNextLevel != null && exp >= confNextLevel.expend) {
            // 升级
            farm.setLevel(confNextLevel.sn);
            isUpgrade = true;
            // 解锁土地
            unlockLand(humanObj, farm);
            ProduceManager.inst().produceAdd(humanObj, confNextLevel.effect, MoneyItemLogKey.家园升级);
            HumanManager.inst().updatePowerPar(humanObj, EModule.FarmLv, confNextLevel.power);

            // 下一等级
            level = farm.getLevel();
            confNextLevel = ConfFarmLevel.get(level + 1);
        }
        farm.update();
        if(isUpgrade){
            ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_53, 0);
        }
    }

    public void unlockLand(HumanObject humanObj, Farm farm) {
        Collection<ConfFarmPos> confFarmPosList = ConfFarmPos.findBy(ConfFarmPos.K.sn, OrderBy.ASC);
        for (ConfFarmPos confFarmPos : confFarmPosList) {
            if (confFarmPos.condition == null || confFarmPos.condition.length == 0) {
                continue;
            }
            LandVo landVo = new LandVo(getLand(farm, confFarmPos.sn));
            if (landVo.landId == 0 && confFarmPos.condition[0][1] <= farm.getLevel()) {
                landVo.landId = confFarmPos.sn;
                setLand(farm, confFarmPos.sn, landVo.toJsonString());
                FarmServiceProxy proxy = FarmServiceProxy.newInstance();
                proxy.farmPlantUpdate(farm, Collections.singletonList(landVo.landId));
                proxy.listenResult(this::_result_farmPlantUpdate,"humanObj",humanObj);
                farm.update();
                break;
            }
        }
    }

    public void unlockVipLand(HumanObject humanObj){
        int vipUnlockNum = PrivilegeManager.inst().getPrivilegeEffectValue(humanObj, PrivilegeType.FIELDVALUE);
        if(vipUnlockNum <= 0){
            return;
        }

        EntityManager.getEntityAsync(Farm.class, humanObj.id, (res) -> {
            if (res.failed()) {
                Log.farm.error("无法获取玩家Farm数据humanId={}", humanObj.id);
                return;
            }
            Farm farm = (Farm) res.result();
            if (farm == null) {
                Log.farm.error("farm is null humanId={}", humanObj.id);
                return;
            }
            int tempVipUnlockNum = vipUnlockNum;
            Collection<ConfFarmPos> confFarmPosList = ConfFarmPos.findAll();
            List<ConfFarmPos> sortedList = new ArrayList<>(confFarmPosList);
            sortedList.sort(Comparator.comparingInt(conf -> conf.sn));

            for (int i = sortedList.size() - 1; i >= 0; i--) {
                if (tempVipUnlockNum <= 0) {
                    break;
                }
                ConfFarmPos confFarmPos = sortedList.get(i);
                if (confFarmPos.condition == null || confFarmPos.condition.length == 0) {
                    continue;
                }
                LandVo landVo = new LandVo(getLand(farm, confFarmPos.sn));
                if (landVo.landId == 0) {
                    landVo.landId = confFarmPos.sn;
                    setLand(farm, confFarmPos.sn, landVo.toJsonString());
                    farm.update();
                    FarmServiceProxy proxy = FarmServiceProxy.newInstance();
                    proxy.farmPlantUpdate(farm, Collections.singletonList(landVo.landId));
                    proxy.listenResult(this::_result_farmPlantUpdate, "humanObj", humanObj);
                }
                tempVipUnlockNum--;
            }
        });
    }

    public void buildingPointFix(HumanObject humanObj){
        Farm farm = humanObj.farmData.farm;
        if(farm==null){
            return;
        }
        if(farm.getLevel() < 50){
            return;
        }
        Map<Integer, BuildVo> buildMap = BuildVo.mapFromJsonString(farm.getBuildMap());
        if(buildMap.values().stream().mapToInt(vo->vo.level).sum() >= 30){
            return;
        }

        int totalPoint = 1275;
        int usedPoint = 0;
        final int POINT_GOODS_SN = 10;
        int bagPoint = ItemManager.inst().getItemNum(humanObj,POINT_GOODS_SN);

        for (Map.Entry<Integer, BuildVo> entry : buildMap.entrySet()) {
            BuildVo buildVo = entry.getValue();
            for (int i = 0; i < buildVo.level; i++) {
                ConfFarmBuildings_0 confFarmBuildings = ConfFarmBuildings_0.get(entry.getKey(), i + 1);
                if (confFarmBuildings == null || confFarmBuildings.cost.length < 1 ||confFarmBuildings.cost[0].length < 2) {
                    continue;
                }
                usedPoint += confFarmBuildings.cost[0][1];
            }
        }
        int fixPoint = totalPoint - usedPoint - bagPoint;
        ProduceManager.inst().produceAdd(humanObj,POINT_GOODS_SN,fixPoint,MoneyItemLogKey.道具修复);

        Log.farm.error("玩家庄园建筑点数修复成功，玩家ID={},修复点数={}",humanObj.id,fixPoint);
    }

    public ReasonResult pickCheckAndSet(FarmVo farmVo, int landId){
        ReasonResult result = new ReasonResult(true);
        result.setParam(0);
        LandVo landVo = farmVo.landMap.get(landId);
        if(landVo == null || landVo.landId == 0){
            result.success = false;
            result.setParam(118);//该田地未解锁
            return result;
        }
        if(landVo.state != EHomeType.MATURE){
            result.success = false;
            result.setParam(122);//该田地不可收获
            return result;
        }
        ConfFarmGreens confFarmGreens = ConfFarmGreens.get(landVo.cfgId);
        if(confFarmGreens == null){
            Log.farm.error("confFarmGreens is null cfgId={}",landVo.cfgId);
            result.success = false;
            return result;
        }
        landVo.state = EHomeType.COLLECTING;
        landVo.startTime = (int)(Port.getTime()/Time.SEC);
        landVo.endTime = landVo.startTime+confFarmGreens.harvestTime;
        landVo.accTime = 0;
        int buildAddAttr = getBuildingAttr(farmVo.buildMap,EHomeType.HARVEST_ADD_ATTR);
        int rewardNum = (int)(confFarmGreens.reward[1] * (1+buildAddAttr/10000.0));
        int stealRewardNum = 0;
        if(landVo.robberInfoMap.size() > 0){
            stealRewardNum = (int)(rewardNum * confFarmGreens.stolenRatio/10000.0)*landVo.robberInfoMap.size();
        }
        landVo.realFruitCount = rewardNum - stealRewardNum;
        farmVo.landMap.put(landId,landVo);
        return result;
    }

    public ReasonResult harvestCheckAndSet(FarmVo farmVo, int landId){
        ReasonResult result = new ReasonResult(true);
        result.setParam(0);
        LandVo landVo = farmVo.landMap.get(landId);
        if (landVo == null || landVo.landId == 0) {
            result.success = false;
            result.setParam(118);//该田地未解锁
            return result;
        }
        if (landVo.state != EHomeType.COLLECTING && landVo.state != EHomeType.WAIT_FETCH) {
            result.success = false;
            result.setParam(122);//该田地不可收获
            return result;
        }
        LandVo newLandVo = new LandVo(landVo.landId);
        farmVo.landMap.put(landId,newLandVo);
        return result;
    }

    /**
     * 家园农场建筑升级
     * @param cfgId 建筑配置ID
     */
    public void handleFarmBuildingLevUpC2S(HumanObject humanObj, int cfgId) {
        if(ConfFarmBuildings_0.get(cfgId,1) == null){
            return;
        }

        EntityManager.getEntityAsync(Farm.class, humanObj.id, (res) -> {
            if (res.failed()) {
                Log.farm.error("无法获取玩家Farm数据humanId={}", humanObj.id);
                return;
            }
            Farm farm = (Farm) res.result();
            if (farm == null) {
                Log.farm.error("Farm数据为空humanId={}", humanObj.id);
                return;
            }
            Map<Integer, BuildVo> buildVoMap = BuildVo.mapFromJsonString(farm.getBuildMap());
            BuildVo buildVo = buildVoMap.getOrDefault(cfgId, new BuildVo());
            ConfFarmBuildings_0 confFarmBuildingsNext = ConfFarmBuildings_0.get(cfgId, buildVo.level + 1);
            if (confFarmBuildingsNext == null) {
                return;
            }
            ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj, confFarmBuildingsNext.cost, MoneyItemLogKey.建筑升级);
            if (!result.success) {
                return;
            }
            buildVo.startTime = (int) (Port.getTime() / Time.SEC);
            buildVo.accTime = 0;
            buildVo.endTime = buildVo.startTime + confFarmBuildingsNext.time;
            buildVoMap.put(cfgId, buildVo);
            FarmServiceProxy proxy = FarmServiceProxy.newInstance();
            proxy.farmBuildUpdate(humanObj.id, buildVo);
            MsgHome.home_farm_building_lev_up_s2c.Builder msg = MsgHome.home_farm_building_lev_up_s2c.newBuilder();
            msg.setBuilding(buildVo.build());
            humanObj.sendMsg(msg);
        });
    }

    /**
     *  家园农场建筑加速
     * @param humanObj
     * @param args1
     * @param speedUpTime
     */

    public void farmBuildingSpeedUp(HumanObject humanObj, int args1, int speedUpTime) {
        FarmServiceProxy proxy = FarmServiceProxy.newInstance();
        proxy.buildingSpeedUp(humanObj.id,args1,speedUpTime);
    }

    /**
     * 家园农场日志列表
     * @param page 页数
     */
    public void handleFarmLogListC2S(HumanObject humanObj, int page) {
        Farm2 farm2 = humanObj.farmData.farm2;
        if(farm2 == null){
            return;
        }
        List<Define.p_farm_log> farmLogList = new ArrayList<>();
        String farmLog = farm2.getFarmLog();
        List<String> farmLogs = Utils.strToStringList(farmLog);
        for (String farmLogStr : farmLogs) {
            if(farmLogStr.isEmpty()){
                continue;
            }
            Define.p_farm_log p_farm_log = Utils.fromProtoString(farmLogStr,Define.p_farm_log.class);
            farmLogList.add(p_farm_log);
        }
        int total = farmLogList.size();
        int start = (page - 1) * EHomeType.LOG_PAGE_NUM;

        if (total == 0 || start >= total) {
            MsgHome.home_farm_log_list_s2c.Builder msg = MsgHome.home_farm_log_list_s2c.newBuilder();
            msg.setNum(total);
            msg.setPage(page);
            humanObj.sendMsg(msg);
            return;
        }

        int end = Math.min(page * EHomeType.LOG_PAGE_NUM, total);
        List<Define.p_farm_log> farmLogListPage = farmLogList.subList(start, end);
        List<Long> roleIds = new ArrayList<>();
        for (Define.p_farm_log p_farm_log : farmLogListPage) {
            if (!roleIds.contains(p_farm_log.getRoleId())){
                roleIds.add(p_farm_log.getRoleId());
            }
        }
        EntityManager.batchGetEntity(Human.class, roleIds,(res)->{
            if(res.failed()){
                Log.farm.error("farmLogListPage batchGetEntity failed! err:{}",res.cause().getMessage());
                return;
            }
            List<Human> humanList = res.result();
            Map<Long, Human> humanMap = new HashMap<>();
            for (Human human : humanList){
                humanMap.put(human.getId(), human);
            }
            for (Define.p_farm_log p_farm_log : farmLogListPage) {
                Human human = humanMap.get(p_farm_log.getRoleId());
                if(human == null){
                    continue;
                }
                Define.p_farm_log.Builder p_farm_logBuilder = p_farm_log.toBuilder();
                p_farm_logBuilder.setName(human.getName());
                Define.p_head.Builder head = Define.p_head.newBuilder();
                head.setId(Utils.intValue(human.getHeadSn()));
                head.setFrameId(human.getCurrentHeadFrameSn());
                head.setUrl("");
                p_farm_logBuilder.setHead(head);
                p_farm_logBuilder.setLevel(human.getLevel());
                farmLogListPage.set(farmLogListPage.indexOf(p_farm_log),p_farm_logBuilder.build());
            }
            MsgHome.home_farm_log_list_s2c.Builder msg = MsgHome.home_farm_log_list_s2c.newBuilder();
            msg.setPage(page);
            msg.setNum(total);
            msg.addAllLogList(farmLogListPage);
            humanObj.sendMsg(msg);
        });
    }

    /**
     * 家园农场战报列表
     * @param page 页数
     */
    public void handleFarmBattleReportListC2S(HumanObject humanObj, int page) {
        MsgHome.home_farm_battle_report_list_s2c.Builder msg = MsgHome.home_farm_battle_report_list_s2c.newBuilder();
        msg.setPage(page);
        if(page > 1){
            humanObj.sendMsg(msg);
            return;
        }

        String redisKey = Utils.createStr("{}{}", RedisKeys.farmBattleHistoryProtoList, humanObj.id);
        RedisTools.getListRange(EntityManager.getRedisClient(), redisKey, 0, -1, res -> {
            if(res.failed()){
                return;
            }
            JsonArray jsonArray = res.result();
            if(jsonArray == null || jsonArray.isEmpty()){
                humanObj.sendMsg(msg);
                return;
            }

            long currentTime = Port.getTime() / Time.SEC;
            int validCount = jsonArray.size();

            // 处理每条战报数据
            for (int i = 0; i < jsonArray.size(); i++) {
                try {
                    String protoBuf = jsonArray.getString(i);
                    Define.p_farm_battle_report report = Define.p_farm_battle_report.parseFrom(
                            protoBuf.getBytes(StandardCharsets.ISO_8859_1));

                    // 检查是否过期
                    if (currentTime - report.getTime() > ParamKey.historyExpireTime) {
                        validCount = i;
                        break;
                    }
                    msg.addBattleReportList(report);
                } catch (Exception e) {
                    Log.temp.error("解析战报数据失败, index={}: {}", i, e.getMessage());
                }
            }

            // 如果有过期数据,清理Redis中的过期数据
            if (validCount < jsonArray.size()) {
                if (validCount > 0) {
                    // 保留有效的数据
                    RedisTools.ltrim(EntityManager.getRedisClient(), redisKey, 0, validCount-1);
                } else {
                    // 如果所有数据都过期了，清空整个列表
                    RedisTools.del(EntityManager.getRedisClient(), redisKey);
                }
            }

            msg.setNum(msg.getBattleReportListCount());
            humanObj.sendMsg(msg);
        });
    }

    /**
     * 家园农场战斗开始
     * @param cropId 被抢夺者ID
     * @param robberId 抢夺者ID
     */
    public void handleFarmBattleBeginC2S(HumanObject humanObj, long cropId, long robberId) {
        EntityManager.getEntityAsync(Farm2.class, robberId, (res) -> {
            if (res.failed()) {
                Log.farm.error("无法获取玩家Farm2数据humanId={}", robberId);
                return;
            }
            Farm2 farm2 = (Farm2) res.result();
            if (farm2 == null) {
                Log.farm.error("farm2 is null humanId={}", robberId);
                return;
            }
            List<StolenVo> stolenList = StolenVo.listFromJsonString(farm2.getStealList());
            for (StolenVo stolenVo : stolenList) {
                if (stolenVo.cropId == cropId) {
                    humanObj.combatId = 0;
                    humanObj.combatForId = stolenVo.id;
                    humanObj.combatParam = cropId;
                }
            }
            if (humanObj.combatForId == 0) {
                return;
            }
            EntityManager.getEntityAsync(Farm.class, humanObj.combatForId, (resFarm) -> {
                if (resFarm.failed()) {
                    Log.farm.error("handleFarmBattleBeginC2S:无法获取玩家Farm数据humanId={}", humanObj.id);
                    return;
                }
                Farm farm = (Farm) resFarm.result();
                if (farm == null) {
                    Log.farm.error("handleFarmBattleBeginC2S:farm is null humanId={}", humanObj.id);
                    return;
                }
                for (int i = EHomeType.LAND_1; i <= EHomeType.LAND_MAX; i++) {
                    LandVo landVo = new LandVo(getLand(farm, i));
                    if (landVo.cropId == cropId && landVo.robber.id == robberId && !landVo.battleList.contains(humanObj.id)) {
                        humanObj.combatId = robberId;
                        break;
                    }
                }
                if (humanObj.combatId == 0) {
                    humanObj.combatParam = 0;
                    humanObj.combatForId = 0;
                    return;
                }
                HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
                prx.getHumanBrief2(robberId);
                prx.listenResult((Param results, Param ctx) -> {
                    HumanBrief brief = results.get("humanBrief");
                    if (brief == null) {
                        Log.farm.error("获取玩家数据失败，robberId={}", robberId);
                        return;
                    }
                    HumanBriefVO humanBriefVO = new HumanBriefVO(brief);
                    humanObj.combatBriefVo = humanBriefVO;
                    MsgHome.home_farm_battle_begin_s2c.Builder msg = MsgHome.home_farm_battle_begin_s2c.newBuilder();
                    msg.setCropId(cropId);
                    msg.setRobberId(robberId);
                    msg.setVid(0);
                    humanObj.combatSeed = Utils.getTimeSec();
                    msg.setSeed(humanObj.combatSeed);
                    HumanBriefVO vo = new HumanBriefVO(humanObj);
                    msg.setAtkData(vo.to_p_farm_battle_role());
                    msg.setDefData(humanBriefVO.to_p_farm_battle_role());
                    humanObj.sendMsg(msg);
                });
            });
        });
    }

    /**
     * 家园农场战斗结果
     * @param vid 战斗ID
     * @param winRoleId 胜利者ID
     */
    public void handleFarmBattleResultC2S(HumanObject humanObj, long vid, long winRoleId) {
        winRoleId = winRoleId == 0 ? humanObj.id : winRoleId;
        if (humanObj.combatId == 0 || humanObj.combatParam == 0 || humanObj.combatBriefVo == null) {
            Log.farm.error("农场战斗结果找不到数据humanId={},combatId={},combatParam={}", humanObj.id, humanObj.combatId, humanObj.combatParam);
            return;
        }
        MsgHome.home_farm_battle_result_s2c.Builder msg = MsgHome.home_farm_battle_result_s2c.newBuilder();
        msg.setCode(0);

        HumanBriefVO combatBriefVo = humanObj.combatBriefVo;
        long finalWinRoleId = winRoleId;
        if(finalWinRoleId != humanObj.id){
            msg.setVid(vid);
            msg.setIsWin(0);
            msg.setEHead(Define.p_head.newBuilder().setId(combatBriefVo.headSn).setFrameId(combatBriefVo.currentHeadFrameSn).setUrl(""));
            msg.setEName(combatBriefVo.name);
            humanObj.sendMsg(msg);
            humanObj.combatId = 0;
            humanObj.combatParam = 0;
            ArenaManager.inst().createHistoryProto(humanObj, finalWinRoleId, RedisKeys.farmBattleHistoryProtoList, false, humanObj.combatSeed);
            return;
        }
        EntityManager.getEntityAsync(Farm.class, humanObj.combatForId, (res1) -> {
            if (res1.failed()) {
                Log.farm.error("handleFarmBattleResultC2S:farm is null humanId={}", humanObj.id);
                return;
            }
            Farm farm = res1.result();
            if (farm == null) {
                Log.farm.error("handleFarmBattleResultC2S:farm is null humanId={}", humanObj.id);
                return;
            }
            RobberVo robberVo = null;
            for (int i = EHomeType.LAND_1; i <= EHomeType.LAND_MAX; i++) {
                LandVo landVo = new LandVo(getLand(farm, i));
                robberVo = landVo.robber;
                if (landVo.cropId == humanObj.combatParam && robberVo.id != 0) {
                    FarmServiceProxy proxy = FarmServiceProxy.newInstance();
                    proxy.farmBattleWin(humanObj.combatForId, i);
                    proxy.listenResult(this::_result_farmBattleWin, "humanObj", humanObj);
                    break;
                }
            }

            msg.setVid(vid);
            msg.setIsWin(finalWinRoleId == humanObj.id ? 1 : 0);
            RobberVo finalRobberVo = robberVo;
            msg.setCode(0);
            msg.setEHead(Define.p_head.newBuilder().setId(combatBriefVo.headSn).setFrameId(combatBriefVo.currentHeadFrameSn).setUrl(""));
            msg.setEName(combatBriefVo.name);
            humanObj.sendMsg(msg);

            ArenaManager.inst().createHistoryProto(humanObj, finalRobberVo.id, RedisKeys.farmBattleHistoryProtoList, true, humanObj.combatSeed);
            humanObj.combatId = 0;
            humanObj.combatParam = 0;
            humanObj.combatForId = 0;
            humanObj.combatBriefVo = null;
        });
    }

    private void _result_farmBattleWin(Param results, Param context) {
        ReasonResult reasonResult = Utils.getParamValue(results, "result", new ReasonResult(false));
        if (!reasonResult.success) {
            return;
        }
        HumanObject humanObj = Utils.getParamValue(context, "humanObj", null);
        handleFarmInfoC2S(humanObj, humanObj.id, 0);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_成就任务, TaskConditionTypeKey.TASK_TYPE_108, 1);
    }

    /**
     * 家园农场搜索列表
     */
    public void handleFarmSearchListC2S(HumanObject humanObj) {
        Farm2 farm2 = humanObj.farmData.farm2;
        if(farm2 == null){
            return;
        }
        List<Long> searchList = Utils.strToLongList(farm2.getSearchList());
        if(searchList.size() == 0){
            int searchSize = ConfGlobal.get(ConfGlobalKey.farm_steal_research.SN).intArray[1];
            FriendManager.inst().getRandFriendList(humanObj,searchSize,(res)->{
                if(res.succeeded()){
                    List<Long> searchTempList = res.result();
                    if(searchTempList != null && searchTempList.size() != 0){
                        farm2.setSearchList(Utils.listToString(searchTempList));
                        farm2.setSearchTime(Port.getTime());
                        farm2.update();
                        sendFarmSearchList(humanObj);
                    }
                }
            });
        }else {
            sendFarmSearchList(humanObj);
        }
    }

    public void sendFarmSearchList(HumanObject humanObj) {
        Farm2 farm2 = humanObj.farmData.farm2;
        if (farm2 == null) {
            return;
        }
        List<Long> searchList = Utils.strToLongList(farm2.getSearchList());
        List<Long> enemyList = Utils.strToLongList(farm2.getEnemyList());
        List<Long> mergedList = new ArrayList<>(searchList);
        mergedList.addAll(enemyList);
        MsgHome.home_farm_search_list_s2c.Builder msg = MsgHome.home_farm_search_list_s2c.newBuilder();
        EntityManager.batchGetEntity(Human.class,mergedList,(res)->{
            Map<Long,Human> humanMap = new HashMap<>();
            if(res.succeeded()){
                List<Human> humanList = res.result();
                if(humanList != null){
                    for(Human human : humanList){
                        humanMap.put(human.getId(),human);
                    }
                }
                EntityManager.batchGetEntity(Farm.class,mergedList,(res2)->{
                    Map<Long,Farm> farmMap = new HashMap<>();
                    if(res2.succeeded()) {
                        List<Farm> farmList = res2.result();
                        if (farmList != null) {
                            for (Farm farm : farmList) {
                                farmMap.put(farm.getId(), farm);
                            }
                        }
                    }
                    for (Long roleId : searchList) {
                        Define.p_farm_search_role.Builder searchInfo = buildSearchInfo(humanObj, humanMap,farmMap, roleId,StolenVo.listFromJsonString(farm2.getStealList()));
                        if(searchInfo == null){
                            continue;
                        }
                        msg.addSearchList(searchInfo);
                    }
                    int searchCd = ConfGlobal.get(ConfGlobalKey.farm_steal_research.SN).intArray[0];
                    msg.setCd(searchCd + (int) (farm2.getSearchTime() / Time.SEC));
                    for (long enemyId : enemyList) {
                        Define.p_farm_search_role.Builder searchInfo = buildSearchInfo(humanObj, humanMap, farmMap, enemyId,StolenVo.listFromJsonString(farm2.getStealList()));
                        if (searchInfo == null) {
                            continue;
                        }
                        msg.addEnemyList(searchInfo);
                    }
                    humanObj.sendMsg(msg);
                });
            }
        });
    }

    private Define.p_farm_search_role.Builder buildSearchInfo(HumanObject humanObj, Map<Long,Human> humanMap, Map<Long,Farm> farmMap, long roleId, List<StolenVo> stolenList) {
        Define.p_farm_search_role.Builder searchInfo = Define.p_farm_search_role.newBuilder();
        Human human = humanMap.get(roleId);
        if (human == null) {
            Log.farm.error("sendFarmSearchList:human is null roleId={}", roleId);
            return null;
        }
        searchInfo.setId(roleId);
        searchInfo.setName(human.getName());
        Define.p_head.Builder head = Define.p_head.newBuilder();
        head.setId(human.getHeadSn());
        head.setFrameId(human.getCurrentHeadFrameSn());
        head.setUrl("");
        searchInfo.setHead(head);
        searchInfo.setLevel(human.getLevel());
        //设置被偷玩家的状态和时间
        for (StolenVo stolenVo : stolenList) {
            if (stolenVo.id == roleId) {
                searchInfo.setState(EHomeType.FARM_STEALING);
                searchInfo.setTime(stolenVo.endTime);
                return searchInfo;
            }
        }
        if(human.getFarmState()==EHomeType.FARM_STEAL){
            Farm farm = farmMap.get(roleId);
            boolean hasCanSteal = false;
            if(farm != null){
                for (int i = EHomeType.LAND_1; i <= EHomeType.LAND_MAX; i++) {
                    String landStr = getLand(farm, i);
                    if(Utils.isEmptyJSONString(landStr)){
                        continue;
                    }
                    LandVo landVo = new LandVo(landStr);
                    if (landVo.state == EHomeType.FARM_STEAL) {
                        if(!landVo.robberInfoMap.keySet().contains(humanObj.id) && landVo.robberInfoMap.size() < 3){
                            hasCanSteal = true;
                            break;
                        }
                    }
                }
            }
            if(!hasCanSteal){
                searchInfo.setState(EHomeType.FARM_NONE);
            }else {
                searchInfo.setState(EHomeType.FARM_STEAL);
            }
        }else {
            searchInfo.setState(human.getFarmState());
        }
        searchInfo.setTime(human.getFarmTime());
        return searchInfo;
    }

    /**
     * 家园农场搜索刷新
     */
    public void handleFarmSearchRefreshC2S(HumanObject humanObj) {
        Farm2 farm2 = humanObj.farmData.farm2;
        if(farm2 == null){
            return;
        }
        int searchCd = ConfGlobal.get(ConfGlobalKey.farm_steal_research.SN).intArray[0];
        if (Port.getTime() - farm2.getSearchTime() < searchCd * Time.SEC) {
            return;
        }
        FriendManager.inst().getRandFriendList(humanObj, ConfGlobal.get(ConfGlobalKey.farm_steal_research.SN).intArray[1],(res)->{
            if(res.failed()){
                return;
            }
            List<Long> searchList = res.result();
            if(searchList==null || searchList.size()==0){
                return;
            }
            farm2.setSearchList(Utils.listToString(searchList));
            farm2.setSearchTime(Port.getTime());
            farm2.update();
            MsgHome.home_farm_search_refresh_s2c.Builder msg = MsgHome.home_farm_search_refresh_s2c.newBuilder();
            msg.setCd(searchCd + (int) (farm2.getSearchTime() / Time.SEC));

            EntityManager.batchGetEntity(Human.class, searchList, (res2) -> {
                Map<Long, Human> humanMap = new HashMap<>();
                if (res2.succeeded()) {
                    List<Human> humanList = res2.result();
                    if (humanList != null) {
                        for (Human human : humanList) {
                            humanMap.put(human.getId(), human);
                        }
                    }
                    EntityManager.batchGetEntity(Farm.class, searchList, (res3) -> {
                        Map<Long, Farm> farmMap = new HashMap<>();
                        if (res3.succeeded()) {
                            List<Farm> farmList = res3.result();
                            if (farmList != null) {
                                for (Farm farm : farmList) {
                                    farmMap.put(farm.getId(), farm);
                                }
                            }
                            for (Long roleId : searchList) {
                                Define.p_farm_search_role.Builder searchInfo = buildSearchInfo(humanObj, humanMap,farmMap,roleId, StolenVo.listFromJsonString(farm2.getStealList()));
                                if(searchInfo == null){
                                    continue;
                                }
                                msg.addSearchList(searchInfo);
                            }
                            humanObj.sendMsg(msg);
                        }
                    });
                }
            });
        });
    }


    /*******************家园科技分割线****************************************/

    public void initScience(HumanObject humanObj) {
        if(humanObj.operation.mine == null){
            return;
        }
        humanObj.operation.scienceMap = Utils.jsonToMapIntInt(humanObj.operation.mine.getScienceMap());
        humanObj.operation.sciencePassiveMap = Utils.jsonToMapIntInt(humanObj.getHuman2().getSciencePassiveMap());
    }

    /**
     * 科技信息
     */
    public void handleScienceInfoC2S(HumanObject humanObj) {
        Mine mine = humanObj.operation.mine;
        if(mine == null){
            return;
        }

        MsgScience.science_info_s2c.Builder msg = MsgScience.science_info_s2c.newBuilder();
        Define.p_science_tree.Builder science = Define.p_science_tree.newBuilder();
        int doing = mine.getScienceUpgrading();
        science.setType(1);
        science.setDoing(doing);
        science.setEtime(mine.getScienceEndTime());
        for (Map.Entry<Integer, Integer> entry : humanObj.operation.scienceMap.entrySet()) {
            if(entry.getValue().equals(0)){
                continue;
            }
            Define.p_science.Builder scienceBuilder = Define.p_science.newBuilder();
            scienceBuilder.setScienceId(entry.getKey());
            scienceBuilder.setScienceLv(entry.getValue());
            science.addScienceList(scienceBuilder);
        }
        msg.addScienceInfo(science);
        humanObj.sendMsg(msg);
    }

    /**
     * 家园科技研究
     * @param scienceId 科技ID
     * @param scienceLv 科技等级
     */
    public void handleScienceResearchC2S(HumanObject humanObj, int scienceId, int scienceLv) {
        Mine mine = humanObj.operation.mine;
        if(mine == null){
            humanObj.sendMsg(MsgScience.science_info_s2c.newBuilder().addScienceInfo(Define.p_science_tree.newBuilder().setType(1).setDoing(0).setEtime(0)));
            return;
        }
        if(mine.getScienceUpgrading() != 0){
            return;
        }
        ConfScience_0 confScience_0 = ConfScience_0.get(scienceId,scienceLv);
        if(confScience_0 == null){
            return;
        }
        Map<Integer,Integer> scienceMap = humanObj.operation.scienceMap;
        if(scienceMap.getOrDefault(scienceId,0)+1 != scienceLv){
            return;
        }
        //前置判断
        int[][] condition = confScience_0.condition;
        if(condition != null){
            for (int i = 0; i < condition.length; i++) {
                if(scienceMap.getOrDefault(condition[i][0],0) < condition[i][1]){
                    return;
                }
            }
        }
        ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj,confScience_0.cost[0],confScience_0.cost[1],MoneyItemLogKey.科技升级);
        if(!result.success){
            return;
        }
        mine.setScienceUpgrading(scienceId);
        long now = Port.getTime();
        int scienceAttr = humanObj.getPropPlus().getBigDecimal(PropKey.science_speed.getAttributeSn()).intValue();
        int scienceTime = (int)(confScience_0.time/(1+scienceAttr/10000.0));
        mine.setScienceEndTime((int)(now/Time.SEC) + scienceTime);
        mine.update();
        humanObj.startCheckScience(scienceTime * Time.SEC);

        ActivityManager.inst().addActivityProgress(humanObj,
                TaskConditionTypeKey.TASK_TYPE_6,confScience_0.cost[1],confScience_0.cost[0],confScience_0.cost[1]);

        MsgScience.science_update_s2c.Builder msg = MsgScience.science_update_s2c.newBuilder();
        Define.p_science_tree.Builder scienceTree = Define.p_science_tree.newBuilder();
        scienceTree.setType(confScience_0.type);
        scienceTree.setDoing(scienceId);
        scienceTree.setEtime(mine.getScienceEndTime());
        if(scienceLv-1 != 0){
            Define.p_science.Builder scienceBuilder = Define.p_science.newBuilder();
            scienceBuilder.setScienceId(scienceId);
            scienceBuilder.setScienceLv(scienceLv-1);
            scienceTree.addScienceList(scienceBuilder);
        }
        msg.addUpdateList(scienceTree);
        humanObj.sendMsg(msg);
    }

    public void scienceFinish(HumanObject humanObj) {
        Mine mine = humanObj.operation.mine;
        int scienceId = mine.getScienceUpgrading();
        if (scienceId == 0) {
            return;
        }
        ConfScience_0 confScience_0 = ConfScience_0.get(scienceId,1);
        Map<Integer,Integer> scienceMap = humanObj.operation.scienceMap;
        int scienceLv = scienceMap.getOrDefault(scienceId,0) + 1;
        scienceMap.put(scienceId,scienceLv);
        mine.setScienceMap(Utils.mapIntIntToJSON(scienceMap));
        mine.setScienceUpgrading(0);
        mine.setScienceEndTime(0);
        mine.update();
        HumanManager.inst().addPowerPar(humanObj, EModule.Science, confScience_0.power);
        ConfGoodsRefresh confGoodsRefresh = ConfGoodsRefresh.get(ItemConstants.AUTO_MINE);
        int oldRecoverNum = ItemManager.inst().getRefreshGoodMaxNum(humanObj, confGoodsRefresh);
        int oldRecoverTime = humanObj.getRecoverTime(ItemConstants.AUTO_MINE);
        updateSciencePorpCalc(humanObj);
        int newRecoverTime = humanObj.getRecoverTime(ItemConstants.AUTO_MINE);
        int newRecoverNum = ItemManager.inst().getRefreshGoodMaxNum(humanObj, confGoodsRefresh);
        if(oldRecoverTime != newRecoverTime){
            int nextTime = ItemManager.inst().getRefreshGoodNextTime(humanObj,ItemConstants.AUTO_MINE);
            if(newRecoverTime != 0){
                nextTime = nextTime-(oldRecoverTime-newRecoverTime);
                ItemManager.inst().setRefreshGoodNextTime(humanObj,ItemConstants.AUTO_MINE,nextTime);
                MsgHome.home_mine_update_recover_s2c.Builder msg = MsgHome.home_mine_update_recover_s2c.newBuilder();
                msg.setMaxNum(ItemManager.inst().getRefreshGoodMaxNum(humanObj,confGoodsRefresh));
                msg.setNextTime(nextTime);
                humanObj.sendMsg(msg);
                humanObj.periodCheckerSpeedUp(PeriodKey.periodCheckItemRecover,(newRecoverTime-oldRecoverTime)*Time.SEC, new Param("sn", ItemConstants.AUTO_MINE));
            }
        }
        if(newRecoverNum != oldRecoverNum){
            MsgHome.home_mine_update_recover_s2c.Builder msg = MsgHome.home_mine_update_recover_s2c.newBuilder();
            msg.setMaxNum(ItemManager.inst().getRefreshGoodMaxNum(humanObj,confGoodsRefresh));
            msg.setNextTime(ItemManager.inst().getRefreshGoodNextTime(humanObj,ItemConstants.AUTO_MINE));
            humanObj.sendMsg(msg);
            ItemManager.inst().checkAndRecoverRefreshGood(humanObj,confGoodsRefresh);
        }

        MsgScience.science_finish_s2c.Builder msg = MsgScience.science_finish_s2c.newBuilder();
        msg.setScienceId(scienceId);
        msg.setScienceLv(scienceLv);
        MsgScience.science_update_s2c.Builder msgUpdate = MsgScience.science_update_s2c.newBuilder();
        Define.p_science_tree.Builder scienceTree = Define.p_science_tree.newBuilder();
        scienceTree.setType(confScience_0.type);
        scienceTree.setDoing(0);
        scienceTree.setEtime(0);
        if(scienceLv != 0){
            Define.p_science.Builder scienceBuilder = Define.p_science.newBuilder();
            scienceBuilder.setScienceId(scienceId);
            scienceBuilder.setScienceLv(scienceLv);
            scienceTree.addScienceList(scienceBuilder);
        }
        msgUpdate.addUpdateList(scienceTree);
        humanObj.sendMsg(msg);
        humanObj.sendMsg(msgUpdate);

        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_回归任务, TaskConditionTypeKey.TASK_TYPE_52, 1);
        ActivityManager.inst().addActivityProgress(humanObj,TaskConditionTypeKey.TASK_TYPE_52,1,1);
        Event.fire(EventKey.UPGRADE_FINISH, "type", HumanManager.Upgrade_Finish_Science, "humanId", humanObj.getHuman().getId(), "guildId", humanObj.getHuman2().getGuildId(), "subType", confScience_0.type);
    }

    public void scienceSpeedUp(HumanObject humanObj, int speedUpTime) {
        Mine mine = humanObj.operation.mine;
        if(mine == null){
            Log.farm.error("scienceSpeedUp:mine is null humanId={}",humanObj.id);
            return;
        }
        if(mine.getScienceUpgrading() == 0){
            return;
        }
        mine.setScienceEndTime(mine.getScienceEndTime() - speedUpTime);
        mine.update();
        if(mine.getScienceEndTime() > (int)(Port.getTime()/Time.SEC)){
            sendScienceUpdate(humanObj,mine.getScienceUpgrading(),humanObj.operation.scienceMap.getOrDefault(mine.getScienceUpgrading(),0),mine.getScienceEndTime());
        }
        humanObj.periodCheckerSpeedUp(PeriodKey.periodCheckScience, speedUpTime*Time.SEC, null);
    }

    private void sendScienceUpdate(HumanObject humanObj, int scienceId, int scienceLv,int scienceEndTime) {
        MsgScience.science_update_s2c.Builder msg = MsgScience.science_update_s2c.newBuilder();
        Define.p_science_tree.Builder scienceTree = Define.p_science_tree.newBuilder();
        ConfScience_0 confScience_0 = ConfScience_0.get(scienceId,1);
        scienceTree.setType(confScience_0.type);
        scienceTree.setDoing(scienceId);
        scienceTree.setEtime(scienceEndTime);
        if(scienceLv!=0){
            Define.p_science.Builder scienceBuilder = Define.p_science.newBuilder();
            scienceBuilder.setScienceId(scienceId);
            scienceBuilder.setScienceLv(scienceLv);
            scienceTree.addScienceList(scienceBuilder);
        }
        msg.addUpdateList(scienceTree);
        humanObj.sendMsg(msg);
    }

    /**
     * 登入科技完成
     */
    public void onLoginScienceFinish(HumanObject humanObj) {
        Mine mine = humanObj.operation.mine;
        if (mine == null) {
            return;
        }
        if (mine.getScienceEndTime() == 0) {
            return;
        }
        Long now = Port.getTime();
        long endTime = mine.getScienceEndTime() * Time.SEC;
        if (endTime < now) {
            scienceFinish(humanObj);
        }else {
            humanObj.startCheckScience(endTime - now);
        }
    }

    /*******************家园雕像分割线****************************************/

    public void handleFarmStatueInfoC2S(HumanObject humanObj) {
        handleFarmStatueInfoC2S(humanObj, 0);
    }
    /**
     * 家园农场雕像信息
     */
    public void handleFarmStatueInfoC2S(HumanObject humanObj, int tab) {
        FarmData farmData = humanObj.farmData;
        if(farmData.farm2 == null){
            return;
        }
        MsgHome.home_farm_statue_info_s2c.Builder msg = MsgHome.home_farm_statue_info_s2c.newBuilder();
        msg.setLevel(farmData.farm2.getStatueLevel());
        msg.setExp(farmData.farm2.getStatueExp());
        msg.setTab(farmData.farm2.getTab());
        msg.setShowTab(tab == 0 ? farmData.farm2.getShowTab() : tab);
        Map<Integer, StatueTabVo> farmStatueMap = farmData.tabMap;
        for (Map.Entry<Integer, StatueTabVo> entry : farmStatueMap.entrySet()) {
            Define.p_farm_statue_tab.Builder tabBuilid = entry.getValue().build(entry.getKey()).toBuilder();
            msg.addTabList(tabBuilid);
        }
        humanObj.sendMsg(msg);
    }

    /**
     * 家园农场雕像刷新
     *
     * @param tab 雕像标签
     */
    public void handleFarmStatueRefreshC2S(HumanObject humanObj, int tab) {
        FarmData farmData = humanObj.farmData;
        if(farmData.farm2 == null){
            return;
        }
        StatueTabVo statueTabVo = farmData.tabMap.get(tab);
        if(statueTabVo == null){
            if(!isTabOpen(tab,farmData.farm2.getStatueLevel())){
                return;
            }
            statueTabVo = new StatueTabVo();
            farmData.tabMap.put(tab,statueTabVo);
        }

        //消耗不够
        int lockStatusNum = statueTabVo.getLockStatueNum();
        ConfStatueSpend confStatueSpend = ConfStatueSpend.get(lockStatusNum);
        if(confStatueSpend == null){
            Log.farm.error("confStatueSpend is null lockStatusNum={}",lockStatusNum);
            return;
        }
        ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj,confStatueSpend.spend[0],confStatueSpend.spend[1],MoneyItemLogKey.雕像刷新);
        if(!result.success){
            return;
        }
        ActivityManager.inst().addActivityProgress(humanObj,
                TaskConditionTypeKey.TASK_TYPE_6,confStatueSpend.spend[1],confStatueSpend.spend[0],confStatueSpend.spend[1]);

        //刷新雕像
        for (StatueVo statueVo : statueTabVo.statueVoList) {
            if(statueVo.isLock != EHomeType.BUFF_UNLOCK){
                continue;
            }
            ConfStatueAttr confStatueAttr = getRandomConfStatueAttr(farmData.farm2.getStatueLevel());
            statueVo.attrId = confStatueAttr.attr_id;
            statueVo.quality = confStatueAttr.product;
            statueVo.value = Utils.random(confStatueAttr.value[0],confStatueAttr.value[1]);

            Event.fire(EventKey.UPDATE_NEWS, "humanObj", humanObj, "newsCondition", NewsConditionTypeKey.StatueQuality, "value", statueVo.quality);
        }
        farmData.farm2.setTabMap(farmData.tabMapToJsonString());

        //雕像升级
        int addExp = confStatueSpend.spend[1] * ConfGlobal.get(ConfGlobalKey.statue_spend_experience.SN).value;
        int statueExp = farmData.farm2.getStatueExp() + addExp;
        int statueLevel = farmData.farm2.getStatueLevel();
        ConfStatueLevel confStatueLevelNext = ConfStatueLevel.get(statueLevel+1);
        boolean isUp = false;
        while (confStatueLevelNext != null &&statueExp >= confStatueLevelNext.expend){
            statueLevel++;
            confStatueLevelNext = ConfStatueLevel.get(statueLevel+1);
            isUp = true;
        }
        farmData.farm2.setStatueLevel(statueLevel);
        farmData.farm2.setStatueExp(statueExp);
        //解锁新的标签，解锁新的属性
        if(isUp){
            ConfStatueLevel confStatueLevel = ConfStatueLevel.get(statueLevel);
            HumanManager.inst().updatePowerPar(humanObj, EModule.Statue, confStatueLevel.power);
            updateStatuePorpCalc(humanObj);
            unlockFarmStatueTab(humanObj);

            ActivityManager.inst().addActivityProgress(humanObj,TaskConditionTypeKey.TASK_TYPE_54,0);
        }
        ActivityManager.inst().addActivityProgress(humanObj,TaskConditionTypeKey.TASK_TYPE_55,0);

        handleFarmStatueInfoC2S(humanObj, tab);

        if(tab == farmData.farm2.getTab()){
            updateStatuePorpCalc(humanObj);
        }
        farmData.farm2.update();
        humanObj.getHuman3().setTotalStatueNum(humanObj.getHuman3().getTotalStatueNum() + 1);
        humanObj.getHuman3().update();
        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_28, 1,1);
    }

    private ConfStatueAttr getRandomConfStatueAttr(int statueLevel){
        int index = Utils.randomByWeight2D(ConfStatueLevel.get(statueLevel).proQuality,1);
        int quality = ConfStatueLevel.get(statueLevel).proQuality[index][0];
        List<ConfStatueAttr> confStatueAttrList = GlobalConfVal.getConfStatueAttrs(quality);
        int index1 = Utils.randomByWeight(confStatueAttrList.stream().mapToInt(conf -> conf.pro).toArray());
        return confStatueAttrList.get(index1);
    }

    private boolean isTabOpen(int tab, int statueLv){
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.statue_tab_limit.SN);
        String[] tabLimit = confGlobal.strValue.split("\\|");
        for (String tabStr : tabLimit) {
            String[] tabArr = tabStr.split(",");
            if(Utils.intValue(tabArr[0]) == tab && statueLv >= Integer.parseInt(tabArr[1])){
                return true;
            }
        }
        return false;
    }

    public void unlockFarmStatueTab(HumanObject humanObj){
        FarmData farmData = humanObj.farmData;
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.statue_tab_limit.SN);
        String[] tabLimit = confGlobal.strValue.split("\\|");
        for (String tabStr : tabLimit) {
            String[] tabArr = tabStr.split(",");
            int tab = Utils.intValue(tabArr[0]);
            int statueLv = Integer.parseInt(tabArr[1]);
            if(!farmData.tabMap.containsKey(tab)&&statueLv <= farmData.farm2.getStatueLevel()){
                StatueTabVo statueTabVo = new StatueTabVo();
                farmData.tabMap.put(tab,statueTabVo);
            }
        }
        unlockFarmStatueAttr(farmData);
        farmData.farm2.setTabMap(farmData.tabMapToJsonString());
    }

    private void unlockFarmStatueAttr(FarmData farmData){
        for (Map.Entry<Integer, StatueTabVo> entry : farmData.tabMap.entrySet()) {
            StatueTabVo statueTabVo = entry.getValue();
            Collection<ConfStatuePos> confStatuePosList = ConfStatuePos.findAll();
            if(statueTabVo.statueVoList.size() == confStatuePosList.size()){
                continue;
            }
            for (ConfStatuePos confStatuePos : confStatuePosList) {
                if(confStatuePos.level <= farmData.farm2.getStatueLevel()){
                    if(statueTabVo.statueVoList.stream().noneMatch(statueVo -> statueVo.pos == confStatuePos.sn)){
                        StatueVo statueVo = new StatueVo();
                        statueVo.pos = confStatuePos.sn;
                        statueVo.isLock = EHomeType.LEVEL_UNLOCKABLE;
                        statueTabVo.statueVoList.add(statueVo);
                    }
                }
            }
        }
    }

    /**
     * 家园农场雕像属性解锁
     * @param pos 属性位置
     * @param tab 雕像标签
     */
    public void handleFarmStatueAttrLockC2S(HumanObject humanObj, int pos, int tab) {
        FarmData farmData = humanObj.farmData;
        if(farmData.farm2 == null){
            return;
        }
        StatueTabVo statueTabVo = farmData.tabMap.get(tab);
        if(statueTabVo == null){
            if(!isTabOpen(tab,farmData.farm2.getStatueLevel())){
                return;
            }
            statueTabVo = new StatueTabVo();
            farmData.tabMap.put(tab,statueTabVo);
        }
        StatueVo statueVo = statueTabVo.statueVoList.stream().filter(statueVo1 -> statueVo1.pos == pos).findFirst().orElse(null);
        if(statueVo == null){
            ConfStatuePos confStatuePos = ConfStatuePos.get(pos);
            if(confStatuePos == null){
                return;
            }
            if(ConfStatuePos.get(pos).level > farmData.farm2.getStatueLevel()){
                return;
            }
            statueVo = new StatueVo();
            statueTabVo.statueVoList.add(statueVo);
        }
        if(statueVo.isLock == EHomeType.LEVEL_UNLOCKABLE){
            //激活词条随机一条属性
            ConfStatueAttr confStatueAttr = getRandomConfStatueAttr(farmData.farm2.getStatueLevel());
            statueVo.attrId = confStatueAttr.attr_id;
            statueVo.quality = confStatueAttr.product;
            statueVo.value = Utils.random(confStatueAttr.value[0],confStatueAttr.value[1]);
            if(tab == farmData.farm2.getTab()){
                updateStatuePorpCalc(humanObj);
            }
        }
        statueVo.isLock = statueVo.isLock != EHomeType.BUFF_UNLOCK ? EHomeType.BUFF_UNLOCK : EHomeType.BUFF_LOCK;
        farmData.farm2.setTabMap(farmData.tabMapToJsonString());
        farmData.farm2.update();
        MsgHome.home_farm_statue_attr_lock_s2c.Builder msg = MsgHome.home_farm_statue_attr_lock_s2c.newBuilder();
        msg.setAttr(statueVo.build().toBuilder());
        msg.setTab(tab);
        humanObj.sendMsg(msg);

        Event.fire(EventKey.UPDATE_NEWS, "humanObj", humanObj, "newsCondition", NewsConditionTypeKey.StatueQuality, "value", statueVo.quality);
    }

    /**
     * 家园农场雕像标签使用
     *
     * @param tab 雕像标签
     */
    public void handleFarmStatueTabUseC2S(HumanObject humanObj, int tab) {
        FarmData farmData = humanObj.farmData;
        if(farmData.farm2 == null){
            return;
        }
        if(farmData.farm2.getTab() == tab){
            return;
        }
        farmData.farm2.setTab(tab);
        farmData.farm2.setShowTab(tab);
        farmData.farm2.update();
        updateStatuePorpCalc(humanObj);
        MsgHome.home_farm_statue_tab_use_s2c.Builder msg = MsgHome.home_farm_statue_tab_use_s2c.newBuilder();
        msg.setTab(tab);
        humanObj.sendMsg(msg);
    }

    /**
     * 家园农场雕像标签改名
     * @param tab  雕像标签
     * @param name 新标签名称
     */
    public void handleFarmStatueTabChangeNameC2S(HumanObject humanObj, int tab, String name) {
        FarmData farmData = humanObj.farmData;
        if(farmData.farm2 == null){
            return;
        }
        StatueTabVo statueTabVo = farmData.tabMap.get(tab);
        if(statueTabVo == null){
            return;
        }
        statueTabVo.name = name;
        farmData.farm2.setTabMap(farmData.tabMapToJsonString());
        farmData.farm2.update();
        MsgHome.home_farm_statue_tab_change_name_s2c.Builder msg = MsgHome.home_farm_statue_tab_change_name_s2c.newBuilder();
        msg.setTab(tab);
        msg.setName(name);
        humanObj.sendMsg(msg);
    }
    public void updateBuildPorpCalc(HumanObject humanObj){
        Map<Integer, BuildVo> buildingMap = BuildVo.mapFromJsonString(humanObj.farmData.farm.getBuildMap());
        PropCalc propCalc = new PropCalc();
        long power = 0;
        for (BuildVo vo : buildingMap.values()) {
            ConfFarmBuildings_0 conf = ConfFarmBuildings_0.get(vo.cfgId, vo.level);
            if(conf == null || conf.effect == null){
                continue;
            }
            propCalc.plus(conf.effect);
            power += conf.power;
            if(conf.pvp_effect != null && conf.pvp_effect.length > 0){
                propCalc.plus(conf.pvp_effect);
            }
            if(conf.pvp_effect1 != null && conf.pvp_effect1.length > 0){
                propCalc.plus(conf.pvp_effect1);
            }
        }
        HumanManager.inst().updatePowerPar(humanObj, EModule.build, power);
        humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.build, propCalc.toJSONStr());

        UnitManager.inst().propCalc(humanObj, CombatChangeLog.建筑);
    }

    public void updateStatuePorpCalc(HumanObject humanObj){
        PropCalc propCalc = new PropCalc();
        Farm2 farm2 = humanObj.farmData.farm2;
        if(farm2 == null){
            return;
        }
        Map<Integer, StatueTabVo> farmStatueMap = humanObj.farmData.tabMap;
        StatueTabVo statueTabVo = farmStatueMap.get(farm2.getTab());
        if(statueTabVo == null){
            return;
        }
        for (StatueVo statueVo : statueTabVo.statueVoList) {
            propCalc.plus(statueVo.attrId, BigDecimal.valueOf(statueVo.value));
        }
        humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.statue, propCalc.toJSONStr());
        PropManager.inst().propCalc(humanObj, CombatChangeLog.雕像);
    }

    public PropCalc getStatuePropCalc(HumanObject humanObj, int tab){
        PropCalc propCalc = new PropCalc();
        Farm2 farm2 = humanObj.farmData.farm2;
        if(farm2 == null){
            return propCalc;
        }
        Map<Integer, StatueTabVo> farmStatueMap = humanObj.farmData.tabMap;
        StatueTabVo statueTabVo = farmStatueMap.get(tab);
        if(statueTabVo == null){
            return propCalc;
        }
        for (StatueVo statueVo : statueTabVo.statueVoList) {
            if(statueVo.attrId != 0){
                propCalc.plus(statueVo.attrId, BigDecimal.valueOf(statueVo.value));
            }
        }
        return propCalc;
    }

    public void updateSciencePorpCalc(HumanObject humanObj){
        PropCalc propCalc = new PropCalc();
        Map<Integer, Integer> scienceMap = humanObj.operation.scienceMap;
        Map<Integer, Integer> skillAddLvMap = new HashMap<>();
        for (Map.Entry<Integer, Integer> entry : scienceMap.entrySet()) {
            ConfScience_0 confScience_0 = ConfScience_0.get(entry.getKey(),entry.getValue());
            if(confScience_0 == null){
                continue;
            }
            if(confScience_0.skill != null && confScience_0.skill.length % 2 == 0){
                for(int i = 0; i < confScience_0.skill.length; i+=2){
                    skillAddLvMap.put(confScience_0.skill[i], skillAddLvMap.getOrDefault(confScience_0.skill[i],0) + confScience_0.skill[i + 1]);
                }
            }
            if(confScience_0.attr== null || confScience_0.attr.length < 2){
                continue;
            }
            if(confScience_0.attr == null || confScience_0.attr.length < 2){
                Log.temp.error("===ConfScience_0配表错误， sn= {},{}", entry.getKey(), entry.getValue());
                continue;
            }
            propCalc.plus(confScience_0.attr[0], BigDecimal.valueOf(confScience_0.attr[1]));
        }
        if(!skillAddLvMap.isEmpty()){
            String jsonNew = Utils.mapIntIntToJSON(skillAddLvMap);
            Mine mine = humanObj.operation.mine;
            PropCalc propCalcSkill = new PropCalc();
            if(!jsonNew.equals(humanObj.operation.human2.getSciencePassiveMap()) || humanObj.operation.sciencePassiveMap.size() != skillAddLvMap.size()){
                List<Define.p_passive_skill> updateList = new ArrayList<>();
                // 科技被动
                for (Map.Entry<Integer, Integer> entry : skillAddLvMap.entrySet()) {
                    updateList.add(SkillManager.inst().to_p_passive_skill(entry.getKey(), entry.getValue()));
                    ConfSkillLevel_0 confSkillLevel0  = ConfSkillLevel_0.get(entry.getKey(), entry.getValue());
                    if (confSkillLevel0 == null) {
                        Log.farm.error("找不到ConfSkillLevel_0配表，id={}，lv={}", entry.getKey(), entry.getValue());
                        continue;
                    }
                    if(confSkillLevel0.attrType == null || confSkillLevel0.attrType[0] != 1){
                        propCalc.plus(confSkillLevel0.ownEffect);
                    }
                }
                SkillManager.inst().sendMsg_skill_passive_update_s2c(humanObj, updateList, null);
            }
            humanObj.operation.human2.setSciencePassiveMap(jsonNew);
            humanObj.operation.human2.update();
            humanObj.operation.sciencePassiveMap = skillAddLvMap;
            propCalc.plus(propCalcSkill);
        }

        humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.science, propCalc.toJSONStr());
        PropManager.inst().propCalc(humanObj, CombatChangeLog.科技);
    }

    @Listener(EventKey.HUMAN_LOGIN)
    public void onHumanLogin(Param param) {
        HumanObject humanObj = param.get("humanObj");
        onLoginScienceFinish(humanObj);
        if(humanObj.farmData.farm2 != null && Utils.isEmptyJSONString(humanObj.farmData.farm2.getTabMap())){
            HomeManager.inst().unlockFarmStatueTab(humanObj);
            humanObj.farmData.farm2.update();
            Log.game.error("onHumanLogin farm2 tabMap is null, humanObjId={}", humanObj.getHuman().getId());
        }
        if(humanObj.farmData.farm != null){
            checkAndSetLandTimeout(humanObj,humanObj.farmData.farm);
            checkAndSetBuildingTimeout(humanObj.farmData.farm);
        }
    }


    /**
     * 庄园功能解锁
     */
    @Listener(EventKey.FUNCTION_OPEN)
    public void unlock(Param params) {
        HumanObject humanObj = params.get("humanObj");
        if(humanObj.operation.mine == null && humanObj.isModUnlock(7)){
            createMine(humanObj);
            handleScienceInfoC2S(humanObj);
        }
        if(humanObj.farmData.farm2 != null){
            return;
        }
        if(!humanObj.isModUnlock(41)&&!humanObj.isModUnlock(42)){
            return;
        }
        Farm farm = new Farm();
        Farm2 farm2 = new Farm2();
        farm.setId(humanObj.id);
        farm2.setId(humanObj.id);
        Collection<ConfFarmPos> confFarmPosList = ConfFarmPos.findAll();
        for (ConfFarmPos confFarmPos : confFarmPosList) {
            if(confFarmPos.condition == null || confFarmPos.condition.length == 0){
                LandVo landVo = new LandVo();
                landVo.landId = confFarmPos.sn;
                setLand(farm,confFarmPos.sn,landVo.toJsonString());
            }
        }
        Map<Integer,BuildVo> buildVoMap = new HashMap<>();
        for(int i = 1; i <= 3; ++i){
            BuildVo buildVo = new BuildVo();
            buildVo.cfgId = i;
            buildVoMap.put(i,buildVo);
        }
        farm.setBuildMap(BuildVo.mapToJsonString(buildVoMap));
        farm.setLevel(1);
        farm2.setStatueLevel(1);
        farm.persist();
        farm2.persist();
        unlockVipLand(humanObj);
        humanObj.farmData.farm = farm;
        humanObj.farmData.farm2 = farm2;
        ConfStatueLevel confStatueLevel = ConfStatueLevel.get(1);
        HumanManager.inst().updatePowerPar(humanObj, EModule.Statue, confStatueLevel.power);
        unlockFarmStatueTab(humanObj);
        farm2.update();

        humanObj.getHuman().setFarmState(EHomeType.FARM_OPEN);
        sendFarmData(humanObj,farm, humanObj.id);
    }

    public void setLand(Farm farm, int sn, String jsonString){
        switch (sn) {
            case 1:
                farm.setLand1(jsonString);
                break;
            case 2:
                farm.setLand2(jsonString);
                break;
            case 3:
                farm.setLand3(jsonString);
                break;
            case 4:
                farm.setLand4(jsonString);
                break;
            case 5:
                farm.setLand5(jsonString);
                break;
            case 6:
                farm.setLand6(jsonString);
                break;
            default:
                Log.farm.error("EquipManager.setLand: sn is error, sn={}", sn);
                break;
        }
    }

    public String getLand(Farm farm, int sn) {
        switch (sn) {
            case 1:
                return farm.getLand1();
            case 2:
                return farm.getLand2();
            case 3:
                return farm.getLand3();
            case 4:
                return farm.getLand4();
            case 5:
                return farm.getLand5();
            case 6:
                return farm.getLand6();
            default:
                Log.farm.error("HomeManager.getLand: sn is error, sn={}", sn);
                return null;
        }
    }


    public void setBuilding(Farm farm, BuildVo buildVo) {
        Map<Integer,BuildVo> buildVoMap = BuildVo.mapFromJsonString(farm.getBuildMap());
        buildVoMap.put(buildVo.cfgId,buildVo);
        farm.setBuildMap(BuildVo.mapToJsonString(buildVoMap));
    }

    /**
     * 菜地检查
     */
    private void checkAndSetLandTimeout(HumanObject humanObj,Farm farm) {
        List<Integer> landIds = new ArrayList<>();
        for (int i = 1; i <= 6; ++i) {
            LandVo landVo = new LandVo(getLand(farm,i));
            if (landVo.state == EHomeType.GROWING || landVo.state == EHomeType.STEALING) {
                if (landVo.endTime < (int)(Port.getTime()/Time.SEC)- landVo.accTime) {
                    landIds.add(i);
                }
            }
        }
        if(landIds.size() > 0){
            FarmServiceProxy proxy = FarmServiceProxy.newInstance();
            proxy.farmPlantUpdate(farm,landIds);
            proxy.listenResult(this::_result_farmPlantUpdate,"humanObj",humanObj);
        }
    }

    /**
     * 建筑检查
     */
    private void checkAndSetBuildingTimeout(Farm farm) {
        Map<Integer,BuildVo> buildVoMap = BuildVo.mapFromJsonString(farm.getBuildMap());
        for (Map.Entry<Integer, BuildVo> entry : buildVoMap.entrySet()) {
            BuildVo buildVo = entry.getValue();
            if(buildVo.startTime > 0){
                FarmServiceProxy proxy = FarmServiceProxy.newInstance();
                proxy.farmBuildUpdate(farm.getId(),buildVo);
            }
        }
    }

    /**
     * 偷菜检查
     */
    private void checkAndSetStealTimeout(Farm2 farm2) {
        List<StolenVo> stealList = StolenVo.listFromJsonString(farm2.getStealList());
        stealList.removeIf(stolenVo -> stolenVo.endTime < (int) (Port.getTime() / Time.SEC));
    }

    public PropCalc getPvpEffect(Farm farm, int type) {
        PropCalc propCalc = new PropCalc();
        Map<Integer,BuildVo> buildVoMap = BuildVo.mapFromJsonString(farm.getBuildMap());
        for (Map.Entry<Integer, BuildVo> entry : buildVoMap.entrySet()) {
            BuildVo buildVo = entry.getValue();
            ConfFarmBuildings_0 confFarmBuildings_0 = ConfFarmBuildings_0.get(buildVo.cfgId,buildVo.level);
            if(type == 0){
                if(confFarmBuildings_0 == null || confFarmBuildings_0.pvp_effect == null || confFarmBuildings_0.pvp_effect.length == 0){
                    continue;
                }
                propCalc.plus(confFarmBuildings_0.pvp_effect);
            }else {
                if(confFarmBuildings_0 == null || confFarmBuildings_0.pvp_effect1 == null || confFarmBuildings_0.pvp_effect1.length == 0){
                    continue;
                }
                propCalc.plus(confFarmBuildings_0.pvp_effect1);
            }
        }
        return propCalc;
    }

    public ReasonResult battleCheckAndSet(LandVo landVo) {
        ReasonResult result = new ReasonResult(true);
        if(landVo.state != EHomeType.STEALING){
            result.success = false;
            return result;
        }
        landVo.state = EHomeType.MATURE;
        landVo.startTime = 0;
        landVo.endTime = 0;
        landVo.robberInfoMap.remove(landVo.robber.id);
        landVo.robber = new RobberVo();
        return result;
    }

    public void farmReportFix(HumanObject humanObj){
        String removeKey = RedisKeys.farmBattleHistoryList+humanObj.id;
        RedisTools.del(EntityManager.getRedisClient(), removeKey);
    }

    public void handleFarmSpendFruitC2S(HumanObject humanObj, int spendNum) {
        FarmData farmData = humanObj.farmData;
        if(farmData.farm2 == null){
            Log.farm.error("farmData.farm2 is null humanObj.id={}",humanObj.id);
            return;
        }
        ConfGlobal confGlobal = ConfGlobal.get("statue_multiple_spend");
        if(confGlobal == null){
            Log.farm.error("confGlobal is null statue_multiple_spend");
            return;
        }
        if(farmData.farm2.getStatueLevel() < confGlobal.intArray[0]){
            Log.farm.error("farmData.farm2.getStatueLevel() >= confGlobal.value humanObj.id={} statueLevel={}",humanObj.id,farmData.farm2.getStatueLevel());
            return;
        }
        ConfStatueSpend confStatueSpend = ConfStatueSpend.get(1);
        if(confStatueSpend == null){
            Log.farm.error("confStatueSpend is null lockStatusNum={}",1);
            return;
        }
        ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj, confStatueSpend.spend[0], spendNum, MoneyItemLogKey.雕像消耗);
        if(!result.success){
            return;
        }
        //雕像升级
        int addExp = spendNum * ConfGlobal.get(ConfGlobalKey.statue_spend_experience.SN).value;
        int statueExp = farmData.farm2.getStatueExp() + addExp;
        int statueLevel = farmData.farm2.getStatueLevel();
        ConfStatueLevel confStatueLevelNext = ConfStatueLevel.get(statueLevel+1);
        while (confStatueLevelNext != null &&statueExp >= confStatueLevelNext.expend){
            statueLevel++;
            confStatueLevelNext = ConfStatueLevel.get(statueLevel+1);
        }
        farmData.farm2.setStatueLevel(statueLevel);
        farmData.farm2.setStatueExp(statueExp);
        handleFarmStatueInfoC2S(humanObj, 0);
        ActivityManager.inst().addActivityProgress(humanObj,TaskConditionTypeKey.TASK_TYPE_6,spendNum,confStatueSpend.spend[0],spendNum);
    }
}
