package org.gof.demo.worldsrv.support;

import org.gof.core.support.S;
import org.gof.core.support.Utils;

import java.util.Properties;

/**
 * 节点配置
 */
public class D {

	//配置文件名称
	private static final String CONFIG_NAME = "gofDistr.properties";
	private static final String CROSS_CONFIG_NAME = "crossDistr.properties";
	private static final String ADMIN_CONFIG_NAME = "adminDistr.properties";

	//前缀
	public static final String NODE_WORLD_PREFIX = "world";			//游戏服务器Node前缀
	public static final String NODE_WORLD_BRIDGE_PREFIX = "worldBridge";	//游戏连接跨服服务器Node前缀

	public static final String PORT_PLATFORM_LOGIN_PREFIX = "login";	//平台登陆业务Port前缀

	public static final String PORT_GAME_PREFIX = "game";				//游戏业务Port前缀
	public static final String PORT_BRIDGE_GAME_PREFIX ="bridgeGame"; 	//游戏业务Port前缀

	public static final String PORT_ADMIN_GAME_PREFIX ="adminGame"; 	//游戏业务Port前缀

	public static final String PORT_BRIDGE_LEAGUE_GAME_PREFIX ="bridgeLeagueGame"; 	//游戏业务Port前缀

	//NODE
	public static final String NODE_PLATFORM = "platform";				//平台NodeId

	//服务
	public static final String SERV_PLATFORM_LOGIN = "login";			//平台登陆验证服务ID

	public static final String SERV_TEST = "test";
	public static final String SERV_HUMAN_CREATE_APPLY = "humanCreateApply";	//玩家创建申请服务
	public static final String SERV_GAME_VALUE = "gameValue";			//全局数据信息
	public static final String SERV_STAGE_GLOBAL = "stageGlobal";		//全局地图信息服务
	public static final String SERV_HUMAN_GLOBAL = "humanGlobal";		//全局玩家信息服务

	public static final String SERV_GAME_SELECT = "serverSelect";		//全局查询服务（所有跨服和游戏服都启动）
	public static final String SERV_DATA_RESET = "dataReset";			//每日数据重置
	public static final String SERV_STAGE_DEFAULT = "stageDefault";		//地图默认服务
	public static final String SERV_POCKETLINE = "pocketLine";			//待办服务
	public static final String SERV_NAME = "name";						//名字服务
	public static final String SERV_MAIL = "mail";						//邮件服务
	public static final String SERV_CHAT = "chat";						//聊天服务
	public static final String SERV_FILL_MAIL = "fillMail";				//全服补偿邮件服务
	public static final String SERV_CONFIRM = "confirm";         		// 确认服务
	public static final String SERV_GUILD = "guild";					//公会服务
	public static final String SERV_GUILD_LEAGUE = "guildLeague";		//公会联盟战服务
	public static final String SERV_GUILD_LEAGUE_WARMUP = "guildLeagueWarmUp";		//公会联盟战本服服务

	public static final String SERV_FLY_HYBRID = "flyHybrid";			// 飞宠搭档

	public static final String SERV_HUMAN_SERVER = "humanServer";		//玩家服务

	public static final String SERV_ACCOUNT_LOGIN_SERVER = "accountLoginServer";		//玩家登录服务

	public static final String SERV_WORLD_BOSS = "worldBoss";			//世界boss服务
	public static final String SERV_FARM = "farm";						//农场服务
	public static final String SERV_CAPTURE_SLAVE = "captureSlave";		//抓捕奴隶服务
	public static final String SERV_RANK = "rank";		//排行榜服务
	public static final String SERV_RANK_CROSS = "rankCross";			// 跨服排行榜
	public static final String SERV_ACTIVITY = "activity";				//活动服务
	public static final String SERV_ACTIVITY_CROSS = "activityCross";	//跨服活动服务（主要是为了活动排行榜结算）
	public static final String SERV_CAR_PARK = "carPark";		//停车服务
	public static final String SERV_CAR_PARK_CROSS = "carParkCross";		//停车服务
	public static final String SERV_ARENA = "arena";					//竞技场服务
	public static final String SERV_ARENA_CROSS = "arenaCross";					//竞技场服务
	public static final String SERV_ARENA_NEW = "arenaNew";					//竞技场服务
	public static final String SERV_ARENA_RANKED = "arenaRanked";		//竞技场排位赛服务
	public static final String SERV_CROSS = "crossWar";		//跨服战
	public static final String SERV_HTTP_PUSH = "httpPush";		//
	public static final String SERV_KUNG_FU_RACE = "kungFuRace";		//武道会
	public static final String SERV_BLACK_MARKET = "blackMarket";		// 黑市

	public static final String SERV_TEAM = "team";						//组队服
	public static final String SERV_MATCH = "match";					//匹配服
	public static final String SERV_MATCH_INFO = "matchinfo";  			// 本地匹配信息
	public static final String SERV_KEYACTIVATE = "keyActivate"; 		//激活码服务

	public static final String SERV_SERVER_LIST = "serverList";			//服务器列表
	public static final String SERV_GM_ADMIN = "gmAdmin";				// 中心服GM转发
	public static final String SERV_GM = "gm";							// 游戏服GM
	public static final String SERV_CHECK_ADMIN = "checkAdmin";			//中心服检测信息服务
	public static final String SERV_MATCH_ALLOT = "matchAllot";			//跨服玩法匹配服务
	public static final String SERV_CHECK_WORLD = "checkWorld";			//游戏服检测信息服务
	public static final String SERV_REDIS = "redis";					//redis服务
	//-- cross-new
	public static final String SERV_ADMIN_CENTER="adminCenter";			//中心服跨服管理中心
	public static final String SERV_WORLD_LOCAL="worldLocal";			//游戏服本地服务，供跨服和中心服调用

	//其他配置
	public static final int NODE_WORLD_STARTUP_PLATFORM_LOGIN;			//游戏服务实例数
	public static final int NODE_WORLD_STARTUP_NUM;						//游戏服务实例数
	public static final int PORT_GAME_STARTUP_NUM;						//游戏业务Port服务实例数
	public static final int LOGIN_MAX_ONLINE;							//最大在线人数(注意!!!:登录排队不使用这个)

	public static final String PORT_HUMAN = "humanPort";

	public static final String PORT_ACCOUNT = "accountPort";
	public static final int PORT_STARTUP_HUMAN_NUM;						//游戏服玩家实例数

	public static final int PORT_BRIDGE_GAME_STARTUP_NUM;
	public static final int NODE_BRIDGE_STARTUP_NUM;					//跨服服务实例数
	public static final int NODE_ADMIN_STARTUP_NUM;						//跨服服务实例数
	public static final int NODE_CROSS_STARTUP_NUM;						//跨服服务实例数

	public static final int PORT_BRIDGE_LEAGUE_GAME_STARTUP_NUM;
	public static final int NODE_BRIDGE_LEAGUE_STARTUP_NUM;					//跨服服务实例数

	public static final int GROUP_NUM_BRIDGE_LEAGUE;					//一个联盟服支持几个乱斗跨服组

	public static final int PORT_STARTUP_NUM_ACCOUNT_LOGIN;

	public static final String NODE_BRIDGE_PREFIX = "bridge";
	public static final String NODE_BRIDGE_DEFAULT = "bridge1";			//跨服默认Node
	public static final String NODE_BRIDGE_LEAGUE = "bridgeLeague1";			//跨服车轮战Node
	public static final String NODE_BRIDGE_ADMIN = "admin0";			//中心服node

	public static final String NODE_BRIDGE_LEAGUE_PREFIX = "bridgeLeague";	//跨服车轮战Node

	public static final String NODE_ADMIN_PREFIX = "admin";
	public static final String NODE_CROSS_PREFIX = "cross";
	public static final int PORT_ADMIN_GAME_STARTUP_NUM;


	static {
		//加载 配置文件
		Properties prop;
		String configDir = System.getProperty("gofConfigDir");
		if(configDir!=null && !configDir.isEmpty()){
			if(!configDir.endsWith("/")){
				configDir=configDir+"/";
			}
		}else{
			configDir="";
		}
		if(S.isAdmin){
			prop = Utils.readProperties(configDir+ADMIN_CONFIG_NAME);
		} else if(S.isCross){
			prop = Utils.readProperties(configDir+CROSS_CONFIG_NAME);
		} else {
			prop = Utils.readProperties(configDir+CONFIG_NAME);
		}

		NODE_WORLD_STARTUP_PLATFORM_LOGIN = Integer.parseInt(prop.getProperty("port.startup.num.platform.login","0"));
		NODE_WORLD_STARTUP_NUM = Integer.parseInt(prop.getProperty("node.startup.num.world","0"));

		PORT_STARTUP_HUMAN_NUM = Integer.parseInt(prop.getProperty("port.startup.num.human","5"));

		PORT_GAME_STARTUP_NUM = Integer.parseInt(prop.getProperty("port.startup.num.game","0"));
		LOGIN_MAX_ONLINE = Integer.parseInt(prop.getProperty("login.max.online","0"));


		PORT_BRIDGE_GAME_STARTUP_NUM = Utils.intValue(prop.getProperty("port.startup.num.bridgeGame_","0"));
		NODE_BRIDGE_STARTUP_NUM = Utils.intValue(prop.getProperty("node.startup.num.bridge"));

		PORT_ADMIN_GAME_STARTUP_NUM = Utils.intValue(prop.getProperty("port.startup.num.adminGame0_","0"));
		NODE_ADMIN_STARTUP_NUM = Utils.intValue(prop.getProperty("node.startup.num.admin"));
		NODE_CROSS_STARTUP_NUM = Utils.intValue(prop.getProperty("node.startup.num.cross"));

		PORT_BRIDGE_LEAGUE_GAME_STARTUP_NUM = Utils.intValue(prop.getProperty("port.startup.num.bridgeLeagueGame_","0"));
		NODE_BRIDGE_LEAGUE_STARTUP_NUM = Utils.intValue(prop.getProperty("node.startup.num.bridgeLeague"));

		GROUP_NUM_BRIDGE_LEAGUE = Utils.intValue(prop.getProperty("group.num.bridgeLeague", "1"));
		PORT_STARTUP_NUM_ACCOUNT_LOGIN = Utils.intValue(prop.getProperty("port.startup.num.acountLogin"));
	}
}