package org.gof.demo.worldsrv.support.enumKey;

public enum ConfGlobalKey {

    equip_sync_time("equip_sync_time"),
    Pvp_Defence_win("Pvp_Defence_win"),
    Pvp_Defence_lose("Pvp_Defence_lose"),
    legacy_team_chapter_assist_time("legacy_team_chapter_assist_time"),
    创角首次登录奖励("initial_goods"),
    默认名字规则("1"),
    角色最大等级("2"),
    默认职业("3"),
    跨服排行规则创角人数("1050"),
    跨服排行规则战力榜X名("1051"),
    跨服排行规则随机范围("1052"),

    跨服排位赛功能运行跨服集合("1053"),
    跨服乱斗功能运行跨服集合("1054"),
    跨服竞技场功能运行跨服集合("1055"),
    跨服战功能运行跨服集合("1056"),
    副本战力检测系数百分比("1060"),
    跨服战服务器开启区间("1080"),
    跨服活动服务器开启区间("1090"),
    跨服黑市服务器开启区间("1091"),
    乱斗公会排行范围("familybattle_matching_interval"),

    familybattle_initial_time("familybattle_initial_time"),
    pvp_cross_sever_time("pvp_cross_sever_time"),

    familybattle_point("familybattle_point"),


    chat_records_time("chat_records_time"),

    familybattle_defeat_Number_news("familybattle_defeat_Number_news"),

    familybattle_cross_server("familybattle_cross_server"),


    创建家族消耗("create_guild_cost"),
    家族名字长度("guild_name_length"),
    捐献消耗道具("guild_donate_cost"),
    捐献奖励("guild_donate_reward"),
    伙伴满级物品合成消耗数量("1010"),
    技能满级物品合成消耗数量("1020"),
    initial_attr("initial_attr"),
    更换会长时间("guild_change_leader_time"),
    公会申请等级限制("guild_apply_level_limit"),
    公会自动解散("guild_disband"),
    公会改名消耗("change_guild_name_cost"),
    公会旗帜改动消耗("change_guild_flag_cost"),
    //("guild_daily_donate"),


    main_attr("main_attr"),
    box_goods_id("box_goods_id"),
    accelerate_goods_effect("accelerate_goods_effect"),

    reset_job("reset_job"),
    high_attr("high_attr"),
    initial_job("initial_job"),
    transfer_job_level("transfer_job_level"),
    initial_goods("initial_goods"),
    skill_system("skill_system"),
    base_attr("base_attr"),
    advanced_attr("advanced_attr"),
    finally_attr("finally_attr"),
    show_attr("show_attr"),
    show_attr2("show_attr2"),
    equip_attr("equip_attr"),
    job_passive_skill("job_passive_skill"),
    highlight_quality("highlight_quality"),
    equip_skin("equip_skin"),
    online_reward1_time("online_reward1_time"),
    online_reward2_time("online_reward2_time"),
    offline_reward1_time("offline_reward1_time"),
    offline_reward2_time("offline_reward2_time"),
    online_reward_maxtime("online_reward_maxtime"),
    offline_reward_maxtime("offline_reward_maxtime"),
    online_reward_collecttime("online_reward_collecttime"),
    mine_template_size("mine_template_size"),
    mine_ore("mine_ore"),
    mine_pickaxe("mine_pickaxe"),
    mine_pickaxe_recover("mine_pickaxe_recover"),
    mine_drill("mine_drill"),
    mine_bomb("mine_bomb"),
    change_name_cost("change_name_cost"),
    name_limit("name_limit"),
    friend_limit("friend_limit"),
    friend_gift("friend_gift"),
    friend_gift_limit("friend_gift_limit"),
    change_gender_cost("change_gender_cost"),
    change_styling_tab("change_styling_tab"),
    default_skin("default_skin"),
    res_chapter("res_chapter"),
    chat_level("chat_level"),
    chat_cd("chat_cd"),
    chat_records_number("chat_records_number"),
    chat_word_number("chat_word_number"),
    refresh_friend_list_cd("refresh_friend_list_cd"),
    pvp_s("pvp_s"),
    pvp_k("pvp_k"),// 竞技场战斗记录
    pvp_initial_score("pvp_initial_score"),
    pvp_ticket_max("pvp_ticket_max"),
    pvp_ticket_price("pvp_ticket_price"),
    pvp_auto_add_num("pvp_auto_add_num"),
    pvp_score_change_range("pvp_score_change_range"),
    pvp_match_range("pvp_match_range"),
    pvp_season_duration("pvp_season_duration"),
    pvp_refresh_interval("pvp_refresh_interval"),
    pvp_attribute("pvp_attribute"),
    pvp_win_reward("pvp_win_reward"),
    pvp_skip_time("pvp_skip_time"),
    pay_mall_tab("pay_mall_tab"),
    pay_mall_tab_content("pay_mall_tab_content"),
    robot_skill("robot_skill"),
    robot_pet("robot_pet"),
    robot_equipment_quailty("robot_equipment_quailty"),
    equip_type("equip_type"),
    mine_grid_background("mine_grid_background"),
    equipment_level("equipment_level"),
    default_name("default_name"),
    main_blockreward("main_blockreward"),
    ads_icon_is_show("ads_icon_is_show"),
    word_stay_time("word_stay_time"),
    place_reward_time("place_reward_time"),
    task_guide_range("task_guide_range"),
    task_guide_time("task_guide_time"),
    fail_cd("fail_cd"),
    paydialogue_cd("paydialogue_cd"),
    send_time("send_time"),
    min_part_time("min_part_time"),
    box_delay_time("box_delay_time"),
    daily_turntable_times("daily_turntable_times"),
    turntable_cd("turntable_cd"),
    float_ads_disappear_times("float_ads_disappear_times"),
    batch_auto_open_box("batch_auto_open_box"),
    single_feed_exp("single_feed_exp"),


    guild_notice_length("guild_notice_length"),
    change_guild_name_cost("change_guild_name_cost"),
    guild_daily_donate("guild_daily_donate"),
    change_guild_flag_cost("change_guild_flag_cost"),
    guild_flag_shape("guild_flag_shape"),
    guild_flag_color("guild_flag_color"),
    guild_flag_design("guild_flag_design"),
    guild_flag_design_color("guild_flag_design_color"),
    guild_flag_moving_range("guild_flag_moving_range"),
    guild_flag_design_size("guild_flag_design_size"),
    guild_flag_scroll_view("guild_flag_scroll_view"),
    guild_help_time("guild_help_time"),
    guild_help_reward("guild_help_reward"),
    guild_help_reward_limit("guild_help_reward_limit"),
    guild_flag_default("guild_flag_default"),
    guild_scene_walk_speed("guild_scene_walk_speed"),
    guild_scene_player_num("guild_scene_player_num"),
    guild_scene_min_fps("guild_scene_min_fps"),
    league_solo_chapter_recover("league_solo_chapter_recover"),
    league_solo_chapter_transform_level("league_solo_chapter_transform_level"),
    league_solo_chapter_chest_level("league_solo_chapter_chest_level"),
    league_solo_chapter_rare_chest_rate("league_solo_chapter_rare_chest_rate"),
    league_gve_chapter_time("league_gve_chapter_time"),
    league_gve_chapter_time_limit("league_gve_chapter_time_limit"),
    league_gve_chapter_buff("league_gve_chapter_buff"),
    league_gve_chapter_count_down("league_gve_chapter_count_down"),
    league_gve_chapter_participate_time("league_gve_chapter_participate_time"),
    treasure_hunt_time("treasure_hunt_time"),
    treasure_hunt_points("treasure_hunt_points"),
    treasure_hunt_range("treasure_hunt_range"),
    treasure_hunt_exist_time("treasure_hunt_exist_time"),
    treasure_hunt_pick_time("treasure_hunt_pick_time"),
    treasure_hunt_open("treasure_hunt_open"),
    familybattle_signup_condition("familybattle_signup_condition"),
    familybattle_onebattle_time("familybattle_onebattle_time"),
    familybattle_guidance("familybattle_guidance"),
    familybattle_detail_time("familybattle_detail_time"),
    familybattle_reset_rule("familybattle_reset_rule"),
    familybattle_battle_season_rewardview("familybattle_battle_season_rewardview"),
    familybattle_nullenemy_reward("familybattle_nullenemy_reward"),
    familybattle_nullenemy_selfpoint_reward("familybattle_nullenemy_selfpoint_reward"),
    familybattle_nullenemy_familypoint_reward("familybattle_nullenemy_familypoint_reward"),
    voice_time_max("voice_time_max"),
    legacy_team_chapter_recover("legacy_team_chapter_recover"),
    farm_steal_research("farm_steal_research"),
    farm_steal_enemy("farm_steal_enemy"),
    statue_spend_experience("statue_spend_experience"),
    farm_dairy_quantity("farm_dairy_quantity"),
    farm_pvp_quantity("farm_pvp_quantity"),
    mount_chapter_bonus("mount_chapter_bonus"),
    legacy_team_chapter_bonus("legacy_team_chapter_bonus"),
    legacy_team_chapter_time_limit("legacy_team_chapter_time_limit"),
    max_match_time("max_match_time"),
    legacy_team_chapter_assist_reward("legacy_team_chapter_assist_reward"),
    legacy_team_chapter_match_teammates("legacy_team_chapter_match_teammates"),
    forum_first_reward("forum_first_reward"),
    power_save_mode("power_save_mode"),
    farm_steal_max("farm_steal_max"),
    mount_draw_expend("mount_draw_expend"),
    mount_draw_reward("mount_draw_reward"),
    mount_draw_garanteed_times("mount_draw_garanteed_times"),
    initial_power("initial_power"),
    LIMITED_RANK_SYSTEM_DETAILS("LIMITED_RANK_SYSTEM_DETAILS"),
    defeat_tips("defeat_tips"),
    gve_boss_attr("gve_boss_attr"),
    hide_bowl_cut("hide_bowl_cut"),
    fps_check_time("fps_check_time"),
    fps_check_limit("fps_check_limit"),
    bundle_pop_gap("bundle_pop_gap"),
    auto_simp_mode_cd("auto_simp_mode_cd"),
    auto_simp_mode_disable_time("auto_simp_mode_disable_time"),
    auto_simp_mode_disable_count("auto_simp_mode_disable_count"),
    farm_ai_level("farm_ai_level"),
    farm_ai_level_gap("farm_ai_level_gap"),
    fate_pos("fate_pos"),
    fate_quality("fate_quality"),
    fate_quality_pos("fate_quality_pos"),
    rare_fate("rare_fate"),
    fate_draw_expend("fate_draw_expend"),
    fate_draw_guaranteed("fate_draw_guaranteed"),
    fate_bag_limit("fate_bag_limit"),
    fate_draw_weight("fate_draw_weight"),
    quiz_start_time("quiz_start_time"),
    quiz_gap_time("quiz_gap_time"),
    quiz_answer_time("quiz_answer_time"),
    quiz_answer_score("quiz_answer_score"),
    quiz_first_answer_score("quiz_first_answer_score"),
    quiz_question_num("quiz_question_num"),
    quiz_answer_reward("quiz_answer_reward"),
    quiz_rank_reward("quiz_rank_reward"),
    quiz_player_limit("quiz_player_limit"),
    quiz_dice_time("quiz_dice_time"),
    quiz_dice_reward("quiz_dice_reward"),
    quiz_rank_reward_leader("quiz_rank_reward_leader"),
    jobs_wakeup_props_convert("jobs_wakeup_props_convert"),
    jobs_wakeup_imprint_name("jobs_wakeup_imprint_name"),
    jobs_wakeup_cost("jobs_wakeup_cost"),
    jobs_wakeup_condition("jobs_wakeup_condition"),
    red_packet_lucky_time("red_packet_lucky_time"),
    world_boss_open_time("world_boss_open_time"),
    world_boss_daily_reward("world_boss_daily_reward"),
    world_boss_buff_switch_day("world_boss_buff_switch_day"),
    equip_tab_limit("equip_tab_limit"),
    world_boss_news_player_limit("world_boss_news_player_limit"),
    quiz_rank_reward_exp("quiz_rank_reward_exp"),
    quiz_rank_reward_topleader("quiz_rank_reward_topleader"),
    world_boss_challenge_limit("world_boss_challenge_limit"),
    world_boss_time_limit("world_boss_time_limit"),
    world_boss_player_num("world_boss_player_num"),
    quiz_dice_range("quiz_dice_range"),
    tanabata_battlepass_limited_reward("tanabata_battlepass_limited_reward"),
    miss_correct("miss_correct"),
    vertigo_correct("vertigo_correct"),
    shield_correct("shield_correct"),
    hp_recovery_correct("hp_recovery_correct"),
    statue_tab_limit("statue_tab_limit"),
    pet_tab_num("pet_tab_num"),
    skill_tab_num("skill_tab_num"),
    battle_check_constant("battle_check_constant"),
    cross_pvp_grading_match_reward("cross_pvp_grading_match_reward"),
    cross_pvp_grading_match_time("cross_pvp_grading_match_time"),
    cross_pvp_grading_match_num("cross_pvp_grading_match_num"),
    auto_skill_delay("auto_skill_delay"),
    cross_pvp_initial_score("cross_pvp_initial_score"),
    cross_pvp_s("cross_pvp_s"),
    cross_pvp_k("cross_pvp_k"),
    cross_pvp_battle_max("cross_pvp_battle_max"),
    cross_pvp_score_change_range("cross_pvp_score_change_range"),
    cross_pvp_battle_reward("cross_pvp_battle_reward"),
    cross_pvp_battle_win_ratio("cross_pvp_battle_win_ratio"),
    cross_pvp_battle_lose_ratio("cross_pvp_battle_lose_ratio"),
    douyin_entrance_reward("douyin_entrance_reward"),
    schoolopen_poster_show("schoolopen_poster_show"),
    vip_reward_show("vip_reward_show"),
    vip_identify_reward("vip_identify_reward"),
    cross_pvp_top_id("cross_pvp_top_id"),
    cross_pvp_ticket_price("cross_pvp_ticket_price"),
    cross_pvp_application_top_id("cross_pvp_application_top_id"),
    strategy_activity_monster_score("strategy_activity_monster_score"),
    strategy_activity_time_score("strategy_activity_time_score"),
    strategy_activity_reset_job_cost("strategy_activity_reset_job_cost"),
    autumn_panghong_gift("autumn_panghong_gift"),
    strategy_activity_display_attr("strategy_activity_display_attr"),
    strategy_activity_basic_supply("strategy_activity_basic_supply"),
    strategy_activity_supply_cd("strategy_activity_supply_cd"),
    gamecentre_mario_reward("gamecentre_mario_reward"),
    gamecentre_compose_reward("gamecentre_compose_reward"),
    autumn_search_complete_reward("autumn_search_complete_reward"),
    christmas_search_complete_reward("christmas_search_complete_reward"),
    equip_unlock_condition("equip_unlock_condition"),
    equip_unlock_condition_show("equip_unlock_condition_show"),
    equip_unlock_condition_show2("equip_unlock_condition_show2"),
    autumn_strength_dailycap("autumn_strength_dailycap"),
    autumn_card_dailygift("autumn_card_dailygift"),
    autumn_card_dailyask("autumn_card_dailyask"),
    autumn_strength_dailygift("autumn_strength_dailygift"),
    autumn_stage_pass_condition("autumn_stage_pass_condition"),
    gamecentre_breakthrough_reward("gamecentre_breakthrough_reward"),
    autumn_card_validity_period("autumn_card_validity_period"),
    autumn_shot_guidance("autumn_shot_guidance"),
    gamecentre_towerdefence_reward("gamecentre_towerdefence_reward"),
    gamecentre_snake_reward("gamecentre_snake_reward"),
    gamecentre_hide_reward("gamecentre_hide_reward"),
    gamecentre_swallow_reward("gamecentre_swallow_reward"),
    daily_monster_num("daily_monster_num"),
    cooss_pvp_no_robot("cooss_pvp_no_robot"),
    online_reward_stagetime("online_reward_stagetime"),
    online_reward_hidetime("online_reward_hidetime"),
    suit_tab_limit("suit_tab_limit"),
    pvp_page_limit("pvp_page_limit"),
    park_interval_limit("park_interval_limit"),
    park_time_limit("park_time_limit"),
    park_exp_rate("park_exp_rate"),
    park_mount_limit_time("park_mount_limit_time"),
    park_mount_limit_rate("park_mount_limit_rate"),
    park_num("park_num"),
    park_mount_limit("park_mount_limit"),
    park_management_cost("park_management_cost"),
    mainscene_left_iconlist_show("mainscene_left_iconlist_show"),
    mainscene_right_iconlist_show("mainscene_right_iconlist_show"),
    park_exp_book_num("park_exp_book_num"),
    park_steal_rate("park_steal_rate"),
    park_defend_power_limit("park_defend_power_limit"),
    ask_sweet_success_pro("ask_sweet_success_pro"),
    ask_sweet_get_num("ask_sweet_get_num"),
    park_owner_reward("park_owner_reward"),
    ask_sweet_ads_buff("ask_sweet_ads_buff"),
    park_initial_design("park_initial_design"),
    steal_check_error("steal_check_error"),
    steal_check_success_limit("steal_check_success_limit"),
    steal_check_success_max("steal_check_success_max"),
    artifact_gem_max_attrnum("artifact_gem_max_attrnum"),
    artifact_gem_addattr_pro("artifact_gem_addattr_pro"),
    artifact_gem_max_num("artifact_gem_max_num"),
    mine_auto_min_num("mine_auto_min_num"),
    park_public_num("park_public_num"),
    park_public_buff("park_public_buff"),
    mine_auto_open("mine_auto_open"),
    park_pixel("park_pixel"),
    relic_tab_num("relic_tab_num"),
    park_public_each_num("park_public_each_num"),
    draw_discount_expend("draw_discount_expend"),
    park_steal_limit("park_steal_limit"),
    skill_delay("skill_delay"),
    trading_card_dailyget("trading_card_dailyget"),
    trading_card_dailworld_gift("trading_card_dailworld_gift"),
    trading_card_dailworld_ask("trading_card_dailworld_ask"),
    trading_card_validity_period("trading_card_validity_period"),
    park_languge("park_languge"),
    datang_mole_guidance("datang_mole_guidance"),
    msqql_guidance("msqql_guidance"),
    farm_steal_limit("farm_steal_limit"),
    capture_slave_research("capture_slave_research"),
    capture_slave_enemy("capture_slave_enemy"),
    capture_slave_free_time("capture_slave_free_time"),
    capture_slave_limit("capture_slave_limit"),
    capture_slave_reward_rate("capture_slave_reward_rate"),
    capture_slave_reward_limit("capture_slave_reward_limit"),
    capture_slave_output_time("capture_slave_output_time"),
    capture_slave_collecttime("capture_slave_collecttime"),
    artifact_gem_attr("artifact_gem_attr"),
    seven_trial_reward("seven_trial_reward"),
    seven_trial_guidance("seven_trial_guidance"),
    seven_trial2_reward("seven_trial2_reward"),
    park_cross_num("park_cross_num"),
    park_cross_buff("park_cross_buff"),
    park_cross_auto_time("park_cross_auto_time"),
    park_cross_cd("park_cross_cd"),
    park_cross_protect_time("park_cross_protect_time"),
    park_cross_pvp_time("park_cross_pvp_time"),
    park_cross_defend_cd("park_cross_defend_cd"),
    park_cross_each_num("park_cross_each_num"),
    back_decoration_unlock("back_decoration_unlock"),
    park_cross_open_time("park_cross_open_time"),
    add_table_reward("add_table_reward"),
    capture_slave_help_time("capture_slave_help_time"),
    back_trial_enemy_attribute_show("back_trial_enemy_attribute_show"),
    back_trial_chapter_difficulty_dailyunlock("back_trial_chapter_difficulty_dailyunlock"),
    back_trial_chapter_difficulty_fastchallenge("back_trial_chapter_difficulty_fastchallenge"),
    capture_slave_cd("capture_slave_cd"),
    default_skill_delay("default_skill_delay"),
    cancel_skill_reset_type("cancel_skill_reset_type"),
    reversion_war_num("reversion_war_num"),
    reversion_war_basic_supply("reversion_war_basic_supply"),
    reversion_war_supply_cd("reversion_war_supply_cd"),
    back_trial_chapter_order("back_trial_chapter_order"),
    activity_seed_rewards("activity_seed_rewards"),
    fate_reset_quality("fate_reset_quality"),
    break_gold_egg_expend("break_gold_egg_expend"),
    fate_reset_expend("fate_reset_expend"),
    new_year_gift_limit("new_year_gift_limit"),
    break_gold_egg_guarantee("break_gold_egg_guarantee"),
    one_click_break_gold_egg("one_click_break_gold_egg"),
    slumber_party_boss_hp_reduce("slumber_party_boss_hp_reduce"),
    chrismas_lightshow_guidance("chrismas_lightshow_guidance"),
    mail_auto_show_limit("mail_auto_show_limit"),
    mail_auto_reward_delete("mail_auto_reward_delete"),
    wechat_reward("wechat_reward"),
    park_auto_time("park_auto_time"),
    park_rename_cd("park_rename_cd"),
    game_room_longest_time("game_room_longest_time"),
    toy_game_recommend("toy_game_recommend"),
    server_type("server_type"),
    ios_prebook_reward("ios_prebook_reward"),
    farm_auto_level("farm_auto_level"),
    attach_level_limit("attach_level_limit"),
    marry_level_limit("marry_level_limit"),
    divorce_favorability_remain("divorce_favorability_remain"),
    wedding_pre_time("wedding_pre_time"),
    wedding_time_openday("wedding_time_openday"),
    wedding_out_time("wedding_out_time"),
    wedding_cheers_cd("wedding_cheers_cd"),
    wedding_cheers_auto_time("wedding_cheers_auto_time"),
    wedding_active_reward_time("wedding_active_reward_time"),
    wedding_active_time("wedding_active_time"),
    wedding_active_reward_max("wedding_active_reward_max"),
    wedding_active_cost("wedding_active_cost"),
    divorce_cost("divorce_cost"),
    divorce_time("divorce_time"),
    marry_cd("marry_cd"),
    douyin_video_daily_reward("douyin_video_daily_reward"),
    park_cross_beat_max("park_cross_beat_max"),
    alipay_centre_reward("alipay_centre_reward"),
    park_cross_creat_limit("park_cross_creat_limit"),
    red_envelope_rain("red_envelope_rain"),
    red_envelope_rain_time("red_envelope_rain_time"),
    overlord_shot_guidance("overlord_shot_guidance"),
    gift_list("gift_list"),
    act_share_text("act_share_text"),
    alipay_reborn_value_job("alipay_reborn_value_job"),
    alipay_reborn_value_level("alipay_reborn_value_level"),
    alipay_reborn_value_property("alipay_reborn_value_property"),
    alipay_reborn_value_taskid("alipay_reborn_value_taskid"),
    alipay_reborn_value_equipment("alipay_reborn_value_equipment"),
    alipay_reborn_value_guankaid("alipay_reborn_value_guankaid"),
    alipay_reborn_value_bossguankaid("alipay_reborn_value_bossguankaid"),
    wedding_active_add("wedding_active_add"),
    act_search_complete_reward("act_search_complete_reward"),
    wedding_tri_reward("wedding_tri_reward"),
    newyear_dinner_time("newyear_dinner_time"),
    newyear_dinner_chat_count("newyear_dinner_chat_count"),
    newyear_dinner_base_reward("newyear_dinner_base_reward"),
    rolename_change_limit("rolename_change_limit"),
    familiarity_gift_message("familiarity_gift_message"),
    collect_five_blessing_goods("collect_five_blessing_goods"),
    collect_five_blessing_spec_reward("collect_five_blessing_spec_reward"),
    collect_five_blessing_normal_reward("collect_five_blessing_normal_reward"),
    park_cross_level_limit("park_cross_level_limit"),
    toy_master_out_time("toy_master_out_time"),
    toy_player_out_time("toy_player_out_time"),
    toy_chat_invite_cd("toy_chat_invite_cd"),
    toy_friend_invite_cd("toy_friend_invite_cd"),
    familiarity_gift_buy_limit("familiarity_gift_buy_limit"),
    toy_escape_num("toy_escape_num"),
    mainscene_default_shendeng("mainscene_default_shendeng"),
    wedding_time_limit("wedding_time_limit"),
    marry_invite_cd("marry_invite_cd"),
    wedding_begin_limit("wedding_begin_limit"),
    maze_character("maze_character"),
    lantern_maze_guidance("lantern_maze_guidance"),
    back_leave("back_leave"),
    back_time1("back_time1"),
    back_time2("back_time2"),
    back_time3("back_time3"),
    back_showredawrd1("back_showredawrd1"),
    back_showredawrd2("back_showredawrd2"),
    back_showredawrd3("back_showredawrd3"),
    back_showredawrd4("back_showredawrd4"),
    back_redawrd("back_redawrd"),
    back_rare1("back_rare1"),
    back_rare2("back_rare2"),
    rebate_back1("rebate_back1"),
    rebate_back2("rebate_back2"),
    back_xishu("back_xishu"),
    back_number("back_number"),
    rebate_back3("rebate_back3"),
    push_default_level("push_default_level"),
    transfer_open_day("transfer_open_day"),
    transfer_apply_cd("transfer_apply_cd"),
    transfer_succ_cd("transfer_succ_cd"),
    space_frog_get_score("space_frog_get_score"),
    space_frog_origin_location("space_frog_origin_location"),
    space_frog_g("space_frog_g"),
    space_frog_click_speed("space_frog_click_speed"),
    space_frog_incline_limit("space_frog_incline_limit"),
    space_frog_speed_limit("space_frog_speed_limit"),
    datang_mole_guidance1("datang_mole_guidance1"),
    frog_guidance("frog_guidance"),
    auto_open_box_text("auto_open_box_text"),
    lucky_cat_delay_capital("lucky_cat_delay_capital"),
    park_cross_night_limit("park_cross_night_limit"),
    park_cross_parking_level_limit("park_cross_parking_level_limit"),
    double_ladder_match_num("double_ladder_match_num"),
    double_ladder_partner_num("double_ladder_partner_num"),
    double_ladder_rest_recover("double_ladder_rest_recover"),
    double_ladder_initial_level("double_ladder_initial_level"),
    double_ladder_assist_num("double_ladder_assist_num"),
    double_ladder_assist_reward_max("double_ladder_assist_reward_max"),
    catch_success_pro("catch_success_pro"),
    catch_get_num("catch_get_num"),
    catch_ads_buff("catch_ads_buff"),
    ouxiang_cd("ouxiang_cd"),
    ouxiang_num("ouxiang_num"),
    ouxiang_perfect_score("ouxiang_perfect_score"),
    ouxiang_good_score("ouxiang_good_score"),
    ouxiang_changan("ouxiang_changan"),
    yinyou_guidance("yinyou_guidance"),
    transfer_member_limit("transfer_member_limit"),
    transfer_open_day_range("transfer_open_day_range"),
    ranked_match_win_points("ranked_match_win_points"), // 武道会：胜利积分
    ranked_match_lose_points("ranked_match_lose_points"), // 武道会：失败积分
    maxpoints("maxpoints"), // 武道会：最大积分
    ranked_match_win_buff("ranked_match_win_buff"), // 武道会：连胜系数
    ranked_match_timepoints("ranked_match_timepoints"), // 武道会：挑战时间额外积分
    ranked_match_challenge_times("ranked_match_challenge_times"), // 挑战次数
    guess_points_rate("guess_points_rate"), // 竞猜积分兑换率
    bet_odds_min("bet_odds_min"),   // 筹码最小倍率
    bet_odds_max("bet_odds_max"),   // 筹码最大倍率
    bet_base_amount("bet_base_amount"), // 竞猜基础数量
    wrong_guess_odds("wrong_guess_odds"), // 竞猜失败返还倍率
    add_bet_rate("add_bet_rate"),   // 增加竞猜范围
    add_bet_limit("add_bet_limit"), // 增加竞猜极限
    display_worship_reward("display_worship_reward"), // 展示期膜拜奖励

    fly_hybrid_refresh("fly_hybrid_refresh"),
    fly_attr_growth_rate("fly_attr_growth_rate"),
    fly_reset_cost("fly_reset_cost"),
    fly_reset_return_rate("fly_reset_return_rate"),
    fly_resolve_reward("fly_resolve_reward"),
    fly_var_pro("fly_var_pro"),
    fly_partner_num("fly_partner_num"),
    fly_borrow_num("fly_borrow_num"),
    fly_hybrid_num("fly_hybrid_num"),
    fly_rename_cost("fly_rename_cost"),
    fly_hybrid_attr_growth_rate("fly_hybrid_attr_growth_rate"),
    fly_init_egg("fly_init_egg"),
    fly_num_max("fly_num_max"),
    fly_egg_num_max("fly_egg_num_max"),
    fly_reset_goods("fly_reset_goods"),
    fly_hybrid_egg("fly_hybrid_egg"),
    fly_hybrid_cost("fly_hybrid_cost"),
    fly_reset_return_cost("fly_reset_return_cost"),
    fly_remake_par_diy("fly_remake_par_diy"),
    fly_remake_par_change("fly_remake_par_change"),

    limitgift_player_charge_point("limitgift_player_charge_point"),

    sns_show_reward("sns_show_reward"),

    game_accelerate_cost("game_accelerate_cost"),// 没开特权卡的加速消耗
    game_accelerate_maxtime("game_accelerate_maxtime"),// 最大加速次数

    familybattle_cross_sever_time("familybattle_cross_sever_time"),
    farm_record_limit("farm_record_limit"),

    ChristmasPvp_Getbuff_cost("ChristmasPvp_Getbuff_cost"),
    ChristmasPvp_battlefail_skip("ChristmasPvp_battlefail_skip"),
    BoxTowerLevelDraw("BoxTowerLevelDraw"),

    shortagegift_push_gear("shortagegift_push_gear"),

    Legion_initial_actionPower("Legion_initial_actionPower"),
    Legion_reward_mallItem("Legion_reward_mallItem"),
    Legion_quickfight_count("Legion_quickfight_count"),

    emaki_develop_replace_time("emaki_develop_replace_time"),
    emaki_fight_cost("emaki_fight_cost"),
    emaki_develope_cost("emaki_develop_cost"),
    angel_salvage_count_limit("angel_salvage_count_limit"),
    double_ladder_max_help_num("double_ladder_max_help_num"),

    summoner_passive1_jobtype("summoner_passive1_jobtype"),
    Slime_stamina_initial("Slime_stamina_initial"),
    Slime_stamina_recover("Slime_stamina_recover"),
    Slime_initial_job("Slime_initial_job"),
    Slime_initial_attr("Slime_initial_attr"),
    Slime_refine_item("Slime_refine_item"),
    Slime_turntable_needtime("Slime_turntable_needtime"),
    angel_upgrade_star_goods("angel_upgrade_star_goods"),
    Slime_lucky_turntable_cost2("Slime_lucky_turntable_cost2"),
    angel_init_angel("angel_init_angel"),
    Valentine_flower_level_limit("Valentine_flower_level_limit"),
    lamp_attr_filter("lamp_attr_filter"),
    Daily_Recharge_Close("Daily_Recharge_Close"),
    Activity_Calendar_reward("Activity_Calendar_reward"),   // 活动日历每日免费礼包
    dungeon_stamina_initial("dungeon_stamina_initial"), // 转剑初始体力值
    dungeon_stamina_recover("dungeon_stamina_recover"),   // 转剑体力恢复间隔（秒）
    dungeon_stamina_max("dungeon_stamina_max"),   // 转剑体力恢复上限
    dungeon_material_need("dungeon_material_need"),   // 转剑锻造中材料进度达到多少可以领取1个材料
    dungeon_material_value("dungeon_material_value"),   // 每次锻造获得多少材料进度
    medal_show("medal_show"), // 美观值勋章栏位解锁条件（配置槽位id,所需美观值）
    charm_likes_times("charm_likes_times"), // 美观值每日点赞次数上限
    charm_likes_reward("charm_likes_reward")    // 美观值点赞掉落奖励（配置output_id）
    ;

    // TODO 请按数字大小排序，避免重复和查询麻烦


    public final String SN;

    ConfGlobalKey(String sn) {
        this.SN = sn;
    }


}
