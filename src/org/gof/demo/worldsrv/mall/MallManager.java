package org.gof.demo.worldsrv.mall;

import com.alibaba.fastjson.JSONObject;
import com.pwrd.op.LogOp;
import com.pwrd.op.LogOpChannel;
import org.apache.commons.collections.map.HashedMap;
import org.gof.core.Port;
import org.gof.core.support.*;
import org.gof.core.support.observer.Listener;
import org.gof.demo.battlesrv.msgHandler.SkillManager;
import org.gof.demo.support.TimeUtil;
import org.gof.demo.worldsrv.accumulatedRecharge.AccumulatedRechargeManager;
import org.gof.demo.worldsrv.accumulatedRecharge.AccumulatedRechargeUtils;
import org.gof.demo.worldsrv.activity.ActivityControlType;
import org.gof.demo.worldsrv.activity.ActivityControlTypeFactory;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.activity.calculator.*;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlDayPayData;
import org.gof.demo.worldsrv.back.BackUtils;
import org.gof.demo.worldsrv.character.HumanDailyResetInfo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.fund.FundManager;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.human.HumanSettingConstants;
import org.gof.demo.worldsrv.human.MoneyManager;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.intergration.PF_GM_Manager;
import org.gof.demo.worldsrv.intergration.PF_PAY_Manager;
import org.gof.demo.worldsrv.item.ItemConstants;
import org.gof.demo.worldsrv.mail.MailManager;
import org.gof.demo.worldsrv.msg.*;
import org.gof.demo.worldsrv.pet.PetManager;
import org.gof.demo.worldsrv.pocketLine.PocketLineEventSubKey;
import org.gof.demo.worldsrv.privilege.PrivilegeManager;
import org.gof.demo.worldsrv.privilege.PrivilegeType;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.*;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.DailyResetTypeKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;

import java.util.*;
import java.util.stream.Collectors;

public class MallManager extends ManagerBase {

	public static final int cardPoolType1 = 1; //抽卡技能类型
	public static final int cardPoolType2 = 2; //抽卡同伴类型
	public static final String mallOpenfunc= "openfunc";
	public static final String mallGuildLv= "guild_lev";
	public static final String mallActivityGroup= "activity_group";
	public static final String mallOpenTime = "open_time";
	public static final String mallKungfuRace = "kungfuRace";

	public static final int MallType_Gem = 1;
	public static final int MallType_GuildDay = 2;
	public static final int MallType_GuildWeek = 3;
	public static final int MallType_Farm = 4;
	public static final int MallType_Activity = 5;
	public static final int MallType_RankingRush = 6;
	public static final int MallType_GvG = 7;
	public static final int MallType_PvpCompetitionDay = 8;
	public static final int MallType_PvpCompetitionWeek = 9;
	public static final int MallType_CrossWar = 10;
	public static final int MallType_Parking = 11;
	public static final int MallType_Season = 12;
	public static final int MallType_Back = 13;		// 回归商城
	public static final int MallType_BlackMarket = 14;	// 黑市

	public static final int rewardType_0 = 0;//初始
	public static final int rewardType_1 = 1;//可以充值
	public static final int rewardType_2 = 2;//已经领取


	/**
	 * 获取实例
	 * @return
	 */
	public static MallManager inst() {
		return inst(MallManager.class);
	}

	/***
	 * 玩家充值
	 * @param param
	 */
	@Listener(EventKey.PAY_NOTIFY)
	public void _listener_PAY_NOTIFY(Param param) {
		try {
			HumanObject humanObj = param.get("humanObj");
			int sn = param.get("sn");
			boolean isFake = param.get("isFake") != null;

			Log.temp.error("===充值成功，humanId={}, account={}, name={} sn={}", humanObj.id, humanObj.account, humanObj.name, sn);

			ConfPayMall confPayMall = ConfPayMall.get(sn);
			if (confPayMall == null) {
				Log.temp.error("===ConfPayMall配表错误， humanId={}, sn={}", humanObj.id, sn);
				return;
			}
			List<Define.p_reward> p_rewards = new ArrayList<>();
			boolean result = payMall(humanObj, sn, p_rewards, isFake);
			sendMsg_pay_mall_recharge_s2c(humanObj, sn, "", PF_PAY_Manager.payType1, "");
			if (!result) {
				Log.temp.error("===充值失败，humanId={}, sn={}, type={}", humanObj.id, sn, confPayMall.type);
				return;
			}
			int showType = 0;
			List<Define.p_key_value> extList = new ArrayList<>(0);
			switch (confPayMall.type){
				case PayMallTypeKey.PAY_Type_1:
					break;
				case PayMallTypeKey.PAY_Type_2:
					break;
				case PayMallTypeKey.PAY_Type_3:
					payDiamondMall(humanObj, sn, p_rewards);
					break;
				case PayMallTypeKey.PAY_Type_4:
					break;
				case PayMallTypeKey.PAY_Type_5:
					payWarToken(humanObj, sn);
					break;
				case PayMallTypeKey.PAY_Type_6:
					break;
				case PayMallTypeKey.PAY_Type_7:
					break;
				case PayMallTypeKey.PAY_Type_8:
					payStarDiamondMall(humanObj, sn, p_rewards);
					break;
				case PayMallTypeKey.PAY_Type_9:
					payPrivilegeCard(humanObj, sn);
					break;
				case PayMallTypeKey.PAY_Type_10:
					ConfFund confFund = ConfFund.getBy(ConfFund.K.giftId,sn);
					if(confFund != null){
						FundManager.inst().on_fund_info_c2s(humanObj, confFund.sn);
					}
					break;
				case PayMallTypeKey.PAY_Type_11:
					break;
				case PayMallTypeKey.PAY_Type_12: {
					payCustomMall(humanObj, sn, p_rewards);
				}
				break;
				case PayMallTypeKey.PAY_Type_13:
					break;
				case PayMallTypeKey.PAY_Type_20:
					payFirstCharge(humanObj, sn);
					break;
				case PayMallTypeKey.PAY_Type_23:
					sendMsg_pay_mall_info_s2c(humanObj, PayMallTypeKey.PAY_Type_23);
					break;
				case PayMallTypeKey.PAY_Type_26: {
					buyLimitGift26(humanObj, sn);
				}
				break;
				case PayMallTypeKey.PAY_Type_27: {
					showType = InstanceConstants.showType_1001008;
					buyDiscountGift27(humanObj, sn, p_rewards, extList);
				}
				break;
				case PayMallTypeKey.PAY_Type_28: {
					sendMsg_pay_mall_info_s2c(humanObj, PayMallTypeKey.PAY_Type_28);
				}
				break;
				case PayMallTypeKey.PAY_Type_30: {
					// 如果是顶层的礼包，这里逻辑就不需要走，因为顶层礼包条件是活动类型，原本的通用流程会处理
					String[] conditions = GlobalConfVal.payConditionsMap.get(confPayMall.sn);
					if (conditions == null || PayMallTypeKey.pay_condition_act_type.equals(conditions[0])) {
						break;
					}
					// 非顶层的礼包，就需要获取到顶层礼包的活动类型才能往下走，非顶层礼包配的条件是前置礼包，那就取不到活动类型
					ConfPayMall confRoot = ConfPayMall.get(confPayMall.parameter);
					if (confRoot == null) {
						break;
					}
					conditions = GlobalConfVal.payConditionsMap.get(confRoot.sn);
					if (conditions == null || !PayMallTypeKey.pay_condition_act_type.equals(conditions[0])) {
						break;
					}
					int actType = Integer.parseInt(conditions[1]);
					IActivityControl control = ActivityControlTypeFactory.getTypeData(actType);
					control.pay(humanObj, confPayMall.sn);
				}
				break;
				case PayMallTypeKey.PAY_Type_31: {
					if (confPayMall.param != null && !"".equals(confPayMall.param)) {
						// 说明是一键全部购买的礼包
						Map<Integer, Integer> rewardMap = new HashMap<>();
						int[] subSns = Utils.strToIntArray(confPayMall.param);
						for (int subSn : subSns) {
							ConfPayMall confSubPay = ConfPayMall.get(subSn);
							updatePayMallBuyNum(humanObj, subSn, confSubPay.reset);
							for (int[] rewards : confSubPay.reward) {
								rewardMap.put(rewards[0], rewardMap.getOrDefault(rewards[0], 0) + rewards[1]);
							}
							p_rewards.addAll(InstanceManager.inst().to_p_rewardList(confSubPay.reward));
							sendMsg_pay_mall_update_s2c(humanObj, subSn, confSubPay);
						}
						ProduceManager.inst().produceAdd(humanObj, rewardMap, MoneyItemLogKey.充值);
					}
				}
				break;
				case PayMallTypeKey.PAY_Type_32: {
					// 团购礼包走充值是发邮件给奖励的
					ConfMergeGroupGift conf = ConfMergeGroupGift.get(confPayMall.parameter);
					if (conf == null) {
						Log.temp.error("团购礼包配置不存在，parameter={}", confPayMall.parameter);
						return;
					}
					String itemJSON = Utils.mapIntIntToJSON(conf.reward);
					MailManager.inst().sendMail(humanObj.id, MailManager.SYS_SENDER, conf.mail_id, "", "", itemJSON, null);
				}
				break;
				default:
					break;
			}
			if (!p_rewards.isEmpty()) {
				// 通知奖励
				InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, showType, p_rewards, extList);
			}
			if(sn == PrivilegeType.PRIVILEGE_BACK_MOON_CARD){
				// 写死回归月卡
				PrivilegeManager.inst().givePrivilegeCard(humanObj, 1);
			}
			// 通知活动礼包已经购买，触发活动后续逻辑
			String[] conditions = GlobalConfVal.payConditionsMap.get(confPayMall.sn);
			if (conditions != null && PayMallTypeKey.pay_condition_act_type.equals(conditions[0])) {
				handleActPayMallPay(humanObj, confPayMall);
			}
		} catch (Exception e) {
			Log.game.error("充值事件监听出错。e={}", e);
		}
	}

	private void handleAccumulatedRecharge(HumanObject humanObj, int price){
		// 策划规定VipPoints点数乘以100为累充金额
		int pt = Utils.intValue(getRegionalVipPoint(humanObj.getRegional(), price) * 100);
		HumanDailyResetInfo ptInfo = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyRechargePt.getType());
		// 累计今日充值Pt
		ptInfo.setValue(ptInfo.getValue() + pt);
		HumanDailyResetInfo canReceiveInfo = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyRechargeCanReceiveNum.getType());
		if(canReceiveInfo.getValue() == 0){
			// 累计新的充值前，刷新累充轮次
			AccumulatedRechargeManager.inst().refreshAccumulatedRechargeRound(humanObj);
			Human2 human2 = humanObj.getHuman2();
			int day = human2.getAccumulatedRechargeDay() + 1;
			ConfDailyRecharge conf = AccumulatedRechargeUtils.getAccumulatedRechargeConf(humanObj, day);
			if(conf != null){
				if(ptInfo.getValue() >= conf.condition){
					human2.setAccumulatedRechargeDay(day);
					// 累充条件达成，标记可领取
					canReceiveInfo.setValue(1);
					// 推累充info
					AccumulatedRechargeManager.inst()._msg_accumulated_recharge_info_c2s(humanObj);
					Log.accumulatedRecharge.info("===humanId={} setAccumulatedRechargeDay={}", humanObj.id, day);
				}
			}
		}
		humanObj.saveDailyResetRecord();
	}

	private void handleActPayMallPay(HumanObject humanObj, ConfPayMall conf) {
		int[] ints = GlobalConfVal.actPayMallConditionMap.get(conf.sn);
		if (ints == null) {
			return;
		}
		int actType = ints[0];
		IActivityControl control = ActivityControlTypeFactory.getTypeData(actType);
		if (control == null) {
			return;
		}
		control.pay(humanObj, conf.sn);
	}

	public void sendMsg_pay_mall_recharge_s2c(HumanObject humanObj, int sn, String orderId, int type, String giftId){
		MsgPayMall.pay_mall_recharge_s2c.Builder msg = MsgPayMall.pay_mall_recharge_s2c.newBuilder();
		msg.setBundleId(sn);
		msg.setType(type);
		msg.setOrderId(orderId);
		msg.setGiftId(giftId);
		humanObj.sendMsg(msg);
	}

	// =========================================TODO payMall
	/**
	 * 请求商店信息
	 * <AUTHOR>
	 * @Date 2024/3/12
	 * @Param
	 */
	public void _msg_pay_mall_info_c2s(HumanObject humanObj, int type){
		sendMsg_pay_mall_info_s2c(humanObj, type);
	}
	public void sendMsg_pay_mall_info_s2c(HumanObject humanObj, int type){
		MsgPayMall.pay_mall_info_s2c.Builder msg = MsgPayMall.pay_mall_info_s2c.newBuilder();
		msg.setType(type);
		if (type == 0) {
			Map<Integer, List<ConfPayMall>> confPayMallMap = GlobalConfVal.typePayMallMap;
			//遍历key
			for (Integer key : confPayMallMap.keySet()) {
				if (key == PayMallTypeKey.PAY_Type_7) {
					continue;
				}
				List<Define.p_pay_mall_info> list = to_p_pay_mall_info_list(humanObj, key);
				if (!list.isEmpty()) {
					msg.addAllPayMallList(list);
				}
			}
			humanObj.sendMsg(msg);
		} else {
			List<Define.p_pay_mall_info> list = to_p_pay_mall_info_list(humanObj, type);
			msg.addAllPayMallList(list);
			humanObj.sendMsg(msg);
		}
	}

	private List<Define.p_pay_mall_info> to_p_pay_mall_info_list(HumanObject humanObj, int type){
		switch (type) {
			case PayMallTypeKey.PAY_Type_7:
			case PayMallTypeKey.PAY_Type_29:{
				return to_p_pay_mall_info_list_act(humanObj, type);
			}
			case PayMallTypeKey.PAY_Type_12: {
				return to_p_pay_mall_info_list_12(humanObj);
			}
			case PayMallTypeKey.PAY_Type_20: {
				return to_p_pay_mall_info_list_20(humanObj);
			}
			case PayMallTypeKey.PAY_Type_21: {
				return new ArrayList<>();
			}
			case PayMallTypeKey.PAY_Type_23: {
				return to_p_pay_mall_info_list_23(humanObj);
			}
			case PayMallTypeKey.PAY_Type_25: {
				return to_p_pay_mall_info_list_25(humanObj);
			}
			case PayMallTypeKey.PAY_Type_26: {
				return to_p_pay_mall_info_list_26(humanObj);
			}
			case PayMallTypeKey.PAY_Type_27: {
				return to_p_pay_mall_info_list_27(humanObj);
			}
			case PayMallTypeKey.PAY_Type_28: {
				return to_p_pay_mall_info_list_28(humanObj);
			}
			case PayMallTypeKey.PAY_Type_5: {
				return to_p_pay_mall_info_list_5(humanObj);
			}
			case PayMallTypeKey.PAY_Type_30: {
				return to_p_pay_mall_info_list_30(humanObj);
			}
			default:
				return to_p_pay_mall_info_list_general(humanObj, type);
		}
	}

	private List<Define.p_pay_mall_info> to_p_pay_mall_info_list_general(HumanObject humanObj, int type){
		List<Define.p_pay_mall_info> list = new ArrayList<>();
		List<ConfPayMall> payMallList = GlobalConfVal.getConfPayMalls(type);
		if (payMallList == null || payMallList.isEmpty()) {
			Log.game.error("ConfPayMall===配表错误，type={}", type);
		}
		for(ConfPayMall confPayMall : payMallList){
			if(confPayMall.type != PayMallTypeKey.PAY_Type_3 && confPayMall.type != PayMallTypeKey.PAY_Type_8){
				if(confPayMall.pre_id == 0){
					continue;
				}
			}
			int buyNum = buyPayMallNum(humanObj, confPayMall.sn, confPayMall.reset);
			if(buyNum >= confPayMall.buy_times){
				if(confPayMall.next_id == 0){
					list.add(to_p_pay_mall_info(confPayMall.sn, confPayMall.type, buyNum, confPayMall.reset, rewardType_2));
					continue;
				}
				ConfPayMall confMallTemp = ConfPayMall.get(confPayMall.next_id);
				if(confMallTemp.next_id == 0){
					buyNum = buyPayMallNum(humanObj, confMallTemp.sn, confMallTemp.reset);
					list.add(to_p_pay_mall_info(confMallTemp.sn, confMallTemp.type, buyNum, confMallTemp.reset,
							buyNum >= confMallTemp.buy_times ? rewardType_2 : rewardType_1));
					continue;
				}
				int loopNum = payMallList.size();
				while (confMallTemp != null && buyPayMallNum(humanObj, confMallTemp.sn, confMallTemp.reset) >= confMallTemp.buy_times){
					confMallTemp = ConfPayMall.get(confMallTemp.next_id);
					if(confMallTemp.next_id == 0){
						buyNum = buyPayMallNum(humanObj, confMallTemp.sn, confMallTemp.reset);
						list.add(to_p_pay_mall_info(confMallTemp.sn, confMallTemp.type, buyNum, confMallTemp.reset,
								buyNum >= confMallTemp.buy_times ? rewardType_2 : rewardType_1));
						confMallTemp = null;
						break;
					}
					loopNum--;
					if(loopNum <= 0){
						break;
					}
				}
				if(confMallTemp != null){
					buyNum = buyPayMallNum(humanObj, confMallTemp.sn, confMallTemp.reset);
					list.add(to_p_pay_mall_info(confMallTemp.sn, confMallTemp.type, buyNum, confMallTemp.reset,
							buyNum >= confMallTemp.buy_times ? rewardType_2 : rewardType_1));
				}
			} else {
				list.add(to_p_pay_mall_info(confPayMall.sn, confPayMall.type, buyNum, confPayMall.reset,
						buyNum >= confPayMall.buy_times ? rewardType_2 : rewardType_1));
			}
		}
		return list;
	}

	private List<Define.p_pay_mall_info> to_p_pay_mall_info_list_25(HumanObject humanObj){
		List<Define.p_pay_mall_info> list = new ArrayList<>();
		Map<Integer,Integer> limitTimeGiftMap = getLimitTimeGifMap(humanObj);
		for (Map.Entry<Integer, Integer> entry : limitTimeGiftMap.entrySet()) {
			ConfPayMall confPayMall = ConfPayMall.get(entry.getKey());
			if (confPayMall != null && confPayMall.type == PayMallTypeKey.PAY_Type_25) {
				int buyNum = buyPayMallNum(humanObj, confPayMall.sn, confPayMall.reset);
				list.add(to_p_pay_mall_info(confPayMall.sn, confPayMall.type, buyNum, confPayMall.reset,
						buyNum >= confPayMall.buy_times ? rewardType_2 : rewardType_1, limitTimeGiftMap.get(confPayMall.sn)));
			}
		}
		return list;
	}

	private List<Define.p_pay_mall_info> to_p_pay_mall_info_list_act(HumanObject humanObj, int type){
		List<Define.p_pay_mall_info> list = new ArrayList<>();
		List<ConfPayMall> payMallList = GlobalConfVal.getConfPayMalls(type);
		if (payMallList == null || payMallList.isEmpty()) {
			Log.game.error("ConfPayMall===配表错误，type={}", type);
		}
		for (ConfPayMall confPayMall : payMallList) {
			//判断活动有没开启
			String[] actStr = Utils.splitStr(confPayMall.conditon, "\\|");
			if(actStr.length < 3){
				Log.game.error("ConfPayMall===配表错误，sn={}", confPayMall.sn);
				continue;
			}
			int actType = Utils.intValue(actStr[1]);
			String[] actStr2 = Utils.splitStr(actStr[2], ",");
			if(actStr2.length < 2){
				Log.game.error("ConfPayMall===配表错误，sn={}", confPayMall.sn);
				continue;
			}
			int roundMin = Utils.intValue(actStr2[0]);
			int roundMax = Utils.intValue(actStr2[1]);
			if(!ActivityManager.inst().isActivityOpen(humanObj, actType, roundMin, roundMax)){
				continue;
			}
			int buyNum = buyPayMallNum(humanObj, confPayMall.sn, confPayMall.reset);
			list.add(to_p_pay_mall_info(confPayMall.sn, confPayMall.type, buyNum, confPayMall.reset,
					buyNum >= confPayMall.buy_times ? rewardType_2 : rewardType_1));
		}
		return list;
	}

	private List<Define.p_pay_mall_info> to_p_pay_mall_info_list_20(HumanObject humanObj){
		List<Define.p_pay_mall_info> list = new ArrayList<>();
		List<ConfPayMall> payMallList = new ArrayList<>();
		payMallList.addAll(GlobalConfVal.getConfPayMalls(PayMallTypeKey.PAY_Type_20));
		payMallList.addAll(GlobalConfVal.getConfPayMalls(PayMallTypeKey.PAY_Type_21));
		boolean isAllBuy = true;
		for(ConfPayMall confPayMall : payMallList){
			int buyNum = buyPayMallNum(humanObj, confPayMall.sn, confPayMall.reset);
			isAllBuy = confPayMall.type == PayMallTypeKey.PAY_Type_20 && isAllBuy && buyNum >= 1;
			boolean inBuyTimes = confPayMall.buy_times <= 0 || buyNum < confPayMall.buy_times;
			int rewardType = inBuyTimes && isPayConditon(humanObj,confPayMall) ? rewardType_0 : rewardType_2;
			if(confPayMall.type == PayMallTypeKey.PAY_Type_21){
				rewardType = buyNum == 0 ? (rewardType == rewardType_2 ? rewardType_0 : rewardType_1) : rewardType_2;
			}
			list.add(to_p_pay_mall_info(confPayMall.sn, confPayMall.type, buyNum, confPayMall.reset,rewardType));
		}
		if(isAllBuy){
			return new ArrayList<>();
		}
		return list;
	}

	private List<Define.p_pay_mall_info> to_p_pay_mall_info_list_12(HumanObject humanObj) {
		List<ConfPayMall> payMallList = GlobalConfVal.getConfPayMalls(PayMallTypeKey.PAY_Type_12);
		Map<Integer,Integer> limitTimeGiftMap = getLimitTimeGifMap(humanObj);
		List<Define.p_pay_mall_info> list = new ArrayList<>();
		for (ConfPayMall confPayMall : payMallList) {
			int buyNum = buyPayMallNum(humanObj, confPayMall.sn, confPayMall.reset);
			int rewardType = isPayCondition(humanObj, confPayMall, true) ? rewardType_1 : rewardType_0;
			rewardType = (confPayMall.buy_times <= 0 || buyNum < confPayMall.buy_times) ? rewardType : rewardType_2;
			list.add(to_p_pay_mall_info(confPayMall.sn, confPayMall.type, buyNum, confPayMall.reset, rewardType, limitTimeGiftMap.getOrDefault(confPayMall.sn, 0)));
		}
		return list;
	}

	private List<Define.p_pay_mall_info> to_p_pay_mall_info_list_23(HumanObject humanObj) {
		// 限时福利
		List<ConfPayMall> payMallList = GlobalConfVal.getConfPayMalls(PayMallTypeKey.PAY_Type_23);
		if (payMallList == null || payMallList.isEmpty()) {
			return new ArrayList<>(0);
		}
		// 所有23类型礼包的最顶层礼包
		Set<Integer> confPayFirstList = payMallList.stream().map(confPayMall -> confPayMall.parameter).collect(Collectors.toSet());

		Map<Integer,Integer> limitTimeGiftMap = getLimitTimeGifMap(humanObj);
		List<Define.p_pay_mall_info> list = new ArrayList<>();
		for (Integer key : confPayFirstList) {
			ConfPayMall confPayFirst = ConfPayMall.get(key);
			if (confPayFirst == null) {
				Log.game.error("ConfPayMall===配表错误，sn={}", key);
				continue;
			}
			boolean payFirstBuy = isPayCondition(humanObj, confPayFirst, true);
			if (!payFirstBuy) {
				// 顶层礼包的购买条件不满足，说明活动或者什么都关了，也不需要再给客户端发消息
				continue;
			}
			for (ConfPayMall confPayMall : payMallList) {
				if (confPayMall.parameter == confPayFirst.parameter) {
					int buyNum = buyPayMallNum(humanObj, confPayMall.sn, confPayMall.reset);
					int rewardType = (confPayFirst == confPayMall || isPayCondition(humanObj, confPayMall, true)) ? rewardType_1 : rewardType_0;
					rewardType = (confPayMall.buy_times <= 0 || buyNum < confPayMall.buy_times) ? rewardType : rewardType_2;
					list.add(to_p_pay_mall_info(confPayMall.sn, confPayMall.type, buyNum, confPayMall.reset, rewardType, limitTimeGiftMap.getOrDefault(confPayFirst.sn, 0)));
				}
			}
		}
		return list;
	}

	/**
	 * 26类型礼包
	 */
	private List<Define.p_pay_mall_info> to_p_pay_mall_info_list_26(HumanObject humanObj) {
		List<Define.p_pay_mall_info> list = new ArrayList<>();
		Map<Integer, Integer> limitTimeGiftMap = getLimitTimeGifMap(humanObj);
		for (Map.Entry<Integer, Integer> entry : limitTimeGiftMap.entrySet()) {
			int sn = entry.getKey();
			int expireTime = entry.getValue();
			ConfPayMall conf = ConfPayMall.get(sn);
			if (conf == null) {
				Log.shop.error("ConfPayMall表查询不到，sn={}", sn);
				continue;
			}
			if (conf.type != PayMallTypeKey.PAY_Type_26) {
				continue;
			}
			int buyNum = buyPayMallNum(humanObj, conf.sn, conf.reset);
			int rewardType = buyNum >= conf.buy_times ? rewardType_2 : rewardType_1;
			if (rewardType != rewardType_1) {
				continue;
			}
			list.add(to_p_pay_mall_info(sn, conf.type, buyNum, conf.reset, rewardType, expireTime));
		}
		return list;
	}

	/**
	 * 27类型礼包
	 */
	private List<Define.p_pay_mall_info> to_p_pay_mall_info_list_27(HumanObject humanObj) {
		List<Define.p_pay_mall_info> list = new ArrayList<>();
		Map<Integer, Integer> limitTimeGiftMap = getLimitTimeGifMap(humanObj);
		for (Map.Entry<Integer, Integer> entry : limitTimeGiftMap.entrySet()) {
			int sn = entry.getKey();
			int expireTime = entry.getValue();
			ConfPayMall conf = ConfPayMall.get(sn);
			if (conf == null) {
				Log.shop.error("ConfPayMall表查询不到，sn={}", sn);
				continue;
			}
			if (conf.type != PayMallTypeKey.PAY_Type_27) {
				continue;
			}
			int buyNum = buyPayMallNum(humanObj, conf.sn, conf.reset);
			int rewardType = buyNum >= conf.buy_times ? rewardType_2 : rewardType_1;
			list.add(to_p_pay_mall_info(sn, conf.type, buyNum, conf.reset, rewardType, expireTime));
		}
		return list;
	}

	/**
	 * 28类型应援礼包
	 */
	private List<Define.p_pay_mall_info> to_p_pay_mall_info_list_28(HumanObject humanObj) {
		List<ConfPayMall> payMallList = GlobalConfVal.getConfPayMalls(PayMallTypeKey.PAY_Type_28);
		if (payMallList == null || payMallList.isEmpty()) {
			return new ArrayList<>(0);
		}

		Map<Integer,Integer> limitTimeGiftMap = getLimitTimeGifMap(humanObj);
		List<Define.p_pay_mall_info> list = new ArrayList<>(0);
		// 所有28类型礼包的最顶层礼包
		Set<Integer> confPayFirstList = payMallList.stream().map(confPayMall -> confPayMall.parameter).collect(Collectors.toSet());
		for (Integer key : confPayFirstList) {
			ConfPayMall confPayFirst = ConfPayMall.get(key);
			if (confPayFirst == null) {
				Log.game.error("ConfPayMall===配表错误，sn={}", key);
				continue;
			}
			boolean payFirstBuy = isPayCondition(humanObj, confPayFirst, true);
			if (!payFirstBuy) {
				// 顶层礼包的购买条件不满足，说明活动或者什么都关了，也不需要再给客户端发消息
				continue;
			}
			for (ConfPayMall confPayMall : payMallList) {
				if (confPayMall.parameter == confPayFirst.parameter) {
					int buyNum = buyPayMallNum(humanObj, confPayMall.sn, confPayMall.reset);
					int rewardType = (confPayFirst == confPayMall || isPayCondition(humanObj, confPayMall, true)) ? rewardType_1 : rewardType_0;
					rewardType = (confPayMall.buy_times <= 0 || buyNum < confPayMall.buy_times) ? rewardType : rewardType_2;
					list.add(to_p_pay_mall_info(confPayMall.sn, confPayMall.type, buyNum, confPayMall.reset, rewardType, limitTimeGiftMap.getOrDefault(confPayFirst.sn, 0)));
				}
			}
		}
		return list;
	}

	/**
	 * 回归礼包
	 * @param humanObj
	 * @return
	 */
	private List<Define.p_pay_mall_info> to_p_pay_mall_info_list_5(HumanObject humanObj) {
		List<ConfPayMall> payMallList = GlobalConfVal.getConfPayMalls(PayMallTypeKey.PAY_Type_5);
		List<Define.p_pay_mall_info> list = new ArrayList<>();
		for (ConfPayMall confPayMall : payMallList) {
			if(confPayMall.pre_id == 0){
				continue;
			}
			String[] conditions = GlobalConfVal.payConditionsMap.get(confPayMall.sn);
			if(!Objects.equals(conditions[0], PayMallTypeKey.pay_condition_return)){
				// type=5的只推送回归礼包
				continue;
			}
			int buyNum = buyPayMallNum(humanObj, confPayMall.sn, confPayMall.reset);
			int rewardType = isPayCondition(humanObj, confPayMall, true) ? rewardType_1 : rewardType_0;
			rewardType = (confPayMall.buy_times <= 0 || buyNum < confPayMall.buy_times) ? rewardType : rewardType_2;
			list.add(to_p_pay_mall_info(confPayMall.sn, confPayMall.type, buyNum, confPayMall.reset, rewardType));
		}
		return list;
	}

	public List<Define.p_pay_mall_info> to_p_pay_mall_info_list_30(HumanObject humanObj) {
		// 多档刷新礼包
		List<ConfPayMall> payMallList = GlobalConfVal.getConfPayMalls(PayMallTypeKey.PAY_Type_30);
		if (payMallList == null || payMallList.isEmpty()) {
			return new ArrayList<>(0);
		}
		// 所有30类型礼包的最顶层礼包
		Set<Integer> confPayFirstList = payMallList.stream().map(confPayMall -> confPayMall.parameter).collect(Collectors.toSet());
		Map<Integer, Integer> limitTimeGiftMap = getLimitTimeGifMap(humanObj);
		List<Define.p_pay_mall_info> list = new ArrayList<>();
		for (Integer key : confPayFirstList) {
			ConfPayMall confPayFirst = ConfPayMall.get(key);
			if (confPayFirst == null) {
				Log.game.error("ConfPayMall===配表错误，sn={}", key);
				continue;
			}
			boolean payFirstBuy = isPayCondition(humanObj, confPayFirst, true);
			if (!payFirstBuy) {
				// 顶层礼包的购买条件不满足，说明活动或者什么都关了，也不需要再给客户端发消息
				continue;
			}
			for (ConfPayMall confPayMall : payMallList) {
				if (confPayMall.parameter != confPayFirst.parameter) {
					continue;
				}
				int buyNum = buyPayMallNum(humanObj, confPayMall.sn, confPayMall.reset);
				int rewardType = (confPayFirst == confPayMall || isPayCondition(humanObj, confPayMall, true)) ? rewardType_1 : rewardType_0;
				rewardType = (confPayMall.buy_times <= 0 || buyNum < confPayMall.buy_times) ? rewardType : rewardType_2;
				list.add(to_p_pay_mall_info(confPayMall.sn, confPayMall.type, buyNum, confPayMall.reset, rewardType, limitTimeGiftMap.getOrDefault(confPayFirst.sn, 0)));
			}
		}
		return list;
	}

	public void sendMsg_pay_mall_info_s2c(HumanObject humanObj){
		// TODO

		sendMsg_pay_mall_info_s2c(humanObj, 0);
	}

	public Define.p_pay_mall_info to_p_pay_mall_info(int sn, int type, int boughtTimes, int resetType, int isReward){
		Define.p_pay_mall_info.Builder dInfo = Define.p_pay_mall_info.newBuilder();
		dInfo.setBundleId(sn);
		dInfo.setType(type);
		dInfo.setBoughtTimes(boughtTimes);
		if(resetType == PayMallTypeKey.resetType_1){// 每日零点
			dInfo.setEndTime(Utils.getOffDayTime(Port.getTime(), 1, 0) / Time.SEC);
		} else if(resetType == PayMallTypeKey.resetType_2){// 每周一零点
			dInfo.setEndTime(Utils.getNextTime(Port.getTime(), 2, 0) / Time.SEC);
		}
		dInfo.setIsReward(isReward);
		return dInfo.build();
	}

	public Define.p_pay_mall_info to_p_pay_mall_info(int sn, int type, int boughtTimes, int resetType, int isReward, int endTime){
		Define.p_pay_mall_info.Builder dInfo = Define.p_pay_mall_info.newBuilder();
		dInfo.setBundleId(sn);
		dInfo.setType(type);
		dInfo.setBoughtTimes(boughtTimes);
		dInfo.setEndTime(endTime);
		dInfo.setIsReward(isReward);
		return dInfo.build();
	}

	public void sendActivityPayMallUpdate(HumanObject humanObj, int actType){
		List<ConfPayMall> payMallList = GlobalConfVal.getConfPayMalls(PayMallTypeKey.PAY_Type_7);
		for (ConfPayMall confPayMall : payMallList){
			if(Utils.intValue(Utils.splitStr(confPayMall.conditon, "\\|")[1]) == actType){
				int buyNum = buyPayMallNum(humanObj, confPayMall.sn, confPayMall.reset);
				MsgPayMall.pay_mall_update_s2c.Builder msg = MsgPayMall.pay_mall_update_s2c.newBuilder();
				msg.setPayMall(to_p_pay_mall_info(confPayMall.sn, confPayMall.type, buyNum, confPayMall.reset,
						buyNum >= confPayMall.buy_times ? rewardType_2 : rewardType_1));
				humanObj.sendMsg(msg);
			}
		}

	}


	public void _msg_pay_mall_reward_c2s(HumanObject humanObj, int paySn) {
		ConfPayMall confPayMall = ConfPayMall.get(paySn);
		if(confPayMall == null){
			return;
		}
		List<Define.p_reward> rewardList = new ArrayList<>();
		if (!pay_mall_reward_c2s(humanObj, confPayMall, rewardList)) {
			return;
		}
		int showType = InstanceConstants.showType_0;
		List<Define.p_key_value> extList = new ArrayList<>(0);
		if (confPayMall.type == PayMallTypeKey.PAY_Type_27) {
			showType = InstanceConstants.showType_1001008;
			buyDiscountGift27(humanObj, paySn, rewardList, extList);
		}
		InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, showType, rewardList, extList);
	}
	public boolean pay_mall_reward_c2s(HumanObject humanObj, ConfPayMall confPayMall, List<Define.p_reward> pRewardList) {
		if (confPayMall.price > 0) {
			return false;
		}
		if (!isPayConditon(humanObj, confPayMall)) {
			Log.temp.error("===充值条件不满足，humanId={}, paySn={}", humanObj.id, confPayMall.sn);
			return false;
		}
		int buyNumTemp = -1;
		if (confPayMall.buy_times > 0) {
			int resetType = confPayMall.reset;
			int buyNum = buyPayMallNum(humanObj, confPayMall.sn, resetType);
			if (buyNum >= confPayMall.buy_times) {
				return false;
			}
			updatePayMallBuyNum(humanObj, confPayMall.sn, resetType);
			humanObj.saveDailyResetRecord();
			buyNumTemp = buyPayMallNum(humanObj, confPayMall.sn, confPayMall.reset);
		}
		Map<Integer, Integer> reward = Utils.intArrToIntMap(new HashMap<>(), confPayMall.reward);
		if (reward.size() != 0) {
			ProduceManager.inst().produceAdd(humanObj, reward, MoneyItemLogKey.充值免费奖励);
			sendMsg_pay_mall_reward_s2c(humanObj, confPayMall.sn);
			pRewardList.addAll(InstanceManager.inst().to_p_rewardList(reward));
		}

		int buyNum = buyPayMallNum(humanObj, confPayMall.sn, confPayMall.reset);
		if(confPayMall.type == PayMallTypeKey.PAY_Type_25 && confPayMall.show == 0){
			// 限时礼包
			Map<Integer,Integer> limitTimeGiftMap = getLimitTimeGifMap(humanObj);
			MsgPayMall.pay_mall_update_s2c.Builder msg = MsgPayMall.pay_mall_update_s2c.newBuilder();
			msg.setPayMall(to_p_pay_mall_info(confPayMall.sn, confPayMall.type, buyNumTemp, confPayMall.reset,
					buyNum >= confPayMall.buy_times ? rewardType_2 : rewardType_1, limitTimeGiftMap.getOrDefault(confPayMall.sn,0)));
			humanObj.sendMsg(msg);
		} else if (confPayMall.type == PayMallTypeKey.PAY_Type_12) {
			String[] conditions = GlobalConfVal.payConditionsMap.get(confPayMall.sn);
			int actType = Utils.intValue(conditions[0]);
			int roundMin = Utils.intValue(conditions[1]);
			int roundMax = Utils.intValue(conditions[2]);
			if (!ActivityManager.inst().isActivityOpen(humanObj, actType, roundMin, roundMax)) {
				return false;
			}
			payCustomMall(humanObj, confPayMall.sn, pRewardList);
		}else if(confPayMall.type == PayMallTypeKey.PAY_Type_23){
			sendMsg_pay_mall_info_s2c(humanObj, PayMallTypeKey.PAY_Type_23);
		} else if(buyNum >= confPayMall.buy_times) {
			ConfPayMall confMallTemp = ConfPayMall.get(confPayMall.next_id);
			if (confMallTemp != null) {
				MsgPayMall.pay_mall_update_s2c.Builder msg = MsgPayMall.pay_mall_update_s2c.newBuilder();
				msg.setPayMall(to_p_pay_mall_info(confMallTemp.sn, confMallTemp.type, Utils.intValue(confMallTemp.get(confMallTemp.sn)),
						confMallTemp.reset, buyNum >= confPayMall.buy_times ? rewardType_2 : rewardType_1));
				humanObj.sendMsg(msg);
			} else {
				MsgPayMall.pay_mall_update_s2c.Builder msg = MsgPayMall.pay_mall_update_s2c.newBuilder();
				msg.setPayMall(to_p_pay_mall_info(confPayMall.sn, confPayMall.type, buyNumTemp, confPayMall.reset,
						buyNum >= confPayMall.buy_times ? rewardType_2 : rewardType_1));
				humanObj.sendMsg(msg);
			}
		} else {
			MsgPayMall.pay_mall_update_s2c.Builder msg = MsgPayMall.pay_mall_update_s2c.newBuilder();
			msg.setPayMall(to_p_pay_mall_info(confPayMall.sn, confPayMall.type, buyNumTemp, confPayMall.reset,
					buyNum >= confPayMall.buy_times ? rewardType_2 : rewardType_1));
			humanObj.sendMsg(msg);
		}
		return true;
	}

	public void _msg_pay_mall_rewards_c2s(HumanObject humanObj, List<Integer> bundleIdList) {
		List<Define.p_reward> totalRewards = new ArrayList<>();
		for (int bundleId : bundleIdList) {
			ConfPayMall confPayMall = ConfPayMall.get(bundleId);
			if(confPayMall == null){
				continue;
			}
			if(!pay_mall_reward_c2s(humanObj, confPayMall, totalRewards)){
				continue;
			}
		}
		InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, totalRewards);
	}

	private void sendMsg_pay_mall_reward_s2c(HumanObject humanObj, int paySn){
		MsgPayMall.pay_mall_reward_s2c.Builder msg = MsgPayMall.pay_mall_reward_s2c.newBuilder();
		msg.setBundleId(paySn);
		humanObj.sendMsg(msg);
	}

	public boolean isBuyPayMall(HumanObject humanObj, int paySn){
		ConfPayMall confPayMall = ConfPayMall.get(paySn);
		if(confPayMall == null){
			return false;
		}
		int buyNum = buyPayMallNum(humanObj, paySn, confPayMall.reset);
		return buyNum > 0;
	}

	public int buyPayMallNum(HumanObject humanObj, int paySn, int resetType){
		int buyNum = 0;
		if(resetType == PayMallTypeKey.resetType_0){
			String json = humanObj.operation.mall.getPayMallBuyNumMap();
			Map<Integer, Integer> snBuyNumMap = Utils.jsonToMapIntInt(json);
			buyNum = Utils.intValue(snBuyNumMap.get(paySn));
		} else if(resetType == PayMallTypeKey.resetType_1){
			// 每日充值
			HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyPayMall.getType());
			Map<Integer, Integer> snBuyNumMap = Utils.jsonToMapIntInt(info.getParam());
			buyNum = Utils.intValue(snBuyNumMap.get(paySn));
		} else if(resetType == PayMallTypeKey.resetType_2){
			// 每周充值
			HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.weekPayMall.getType());
			Map<Integer, Integer> snBuyNumMap = Utils.jsonToMapIntInt(info.getParam());
			buyNum = Utils.intValue(snBuyNumMap.get(paySn));
		} else {
			Log.temp.error("===未实现代码，resetType={}, paySn={}", resetType, paySn);
		}
		return buyNum;
	}

	private int buyMallNum(HumanObject humanObj, int paySn, int resetType, int shopType){
		int buyNum = 0;
		if(resetType == PayMallTypeKey.resetType_0){
			Map<Integer, Map<Integer, Integer>> typeBuyNumMap = Utils.jsonToIntMapIntInt(humanObj.operation.mall.getMallBuyNumMap());
			Map<Integer, Integer> snBuyNumMap = typeBuyNumMap.getOrDefault(shopType,new HashMap<>());
			buyNum = snBuyNumMap.getOrDefault(paySn, 0);
		} else if(resetType == PayMallTypeKey.resetType_1){
			// 每日商店
			HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyMall.getType());
			Map<Integer, Integer> snBuyNumMap = Utils.jsonToMapIntInt(info.getParam());
			buyNum = snBuyNumMap.getOrDefault(paySn, 0);
		} else if(resetType == PayMallTypeKey.resetType_2){
			// 每周商店
			HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.weekMall.getType());
			Map<Integer, Integer> snBuyNumMap = Utils.jsonToMapIntInt(info.getParam());
			buyNum = snBuyNumMap.getOrDefault(paySn, 0);
		} else {
			Log.temp.error("===未实现代码，resetType={}, paySn={}", resetType, paySn);
		}
		return buyNum;
	}

	public void updatePayMallBuyNum(HumanObject humanObj, int paySn, int resetType){
		if(resetType == PayMallTypeKey.resetType_0){
			String json = humanObj.operation.mall.getPayMallBuyNumMap();
			Map<Integer, Integer> snBuyNumMap = Utils.jsonToMapIntInt(json);
			int buyNum = Utils.intValue(snBuyNumMap.get(paySn)) + 1;
			snBuyNumMap.put(paySn, buyNum);
			humanObj.operation.mall.setPayMallBuyNumMap(Utils.mapIntIntToJSON(snBuyNumMap));
			humanObj.operation.mall.update();
		} else if(resetType == PayMallTypeKey.resetType_1){
			// 每日充值
			HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyPayMall.getType());
			Map<Integer, Integer> snBuyNumMap = Utils.jsonToMapIntInt(info.getParam());
			int buyNum = Utils.intValue(snBuyNumMap.get(paySn)) + 1;
			snBuyNumMap.put(paySn, buyNum);
			info.setParam(Utils.mapIntIntToJSON(snBuyNumMap));
			humanObj.saveDailyResetRecord();
		} else if(resetType == PayMallTypeKey.resetType_2){
			// 每周充值
			HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.weekPayMall.getType());
			Map<Integer, Integer> snBuyNumMap = Utils.jsonToMapIntInt(info.getParam());
			int buyNum = Utils.intValue(snBuyNumMap.get(paySn)) + 1;
			snBuyNumMap.put(paySn, buyNum);
			info.setParam(Utils.mapIntIntToJSON(snBuyNumMap));
			humanObj.saveDailyResetRecord();
		} else {
			Log.temp.error("===未实现代码，resetType={}, paySn={}", resetType, paySn);
		}
	}

	public void clearPayMallBuyNum(HumanObject humanObj, int paySn){
		String json = humanObj.operation.mall.getPayMallBuyNumMap();
		Map<Integer, Integer> snBuyNumMap = Utils.jsonToMapIntInt(json);
		if(snBuyNumMap.getOrDefault(paySn, 0) == 0){
			return;
		}
		Log.game.info("玩家：{},清除购买记录paySn={}",humanObj.id, paySn);
		snBuyNumMap.put(paySn, 0);
		humanObj.operation.mall.setPayMallBuyNumMap(Utils.mapIntIntToJSON(snBuyNumMap));
	}

	public void _msg_role_pay_check_c2s(HumanObject humanObj, int bundleId, int price, int time, int isMoney999) {
		MsgPayMall.role_pay_check_s2c.Builder msg = MsgPayMall.role_pay_check_s2c.newBuilder();
		msg.setBundleId(bundleId);
		msg.setTime(time);
		msg.setIsMoney999(isMoney999);
		msg.setResult(0);
		ConfPayMall confPayMall = ConfPayMall.get(bundleId);
		if (confPayMall == null) {
			msg.setResult(ErrorTip.SystemDefault);
			humanObj.sendMsg(msg);
			return;
		}
		if (confPayMall.conditon != null && !"".equals(confPayMall.conditon) && !isPayConditon(humanObj, confPayMall)) {
			msg.setResult(ErrorTip.RewardNotMeetCondition);
			humanObj.sendMsg(msg);
			return;
		}
		if (confPayMall.type == PayMallTypeKey.PAY_Type_23) {
			// 天降鸿福礼包支付的时候去检测顶层礼包是否还可以购买，针对活动结束
			ConfPayMall confTopPayMall = ConfPayMall.get(confPayMall.parameter);
			if (confTopPayMall != null && !isPayConditon(humanObj, confTopPayMall)) {
				msg.setResult(ErrorTip.RewardNotMeetCondition);
				humanObj.sendMsg(msg);
				Log.activity.error("天降鸿福礼包购买前检测拦截, 顶层礼包条件不满足, humanId={}, sn={}, topSn={}", humanObj.id, bundleId, confPayMall.parameter);
				return;
			}
		}
		if (confPayMall.type == PayMallTypeKey.PAY_Type_29) {
			// 连冲
			int[] ints = GlobalConfVal.actPayMallConditionMap.get(bundleId);
			if (ints == null) {
				msg.setResult(ErrorTip.SystemDefault);
				humanObj.sendMsg(msg);
				return;
			}
			int actType = ints[0];
			IActivityControl control = ActivityControlTypeFactory.getTypeData(actType);
			if (control == null) {
				msg.setResult(ErrorTip.SystemDefault);
				humanObj.sendMsg(msg);
				return;
			}
			if(!control.canPay(humanObj, confPayMall)){
				msg.setResult(ErrorTip.SystemDefault);
				humanObj.sendMsg(msg);
				return;
			}
		}
		if (confPayMall.buy_times != 0) {
			int buyNum = buyPayMallNum(humanObj, bundleId, confPayMall.reset);
			if (buyNum >= confPayMall.buy_times) {
				msg.setResult(ErrorTip.PayFullBuyTime);
				humanObj.sendMsg(msg);
				return;
			}
		}
		humanObj.sendMsg(msg);
	}

	public boolean payMall(HumanObject humanObj, int paySn, List<Define.p_reward> p_rewards, boolean isFake){
		ConfPayMall confPayMall = ConfPayMall.get(paySn);
		if(confPayMall == null){
			Log.temp.error("===ConfPayMall配表错误，not find sn={}", paySn);
			return false;
		}

		if (!isPayConditon(humanObj, confPayMall)) {
			// 充值到账时，如果检测失败不管，仍然给奖励
			Log.temp.error("===充值条件不满足，humanId={}, paySn={}, type={}", humanObj.id, paySn, confPayMall.type);
//			sendMsg_pay_mall_update_s2c(humanObj, paySn, confPayMall);
//			return false;
		}
		Human3 human = humanObj.getHuman3();
		if(confPayMall.type != PayMallTypeKey.PAY_Type_8){
			addVipPoint(humanObj,confPayMall.price);
		}

		if(confPayMall.buy_times > 0 || confPayMall.type == PayMallTypeKey.PAY_Type_3 || confPayMall.type == PayMallTypeKey.PAY_Type_8){
			int resetType = confPayMall.reset;
			int buyNum = buyPayMallNum(humanObj, paySn, resetType);
			if(buyNum >= confPayMall.buy_times && confPayMall.buy_times > 0){
				Log.temp.error("===已经购买过该商城，humanId={}, paySn={}, type={}, buyNum={}", humanObj.id, paySn, confPayMall.type, buyNum);
				Inform.sendMsg_error(humanObj, 366);// 次数已经用完
				sendMsg_pay_mall_update_s2c(humanObj, paySn, confPayMall);
				// TODO 次数用完补奖励
				ProduceManager.inst().produceAdd(humanObj, confPayMall.reissueNum, MoneyItemLogKey.充值超次数补奖励);
				InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, InstanceManager.inst().to_p_rewardList(confPayMall.reissueNum));
				return false;
			}
			updatePayMallBuyNum(humanObj, paySn, resetType);
		}
		ProduceManager.inst().produceAdd(humanObj, confPayMall.reward, MoneyItemLogKey.充值);
		p_rewards.addAll(InstanceManager.inst().to_p_rewardList(confPayMall.reward));
		sendMsg_pay_mall_update_s2c(humanObj, paySn, confPayMall);

		//添加充值日志
		LogOp.log(LogOpChannel.RECHARGE,
				human.getId(),
				Port.getTime(),
				Utils.formatTime(Port.getTime(), "yyyy-MM-dd"),
				confPayMall.price,
				0,
				humanObj.getHuman().getAccount(),
				humanObj.getHuman().getName(),
				humanObj.getHuman().getLevel(),
				humanObj.getHuman().getServerId(),
				PF_PAY_Manager.payType1,
				isFake ? 1 : 0,
				confPayMall.desc,
				confPayMall.ios_pid
		);
		Log.temp.error("===充值成功payMall表，humanId={}, account={}, name={}, price={}, sn={}, type={}",
				humanObj.id, humanObj.getHuman().getAccount(), humanObj.getHuman().getName(), confPayMall.price, paySn, confPayMall.type);
		return true;
	}

	private void sendMsg_pay_mall_update_s2c(HumanObject humanObj, int paySn, ConfPayMall confPayMall){
		int buyNum = buyPayMallNum(humanObj, paySn, confPayMall.reset);
		if (confPayMall.type == PayMallTypeKey.PAY_Type_25 && confPayMall.show == 0)  {
			// 限时礼包
			Map<Integer, Integer> limitTimeGiftMap = getLimitTimeGifMap(humanObj);
			MsgPayMall.pay_mall_update_s2c.Builder msg = MsgPayMall.pay_mall_update_s2c.newBuilder();
			msg.setPayMall(to_p_pay_mall_info(confPayMall.sn, confPayMall.type, buyNum, confPayMall.reset,
					buyNum >= confPayMall.buy_times ? rewardType_2 : rewardType_1, limitTimeGiftMap.getOrDefault(confPayMall.sn, 0)));
			humanObj.sendMsg(msg);
			return;
		}
		if (buyNum >= confPayMall.buy_times) {
			ConfPayMall confMallTemp = ConfPayMall.get(confPayMall.next_id);
			if (confMallTemp != null) {
				MsgPayMall.pay_mall_update_s2c.Builder msg = MsgPayMall.pay_mall_update_s2c.newBuilder();
				msg.setPayMall(to_p_pay_mall_info(confMallTemp.sn, confMallTemp.type, Utils.intValue(confMallTemp.get(confMallTemp.sn)),
						confMallTemp.reset, buyNum >= confPayMall.buy_times ? rewardType_2 : rewardType_1));
				humanObj.sendMsg(msg);
			} else {
				Define.p_pay_mall_info dInfo;
				if (confPayMall.push_continue != 0) {
					Map<Integer, Integer> limitTimeGiftMap = getLimitTimeGifMap(humanObj);
					int endTime = limitTimeGiftMap.getOrDefault(confPayMall.sn, 0);
					dInfo = to_p_pay_mall_info(confPayMall.sn, confPayMall.type, buyNum, confPayMall.reset,  rewardType_2, endTime);
				} else {
					dInfo = to_p_pay_mall_info(confPayMall.sn, confPayMall.type, buyNum, confPayMall.reset, rewardType_2);
				}
				MsgPayMall.pay_mall_update_s2c.Builder msg = MsgPayMall.pay_mall_update_s2c.newBuilder();
				msg.setPayMall(dInfo);
				humanObj.sendMsg(msg);
			}
		} else {
			Define.p_pay_mall_info dInfo;
			if (confPayMall.push_continue != 0) {
				Map<Integer, Integer> limitTimeGiftMap = getLimitTimeGifMap(humanObj);
				int endTime = limitTimeGiftMap.getOrDefault(confPayMall.sn, 0);
				dInfo = to_p_pay_mall_info(confPayMall.sn, confPayMall.type, buyNum, confPayMall.reset,  rewardType_1, endTime);
			} else {
				dInfo = to_p_pay_mall_info(confPayMall.sn, confPayMall.type, buyNum, confPayMall.reset, rewardType_1);
			}
			MsgPayMall.pay_mall_update_s2c.Builder msg = MsgPayMall.pay_mall_update_s2c.newBuilder();
			msg.setPayMall(dInfo);
			humanObj.sendMsg(msg);
		}
	}

	/** 
	 * 是否满足充值条件
	 * <AUTHOR>
	 * @Date 2024/4/23
	 * @Param
	 */
	private boolean isPayConditon(HumanObject humanObj, ConfPayMall confPayMall){
		if(confPayMall.conditon == null || confPayMall.conditon.isEmpty()){
			return true;
		}
		String[] strArr = GlobalConfVal.payConditionsMap.get(confPayMall.sn);
		switch (strArr[0]) {
			case PayMallTypeKey.pay_condition_open_day:
				long openSerTime = Util.getOpenServerTime(humanObj.getHuman().getServerId());
				int day = Utils.getDaysBetween(Port.getTime(), openSerTime);
				if(day+1 >= Utils.intValue(strArr[1])){
					return true;
				}
				break;
			case PayMallTypeKey.pay_condition_act_type: {
				int[] ints = GlobalConfVal.actPayMallConditionMap.get(confPayMall.sn);
				if (ints != null) {
					int actType = ints[0];
					int roundMin = ints[1];
					int roundMax = ints[2];
					if (ActivityManager.inst().isActivityOpen(humanObj, actType, roundMin, roundMax)) {
						return true;
					}
				}
			}	break;
			case PayMallTypeKey.pay_condition_dungeon:
				int value = InstanceManager.inst().getRenTypeValue(humanObj, Utils.intValue(strArr[1]));
				if(value > Utils.intValue(strArr[2])){
					return true;
				}
				break;
			case PayMallTypeKey.pay_condition_battlepass:
				return ActivityManager.inst().isActivityOpen(humanObj,ActivityControlType.Act_2100);
			case PayMallTypeKey.pay_condition_fairybattlepass:
				break;
			case PayMallTypeKey.pay_condition_wartoken:
				ConfWartoken confWartoken = ConfWartoken.get(Utils.intValue(strArr[1]));
				return confWartoken != null && ActivityManager.inst().isActivityOpen(humanObj,confWartoken.act_type);
			case PayMallTypeKey.pay_condition_guard:
				return humanObj.getHuman2().getRepSn() > Utils.intValue(strArr[1]);
			case PayMallTypeKey.pay_condition_pay_reward:
				String[] paySnArr = Utils.splitStr(strArr[1], "\\,");
				Map<Integer,Integer> firstChargeMap = Utils.jsonToMapIntInt(humanObj.operation.mall.getFirstPayGiftTimeMap());
				int buyTime = firstChargeMap.getOrDefault(Utils.intValue(paySnArr[0]),firstChargeMap.getOrDefault(Utils.intValue(paySnArr[1]),0));
				if(buyTime == 0){
					return false;
				}
				day = Utils.getDaysBetween(Port.getTime(), buyTime*Time.SEC);
				if (day+1 < Utils.intValue(strArr[2])) {
					return false;
				}
				return true;
			case PayMallTypeKey.pay_condition_pay_exclude:
				String[] paySnStrArr = Utils.splitStr(strArr[1], "\\,");
				for(String paySnStr : paySnStrArr){
					int paySn = Utils.intValue(paySnStr);
					if(buyPayMallNum(humanObj, paySn, confPayMall.reset) >= 1){
						return false;
					}
				}
				return true;
			case PayMallTypeKey.pay_condition_get_reward:
				int prePaySn = Utils.intValue(strArr[1]);
				ConfPayMall confPayMallPre = ConfPayMall.get(prePaySn);
				if (confPayMallPre == null) {
					return false;
				}
				if (confPayMallPre.type == PayMallTypeKey.PAY_Type_30) {
					// 多档刷新礼包要根据活动数据判断
					int confPayMallTopSn = confPayMallPre.parameter;
					int[] conditions = GlobalConfVal.actPayMallConditionMap.get(confPayMallTopSn);
					int actType = conditions[0];
					IActivityControl icontrol = ActivityControlTypeFactory.getTypeData(actType);
					if (!(icontrol instanceof ActivityControlRefreshGift)) {
						Log.activity.error("多档刷新礼包配置错误，actType={}, confPayMallTopSn={}", actType, confPayMallTopSn);
						return false;
					}
					ActivityControlRefreshGift control = (ActivityControlRefreshGift) icontrol;
					if (!control.isPayAllGet(humanObj, prePaySn)) {
						return false;
					}
				} else {
					if (buyPayMallNum(humanObj, prePaySn, confPayMallPre.reset) == 0) {
						return false;
					}
				}
				return true;
			case PayMallTypeKey.pay_condition_shortage:
            case PayMallTypeKey.pay_condition_flyEgg:
			case PayMallTypeKey.pay_condition_fashionSkin:
            case PayMallTypeKey.pay_condition_draw:
			case PayMallTypeKey.pay_condition_any_shortage:
			case PayMallTypeKey.pay_condition_common:{
				return true;
			}
			case PayMallTypeKey.pay_condition_return: {
				return BackUtils.isBackActivityOpen(humanObj);
			}
            default:
				Log.temp.error("===未实现代码，conditon={}， confPayMallSn={}", confPayMall.conditon, confPayMall.sn);
				break;
		}
		return false;
	}

	/**
	 * 是否满足充值条件，活动类型的充值条件作为充值的推送校验时，需要检测活动解没解锁，但是作为充值条件时，不需要
	 */
	public boolean  isPayCondition(HumanObject humanObj, ConfPayMall conf, boolean isPush) {
		String[] conditions = GlobalConfVal.payConditionsMap.get(conf.sn);
		if (isPush) {
			switch (conditions[0]) {
				case PayMallTypeKey.pay_condition_act_type: {
					int[] ints = GlobalConfVal.actPayMallConditionMap.get(conf.sn);
					if (ints != null) {
						int actType = ints[0];
						int roundMin = ints[1];
						int roundMax = ints[2];
						if (ActivityManager.inst().isActivityOpenAndUnlock(humanObj, actType, roundMin, roundMax)) {
							return true;
						}
					}
				}
				break;
				case PayMallTypeKey.pay_condition_draw: {
					int drawType = Utils.intValue(conditions[1]);// 抽卡类型
					int drawNum = Utils.intValue(conditions[2]);// 抽卡数量
					int resetType = 0;
					if (drawType == 1) {
						resetType = DailyResetTypeKey.dailyDraw1Count.getType();
					} else if (drawType == 2) {
						resetType = DailyResetTypeKey.dailyDraw2Count.getType();
					} else if (drawType == 3) {
						resetType = DailyResetTypeKey.dailyDraw3Count.getType();
					}
					int value = humanObj.getDailyResetTypeValue(resetType);// 此时总的抽卡数量
					int lastValue = humanObj.getLastPushGift26DrawNum(drawType);// 上次激活礼包的抽卡数量
					return value - lastValue >= drawNum;
				}
				default: {
					return isPayConditon(humanObj, conf);
				}
			}
		}
		return isPayConditon(humanObj, conf);
	}

	// ==============================TODO shopMall

	public void _msg_shop_info_c2s(HumanObject humanObj, int type){

		Map<Integer, Map<Integer, Integer>> typeBuyNumMap = Utils.jsonToIntMapIntInt(humanObj.operation.mall.getMallBuyNumMap());
		Map<Integer, Integer> snBuyNumMap = typeBuyNumMap.getOrDefault(type, new HashMap<>());
		MsgShop.shop_info_s2c.Builder msg = MsgShop.shop_info_s2c.newBuilder();
		msg.setShopType(type);
		// 每日商店重置
		HumanDailyResetInfo dailyInfo = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyMall.getType());
		Map<Integer, Integer> dailySnBuyNumMap = Utils.jsonToMapIntInt(dailyInfo.getParam());
		// 每周商店重置
		HumanDailyResetInfo weekInfo = humanObj.getDailyResetInfo(DailyResetTypeKey.weekMall.getType());
		Map<Integer, Integer> weekSsnBuyNumMap = Utils.jsonToMapIntInt(weekInfo.getParam());

		List<Integer> snList = getMallSnListByType(humanObj, type);
		if(snList != null) {
			for (int sn : snList) {
				ConfMall confMall = ConfMall.get(sn);
				if(!isUnlockMall(humanObj, confMall.condition)){
					continue;
				}
				int shopId = confMall.sn;
				if (confMall.refreshWay == PayMallTypeKey.resetType_0) {
					Define.p_key_value.Builder dInfo = Define.p_key_value.newBuilder();
					dInfo.setK(shopId);
					dInfo.setV(snBuyNumMap.getOrDefault(shopId, 0));
					msg.addBuyInfo(dInfo);
				} else {
					HumanDailyResetInfo info = null;
					if (confMall.refreshWay == PayMallTypeKey.resetType_1) {
						// 每日
						Define.p_key_value.Builder dInfo = Define.p_key_value.newBuilder();
						dInfo.setK(shopId);
						dInfo.setV(dailySnBuyNumMap.getOrDefault(shopId, 0));
						msg.addBuyInfo(dInfo);
					} else if (confMall.refreshWay == PayMallTypeKey.resetType_2) {
						// 每周
						Define.p_key_value.Builder dInfo = Define.p_key_value.newBuilder();
						dInfo.setK(shopId);
						dInfo.setV(weekSsnBuyNumMap.getOrDefault(shopId, 0));
						msg.addBuyInfo(dInfo);
					} else {
						Log.temp.error("===未实现代码，refreshWay={}， confMallSn={}", confMall.refreshWay, confMall.sn);
					}
				}
			}
		}
		humanObj.sendMsg(msg);
	}

	/**
	 * 获取商城商品列表
	 * @param humanObj
	 * @param type
	 * @return
	 */
	public List<Integer> getMallSnListByType(HumanObject humanObj, int type) {
		List<Integer> snList = GlobalConfVal.getTypeMallSortASCSnList(type);
		if(type == MallType_Back){
			List<Integer> backSnList = BackUtils.getMallTypeBackSnList(humanObj);
			snList.retainAll(backSnList);
		}
		return snList;
	}
	

	public boolean isUnlockMall(HumanObject humanObj, String condition){
		if(condition == null || condition.isEmpty()){
			return true;
		}
		String[] strArr = Utils.splitStr(condition, "\\,");
		String type = strArr[0];
		switch (type){
			case mallOpenfunc:
				int modSn = Utils.intValue(strArr[1]);
				if(humanObj.isModUnlock(modSn)){
					return true;
				}
				break;
			case mallGuildLv:
				int needLv = Utils.intValue(strArr[1]);
				if(humanObj.guildLv >= needLv){
					return true;
				}
				break;
			case mallActivityGroup:
				return ActivityManager.inst().isActivityOpenOrShow(humanObj, Utils.intValue(strArr[1]));
			case mallOpenTime:
				return isOpenTime(strArr);
			case mallKungfuRace:
				int[] stageArray = Utils.strToIntArray(strArr[1], "\\_");
                return Arrays.stream(stageArray).anyMatch(stageGroup -> stageGroup == humanObj.currentKungFuRaceStageGroup);
			default:
				Log.temp.error("===未实现此类型，type={}, condition={}", type, condition);
				break;
		}
		return false;
	}

	private boolean isOpenTime(String[] strArr){
		if(strArr.length < 13){
			return false;
		}
		long openTime = Utils.getTime(Utils.intValue(strArr[1]), Utils.intValue(strArr[2]), Utils.intValue(strArr[3]),
				Utils.intValue(strArr[4]), Utils.intValue(strArr[5]), Utils.intValue(strArr[6]));
		long endTime = Utils.getTime(Utils.intValue(strArr[7]), Utils.intValue(strArr[8]), Utils.intValue(strArr[9]),
				Utils.intValue(strArr[10]), Utils.intValue(strArr[11]), Utils.intValue(strArr[12]));
		long timeNow = Port.getTime();
		if(openTime <= timeNow && timeNow < endTime){
			return true;
		}
		return false;
	}

	public void _msg_shop_buy_c2s(HumanObject humanObj, int shopType, int shopId, int num) {
		if(num <= 0 || num >= Integer.MAX_VALUE){
			Log.temp.error("===humanId={}， shopId={}, num={}", humanObj.id, shopId, num);
			return;
		}
		ConfMall confMall = ConfMall.get(shopId);
		if(confMall == null){
			Log.temp.error("===ConfMall配表错误， sn={}", shopId);
			return;
		}
		if(shopType != confMall.type){
			return;
		}
		if(confMall.limit > 0){
			if(!isCanBuy(humanObj, shopType, confMall.limit, shopId, confMall.refreshWay, num)){
				// 没有购买次数或次数不够
				return;
			}
		}
		if(confMall.goods == null || confMall.goods.length % 2 != 0){
			return;
		}
		if (shopType == MallType_Back){
			List<Integer> backSnList = BackUtils.getMallTypeBackSnList(humanObj);
			if(!backSnList.contains(shopId)){
				// 不在自己可选的回归商城列表里
				return;
			}
		}
		if(!isUnlockMall(humanObj, confMall.condition)){
			return;
		}

		int costItemSn = confMall.price != null ? confMall.price[0] : 0;
		int costItemNum =confMall.price != null ?  confMall.price[1] : 0;
		long sumCostNum = 0;

		int buyNum = buyMallNum(humanObj, shopId, confMall.refreshWay, shopType);

		if(confMall.addPrice == null){
			sumCostNum = Utils.longValue(num) * costItemNum;
		} else {
			int length = confMall.addPrice.length;
			long sumCostAddNum = 0;
			int loopNum = num;
			if(num > length){
				loopNum = length;
				long superNum = Utils.longValue(num - length);
				sumCostNum = superNum * costItemNum + superNum * confMall.addPrice[length - 1];
			}
			for(int i = 0; i < loopNum; i++){
				if(buyNum < length){
					sumCostAddNum += costItemNum + confMall.addPrice[buyNum];
				} else {
					sumCostAddNum += costItemNum + confMall.addPrice[length - 1];
				}
				buyNum++;
			}
			sumCostNum += sumCostAddNum;
		}

		if(costItemSn > 0 && sumCostNum <= 0){
			Log.temp.error("===humanId={}， shopId={}, num={}， costItemSn={}, costItemNum={}, sumCostNum={}",
					humanObj.id, shopId, num, costItemSn, costItemNum, sumCostNum);
			return;
		}
		if(costItemSn > 0){
			ReasonResult result = ProduceManager.inst().canCostProduce(humanObj, costItemSn, sumCostNum);
			if(!result.success){
				return;
			}
		}
		// 限制次数或有价格增加
		if(confMall.limit > 0 || confMall.addPrice != null){
			if(confMall.refreshWay == PayMallTypeKey.resetType_0){
				updateMallBuyNum(humanObj, shopType, shopId, num);
			} else {
				HumanDailyResetInfo info = null;
				if(confMall.refreshWay == PayMallTypeKey.resetType_1){
					// 每日商店重置
					info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyMall.getType());
				} else if(confMall.refreshWay == PayMallTypeKey.resetType_2){
					// 每周商店重置
					info = humanObj.getDailyResetInfo(DailyResetTypeKey.weekMall.getType());
				}
				if(info == null){
					return;
				}
				Map<Integer, Integer> snBuyNumMap = Utils.jsonToMapIntInt(info.getParam());
				snBuyNumMap.put(shopId, snBuyNumMap.getOrDefault(shopId, 0) + num);
				info.setParam(Utils.mapIntIntToJSON(snBuyNumMap));
				humanObj.saveDailyResetRecord();
			}
		}
		if(costItemSn > 0){
			// 扣消耗
			ProduceManager.inst().costItem(humanObj, costItemSn, sumCostNum, MoneyItemLogKey.商店购买);
		}
		Map<Integer, Integer> addMap = Utils.intArrToIntMap(confMall.goods, num);
		if(confMall.goods != null && confMall.goods.length >= 2){
			ConfGoods confGoods = ConfGoods.get(confMall.goods[0]);
			if(confGoods != null && confGoods.type == ItemConstants.鱼饵){
				humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_钓鱼每日任务, TaskConditionTypeKey.TASK_TYPE_2022, num);
				ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_2022, 0, num);
			}
		}

		// 给物品
		List<Define.p_reward> rewardList = ProduceManager.inst().rewardProduceToMsg(humanObj, addMap, MoneyItemLogKey.商店购买);

		sendMsg_shop_buy_s2c(humanObj, shopId, 0);// num客户端没用无所谓发什么

		InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewardList);

		_msg_shop_info_c2s(humanObj, shopType);
	}

	public void sendMsg_shop_buy_s2c(HumanObject humanObj, int shopId, int buyNum){
		MsgShop.shop_buy_s2c.Builder msg = MsgShop.shop_buy_s2c.newBuilder();
		msg.setShopId(shopId);
		msg.setNum(buyNum);
		humanObj.sendMsg(msg);
	}

	private boolean isCanBuy(HumanObject humanObj, int type, int limit, int mallSn, int resetType, int buyNum){
		if(limit <= 0) {
			return true;
		}
		Map<Integer, Integer> snBuyNumMap = new HashMap<>();
		if(resetType == PayMallTypeKey.resetType_0){
			Map<Integer, Map<Integer, Integer>> typeBuyNumMap = Utils.jsonToIntMapIntInt(humanObj.operation.mall.getMallBuyNumMap());
			snBuyNumMap = typeBuyNumMap.get(type);
		} else if(resetType == PayMallTypeKey.resetType_1){
			// 每日商店重置
			HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyMall.getType());
			snBuyNumMap = Utils.jsonToMapIntInt(info.getParam());
		} else if(resetType == PayMallTypeKey.resetType_2){
			// 每周商店重置
			HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.weekMall.getType());
			snBuyNumMap = Utils.jsonToMapIntInt(info.getParam());
		}
		if(snBuyNumMap == null){
			snBuyNumMap = new HashedMap();
		}

		int surplusNum = limit - Utils.intValue(snBuyNumMap.get(mallSn));
		if(surplusNum <= 0 || surplusNum < buyNum){
			// 没有购买次数
			return false;
		}
		return true;
	}

	public int updateMallBuyNum(HumanObject humanObj, int type, int mallSn, int addBuyNum){
		Map<Integer, Map<Integer, Integer>> typeBuyNumMap = Utils.jsonToIntMapIntInt(humanObj.operation.mall.getMallBuyNumMap());
		Map<Integer, Integer> snBuyNumMap = typeBuyNumMap.get(type);
		if(snBuyNumMap == null){
			snBuyNumMap = new HashedMap();
			typeBuyNumMap.put(type, snBuyNumMap);
		}
		int buyNum = Utils.intValue(snBuyNumMap.get(mallSn)) + addBuyNum;
		snBuyNumMap.put(mallSn, buyNum);
		humanObj.operation.mall.setMallBuyNumMap(Utils.mapIntMapIntIntToJSON(typeBuyNumMap));
		return buyNum;
	}

	/**
	 * 重置回归商城购买限制次数
	 * @param humanObj
	 */
	public void resetBackMallBuyNum(HumanObject humanObj) {
		Map<Integer, Map<Integer, Integer>> typeBuyNumMap = Utils.jsonToIntMapIntInt(humanObj.operation.mall.getMallBuyNumMap());
		typeBuyNumMap.remove(MallType_Back);
		humanObj.operation.mall.setMallBuyNumMap(Utils.mapIntMapIntIntToJSON(typeBuyNumMap));
	}

	public void _msg_shop_limit_time_c2s(HumanObject humanObj, int shopType) {


	}


	//=====================================TODO 抽卡（抽技能，抽同伴）
	public void _msg_draw_card_c2s(HumanObject humanObj, int cardPoolType, int num, boolean notCost){
		lotteryCardPool(humanObj, cardPoolType, num, notCost);
	}

	private void lotteryCardPool(HumanObject humanObj, int cardPoolType, int num, boolean notCost){
		Human3 human = humanObj.getHuman3();
		ConfCardPoolType confCardPoolType = ConfCardPoolType.get(cardPoolType);
		if(confCardPoolType == null){
			Log.temp.error("===ConfCardPoolType配表错误, not find sn={}", cardPoolType);
			return;
		}
		int[] intArr = null;
		int index = -1;
		for(int i = 0; i < confCardPoolType.treasure.length; i++){
			int[] arr = confCardPoolType.treasure[i];
			if(arr[0] == num){
				intArr = confCardPoolType.treasure[i];
				index = i;
				break;
			}
		}
		if(intArr == null || intArr.length < 3){
			Log.temp.error("===ConfCardPoolType配表错误, index={}, treasure={}  sn={}, intArr={}, num={}",
					index, confCardPoolType.treasure, confCardPoolType.sn, intArr, num);
			return;
		}
		if(confCardPoolType.treasure == null || confCardPoolType.treasure.length <= index || index < 0){
			Log.temp.error("===ConfCardPoolType配表错误, index={}, treasure={}  sn={}", index, confCardPoolType.treasure, confCardPoolType.sn);
			return;
		}
		int lotteryNum = intArr[0];
		int costSn = intArr[1];
		int costNum = intArr[2];
		boolean isCostGlod = false;
		int extraCostSn = 0;
		int extraCostNum = 0;
		if(!notCost){
			ReasonResult result = ProduceManager.inst().canCostProduce(humanObj, costSn, costNum);
			if(!result.success){
				int haveNum = humanObj.operation.itemData.getItemNum(costSn);
				ConfGoods confGoods = ConfGoods.get(costSn);
				extraCostSn = confGoods.price[0];
				extraCostNum = confGoods.price[1] * (costNum - haveNum);
				result = ProduceManager.inst().canCostProduce(humanObj, extraCostSn, extraCostNum);
				if(!result.success){
					return;
				}
				costNum = haveNum;
				isCostGlod = true;
			}
		}

		int lv = 1;
		int lottNumSum =0;
		MoneyItemLogKey logKey = MoneyItemLogKey.未设置;
		if(cardPoolType == cardPoolType1){
			lv = human.getLotterySkillLv();
			lottNumSum = human.getTotalCardSkillNum();
			logKey =  MoneyItemLogKey.抽技能;
		} else if(cardPoolType == cardPoolType2){
			lv = human.getLotteryPetLv();
			lottNumSum = human.getTotalCardPetNum();
			logKey =  MoneyItemLogKey.抽同伴;
		} else {
			Log.temp.error("===未实现代码， cardPoolType={}", cardPoolType);
			return;
		}
		if(!notCost) {
			if (costNum > 0) {
				// 扣消耗
				ProduceManager.inst().costItem(humanObj, costSn, costNum, logKey);
			}
			if (isCostGlod) {
				ProduceManager.inst().costItem(humanObj, extraCostSn, extraCostNum, logKey);
			}
		}
		List<Define.p_reward> awardSnList = new ArrayList<>();

		Map<Integer, Integer> dropAllMap = new HashMap<>();
		for(int i = 0; i < lotteryNum; i++){
			lottNumSum++;
			// 抽奖励
			Map<Integer, Integer> dropMap = lotteryDropMap(cardPoolType, lv, logKey, awardSnList);
			dropAllMap = Utils.mergeMap(dropAllMap, dropMap);
			int newLv = checkCardPoolAddLv(lottNumSum, cardPoolType, lv);
			if(newLv != lv){
				lv = newLv;
			}
		}

		if(cardPoolType == cardPoolType1){
			human.setLotterySkillLv(lv);
			human.setTotalCardSkillNum(lottNumSum);
		} else if(cardPoolType == cardPoolType2){
			human.setLotteryPetLv(lv);
			human.setTotalCardPetNum(lottNumSum);
		}
		human.update();
		ProduceManager.inst().produceAll(humanObj, dropAllMap, logKey);

		if(num > 35){
			awardSnList = InstanceManager.inst().to_p_rewardList(dropAllMap);
		}
		sendMsg_draw_card_s2c(humanObj, cardPoolType, lottNumSum, awardSnList);

		List<Integer> snList = new ArrayList<>();
		for(int sn : dropAllMap.keySet()){
			ConfGoods conf = ConfGoods.get(sn);
			if(conf == null){
				continue;
			}
			snList.add(conf.effect[0][0]);
		}
		int resetType = 0;
		if (cardPoolType == cardPoolType1) {
			humanObj.operation.skillData.saveData(humanObj);
			SkillManager.inst().sendMsg_skill_system_update_s2c(humanObj, SkillManager.skill_system_update_type_0, snList);
			SkillManager.inst().updatePower(humanObj);
			SkillManager.inst().updateSkillPorpCalc(humanObj);
			resetType = DailyResetTypeKey.dailyDraw1Count.getType();
		} else if (cardPoolType == cardPoolType2) {
			humanObj.operation.petData.saveData(humanObj);
			PetManager.inst().sendMsg_pet_update_s2c(humanObj, PetManager.pet_update_type_0, snList);
			PetManager.inst().updatePetPropCalc(humanObj);
			resetType = DailyResetTypeKey.dailyDraw2Count.getType();
		}
		awardSnList.clear();
		Event.fire(EventKey.LOTTERY_SELECT, "humanObj", humanObj, "type", cardPoolType, "lotteryNum", lotteryNum);


		HumanDailyResetInfo info = humanObj.getDailyResetInfo(resetType);
		if (info == null) {
			return;
		}
		info.setValue(info.getValue() + lotteryNum);
		humanObj.saveDailyResetRecord();

		pushLimitGift26Draw(humanObj, cardPoolType);
	}

	private void sendMsg_draw_card_s2c(HumanObject humanObj, int coodType, int count, List<Define.p_reward> awardSnList){
		MsgDraw.draw_card_s2c.Builder msg = MsgDraw.draw_card_s2c.newBuilder();
		msg.setPoolList(to_p_draw_info(humanObj, coodType, count));
		msg.addAllRewardList(awardSnList);
		humanObj.sendMsg(msg);
	}

	private Define.p_draw_info to_p_draw_info(HumanObject humanObj, int poolType, int count){
		Human3 human = humanObj.getHuman3();
		Define.p_draw_info.Builder dInfo = Define.p_draw_info.newBuilder();
		dInfo.setPoolId(poolType);
		if(poolType == cardPoolType1){
			dInfo.setPoolLv(human.getLotterySkillLv());
		} else if(poolType == cardPoolType2){
			dInfo.setPoolLv(human.getLotteryPetLv());
		}
		dInfo.setCount(count);
		return dInfo.build();
	}

	public void sendMsg_draw_info_s2c(HumanObject humanObj){
		MsgDraw.draw_info_s2c.Builder msg = MsgDraw.draw_info_s2c.newBuilder();
		msg.addPoolList(to_p_draw_info(humanObj, cardPoolType1, humanObj.getHuman3().getTotalCardSkillNum()));
		msg.addPoolList(to_p_draw_info(humanObj, cardPoolType2, humanObj.getHuman3().getTotalCardPetNum()));
		humanObj.sendMsg(msg);
	}

	private int checkCardPoolAddLv(int sunNum, int cardType, int level){
		ConfCardPool_0 confCardPool = ConfCardPool_0.get(cardType, level);
		if(confCardPool == null){
			Log.temp.error("===ConfCardPool_0配表错误， type={}, level={}", cardType, level);
			return level;
		}
		while (sunNum >= confCardPool.num){
			++level;
			confCardPool = ConfCardPool_0.get(cardType, level);
			if(confCardPool == null){
				return --level;
			}
		}
		return level;
	}

	private Map<Integer, Integer> lotteryDropMap(int cardPoolType, int lv, MoneyItemLogKey logKey, List<Define.p_reward> awardSnList){
		ConfCardPool_0 confCardPool = ConfCardPool_0.get(cardPoolType, lv);
		if(confCardPool == null){
			Log.temp.error("===ConfCardPool_0配表错误， not find sn={},{}", cardPoolType, lv);
			return new HashedMap();
		}

		int groupId = Utils.getRandRangeValue(confCardPool.group);
		Map<Integer, Integer> dropMap = ProduceManager.inst().getDropMap(groupId, awardSnList, false);
		return dropMap;
	}

	/**
	 * 限时礼包，达到条件给弹窗
	 */
	@Listener(value = EventKey.INSTANCE_PASS, subInt = {InstanceConstants.DARKTRIALCHAPTER_28,
			InstanceConstants.DARKTRIALCHAPTER_29, InstanceConstants.DARKTRIALCHAPTER_30})
	public void _on_INSTANCE_PASS_DARKTRIALCHAPTER_28(Param param) {
		HumanObject humanObj = param.get("humanObj");
		if (humanObj == null) {
			return;
		}
		int repSn = Utils.getParamValue(param, "repSn", 0);
		Map<Integer, List<ConfPayMall>> dungeonPayRepSnMallSnMap = GlobalConfVal.dungeonPayRepSnMallSnMap;
		if(dungeonPayRepSnMallSnMap.containsKey(humanObj.getHuman2().getRepSn()-1)){
			List<ConfPayMall> confPayMallList = dungeonPayRepSnMallSnMap.get(repSn);
			for(ConfPayMall confPayMall : confPayMallList){
				addLimitTimeGift(humanObj, confPayMall);
			}
			sendMsg_pay_mall_info_s2c(humanObj, PayMallTypeKey.PAY_Type_25);
		}
	}

	@Listener(value = EventKey.INSTANCE_PASS, subInt = InstanceConstants.CHAPTER_1)
	public void _on_INSTANCE_PASS_CHAPTER_1(Param param) {
		HumanObject humanObj = param.get("humanObj");
		if (humanObj == null) {
			return;
		}
		Map<Integer, List<ConfPayMall>> guardPayRepSnMallSnMap = GlobalConfVal.guardPayRepSnMallSnMap;
		if(guardPayRepSnMallSnMap.containsKey(humanObj.getHuman2().getRepSn()-1)){
			List<ConfPayMall> confList = guardPayRepSnMallSnMap.get(humanObj.getHuman2().getRepSn()-1);
			boolean added = false;
			for (ConfPayMall confPayMall : confList) {
				if (humanObj.operation.mall.getChargeScore() <= confPayMall.parameter) {
					addLimitTimeGift(humanObj,confPayMall);
					added = true;
					break;
				}
			}
			// 如果没有添加任何项,则添加该List中最后一个符合条件的confPayMall
			if (!added && !confList.isEmpty()) {
				ConfPayMall confPayMallMax = confList.get(confList.size() - 1);
				addLimitTimeGift(humanObj,confPayMallMax);
			}
			sendMsg_pay_mall_info_s2c(humanObj, PayMallTypeKey.PAY_Type_25);
		}

	}

	public void addLimitTimeGift(HumanObject humanObj, ConfPayMall confPayMall) {
		Map<Integer, Integer> giftMap = getLimitTimeGifMap(humanObj);
		giftMap.put(confPayMall.sn, (int) (Port.getTime() / Time.SEC + confPayMall.push_continue));
		humanObj.operation.mall.setLimitTimeGiftMap(Utils.mapIntIntToJSON(giftMap));
		humanObj.operation.mall.update();
	}

	public void addLimitTimeGift(HumanObject humanObj, ConfPayMall confPayMall, long endTime) {
		Map<Integer, Integer> giftMap = getLimitTimeGifMap(humanObj);
		if (giftMap.containsKey(confPayMall.sn)) {
			return;
		}
		giftMap.put(confPayMall.sn, (int)(endTime / Time.SEC));
		humanObj.operation.mall.setLimitTimeGiftMap(Utils.mapIntIntToJSON(giftMap));
		humanObj.operation.mall.update();
	}

	private Map<Integer, Integer> getLimitTimeGifMap(HumanObject humanObj) {
		Mall mall = humanObj.operation.mall;
		Map<Integer, Integer> limitTimeGifMap = Utils.jsonToMapIntInt(mall.getLimitTimeGiftMap());
		long currTime = Port.getTime();
		//移除过期的
		Iterator<Map.Entry<Integer, Integer>> it = limitTimeGifMap.entrySet().iterator();
		while (it.hasNext()) {
			Map.Entry<Integer, Integer> entry = it.next();
			long expireTime = entry.getValue() * Time.SEC;
			if (currTime >= expireTime) {
				Integer sn = entry.getKey();
				handleGiftExpire(humanObj, sn, currTime, expireTime);
				it.remove();
			}
		}
		return limitTimeGifMap;
	}

	private void handleGiftExpire(HumanObject humanObj, int sn, long currTime, long expireTime) {
		ConfPayMall conf = ConfPayMall.get(sn);
		if (conf == null) {
			return;
		}
		switch (conf.type) {
			case PayMallTypeKey.PAY_Type_12: {
				humanObj.operation.removeCustomGift(sn);
			}
			break;
			case PayMallTypeKey.PAY_Type_26: {
				long startTime = expireTime - (conf.push_continue * Time.SEC);
				if (TimeUtil.isSameDay(startTime, currTime)) {
					// 检测是否在同一天，如果在同一天，才考虑设置成今日不在推礼包
					handleGift26Expire(humanObj, sn);
				}
			}
			break;
		}
	}

	private void handleGift26Expire(HumanObject humanObj, int sn) {
		String[] conditions = GlobalConfVal.payConditionsMap.get(sn);
		if (conditions != null && conditions.length >= 2) {
			humanObj.setGift26Expire(conditions[0] + conditions[1]);

			String key = conditions[0] + conditions[1];
			int gear = humanObj.operation.getGearRange(key);
			ConfPayMall conf = ConfPayMall.get(sn);
			if (gear - 1 >= conf.gear_range[0]) {
				humanObj.operation.addGearRange(key, -1);
			}
		}
	}

	// ===========================================TODO 星钻商店=======================================
	public void payStarDiamondMall(HumanObject humanObj, int sn, List<Define.p_reward> p_rewards){
		ConfStarDiamondMall conf = ConfStarDiamondMall.get(sn);
		if(conf == null){
			Log.temp.error("===ConfStarDiamondMall配表错误， not find Sn={}", sn);
			return;
		}
		Mall mall = humanObj.operation.mall;
		List<Integer> snList = Utils.strToIntList(mall.getStarDiamondMallSnList());
		if(!snList.contains(sn)){
			snList.add(sn);
			mall.setStarDiamondMallSnList(Utils.listToString(snList));
			ProduceManager.inst().produceAdd(humanObj, conf.first_reward, MoneyItemLogKey.星钻商店首充奖励);
			p_rewards.addAll(InstanceManager.inst().to_p_rewardList(conf.first_reward));
		}
		ProduceManager.inst().produceAdd(humanObj, conf.reward, MoneyItemLogKey.星钻商店充值奖励);
		p_rewards.addAll(InstanceManager.inst().to_p_rewardList(conf.reward));
	}

	public void payDiamondMall(HumanObject humanObj, int sn, List<Define.p_reward> p_rewards){
		ConfDiamondMall conf = ConfDiamondMall.get(sn);
		if(conf == null){
			Log.temp.error("===ConfDiamondMall配表错误， not find Sn={}", sn);
			return;
		}
		Mall mall = humanObj.operation.mall;
		List<Integer> snList = Utils.strToIntList(mall.getDiamondMallSnList());
		if(!snList.contains(sn)){
			snList.add(sn);
			mall.setDiamondMallSnList(Utils.listToString(snList));
			ProduceManager.inst().produceAdd(humanObj, TokenItemType.GOLD, conf.first_gift, MoneyItemLogKey.钻石商店首充奖励);
			p_rewards.add(HumanManager.inst().to_p_reward(TokenItemType.GOLD, Utils.longValue(conf.first_gift)));
		}
		ProduceManager.inst().produceAdd(humanObj, TokenItemType.GOLD, conf.recharge, MoneyItemLogKey.钻石商店充值奖励);
		p_rewards.add(HumanManager.inst().to_p_reward(TokenItemType.GOLD, conf.recharge));
		if(conf.gift > 0){
			Map<Integer, Integer> dropMap = ProduceManager.inst().getDropMap(conf.gift);
			ProduceManager.inst().produceAll(humanObj, dropMap, MoneyItemLogKey.钻石商店充值奖励);
			p_rewards.addAll(InstanceManager.inst().to_p_rewardList(dropMap));
		}


	}

	// ===========================================TODO 星钻商店结束=======================================

	// ===========================================TODO 特权商店开始===========================================
	private void payPrivilegeCard(HumanObject humanObj, int sn){
		ConfPrivilegeCardType conf = ConfPrivilegeCardType.getBy(ConfPrivilegeCardType.K.gift_id, sn);
		if(conf == null){
			Log.temp.error("===ConfPrivilegeCardType配表错误， not find Sn={},humanId={},name={}", sn, humanObj.id, humanObj.name);
			return;
		}
		PrivilegeManager.inst().givePrivilegeCard(humanObj, conf.sn);
	}

	// ===========================================TODO 特权卡商店结束=======================================
	private void payWarToken(HumanObject humanObj, int sn){
		ConfPayMall confPayMall = ConfPayMall.get(sn);
		String[] strArr = Utils.splitStr(confPayMall.conditon, "\\|");
		if (strArr[0].equals(PayMallTypeKey.pay_condition_wartoken)) {
			ConfWartoken confWartoken = ConfWartoken.get(Utils.intValue(strArr[1]));
			if (confWartoken == null) {
				Log.game.error("===充值ConfWartoken配表错误， not find Sn={}，HumanID=", Utils.intValue(strArr[1]),humanObj.id);
				return;
			}
			ActivityControlWarToken control = (ActivityControlWarToken)ActivityControlTypeFactory.getTypeData(confWartoken.act_type);
			control.payWarToken(humanObj, confWartoken,confPayMall.sn);
			if(strArr.length > 2){
				int payType = Utils.intValue(strArr[2]);
				if(payType == PayMallTypeKey.wartoken_type_1 || payType == PayMallTypeKey.wartoken_type_2) {
					Set<Integer> actCardEliminateSet = ActivityControlTypeFactory.getTypeSet(ActivityControlCardEliminate.class);
					for (int actType : actCardEliminateSet) {
						if (!ActivityManager.inst().isActivityOpen(humanObj, actType)) {
							continue;
						}
						ActivityControlCardEliminate controlCardEliminate = (ActivityControlCardEliminate) ActivityControlTypeFactory.getTypeData(actType);
						controlCardEliminate.payWarToken(humanObj, payType);
					}
				}
			}
		} else if(strArr[0].equals(PayMallTypeKey.pay_condition_battlepass)) {
			ConfBattlePass confBattlePass = ConfBattlePass.get(Utils.intValue(strArr[1]));
			if (confBattlePass == null) {
				Log.game.error("===充值ConfBattlePass配表错误， not find Sn={}，HumanID=", Utils.intValue(strArr[1]),humanObj.id);
				return;
			}
			ActivityControlWarToken control = (ActivityControlWarToken)ActivityControlTypeFactory.getTypeData(confBattlePass.act_type);
			control.payWarToken(humanObj,confBattlePass);
		}
		if (!"".equals(confPayMall.param)) {
			String[] paramStrs = confPayMall.param.split("\\|");
			for (String paramStr : paramStrs) {
				String[] params = paramStr.split(",");
				int actType = Utils.intValue(params[0]);
				int round1 = Utils.intValue(params[1]);
				int round2 = Utils.intValue(params[2]);
				if (!ActivityManager.inst().isActivityOpen(humanObj, actType, round1, round2)) {
					continue;
				}
				IActivityControl control = ActivityControlTypeFactory.getTypeData(actType);
				control.pay(humanObj, sn);
			}
		}
	}

	private void payCustomMall(HumanObject humanObj, int sn, List<Define.p_reward> pRewardList) {
		ConfCustomMall conf = ConfCustomMall.get(sn);
		if (conf == null) {
			Log.temp.error("===ConfCustomMall配表错误， not find Sn={}", sn);
			return;
		}

		// 组装奖励
		List<int[]> rewardList = new ArrayList<>();
		Map<Integer, List<Integer>> customGiftMap = humanObj.operation.getCustomGift(sn);
		if (customGiftMap != null && !customGiftMap.isEmpty()) {
			for (Map.Entry<Integer, List<Integer>> entry : customGiftMap.entrySet()) {
				int[] items = entry.getValue().stream().mapToInt(Integer::intValue).toArray();
				rewardList.add(items);
			}
		}
		rewardList.addAll(Arrays.asList(conf.regular_reward));
		int[][] rewards = rewardList.toArray(new int[0][]);

		// 发奖励
		List<Define.p_reward> pRewarList = ProduceManager.inst().rewardProduceToMsg(humanObj, rewards, MoneyItemLogKey.限时商店礼包奖励);
		pRewardList.addAll(pRewarList);

		Map<Integer, Integer> limitTimeGifMap = getLimitTimeGifMap(humanObj);
		if (!limitTimeGifMap.containsKey(sn)) {
			// 非限时的，还要删除自选设置
			humanObj.operation.removeCustomGift(sn);
		}
	}

	private void payFirstCharge(HumanObject humanObj, int sn){
		Map<Integer,Integer> firstChargeMap = Utils.jsonToMapIntInt(humanObj.operation.mall.getFirstPayGiftTimeMap());
		firstChargeMap.put(sn, (int) (Port.getTime() / Time.SEC));
		humanObj.operation.mall.setFirstPayGiftTimeMap(Utils.mapIntIntToJSON(firstChargeMap));
		humanObj.operation.mall.update();
		MsgPayMall.pay_mall_info_s2c.Builder msg = MsgPayMall.pay_mall_info_s2c.newBuilder();
		msg.setType(PayMallTypeKey.PAY_Type_20);
		msg.addAllPayMallList(to_p_pay_mall_info_list_20(humanObj));
		humanObj.sendMsg(msg);
	}

	public void _msg_fake_recharge_c2s(HumanObject humanObj, int bundleId, String payId) {
		if(!payId.isEmpty()){
			// 闪钻购买AI礼包
			fakeRechargePayGift(humanObj, payId);
			return;
		}
		ConfPayMall confPayMall = ConfPayMall.get(bundleId);
		if(confPayMall == null){
			Log.temp.error("===ConfPayMall配表错误， not find bundleId={}", bundleId);
			return;
		}
		int price = confPayMall.price;
		if(price <= 0){
			Log.temp.error("===ConfPayMall配表错误， price={} bundleId={}", price ,bundleId);
			return;
		}
		ReasonResult result = MoneyManager.inst().canProduceReduce(humanObj, TokenItemType.Money999, price);
		if(!result.success){
			Inform.sendMsg_error(humanObj, 3);// 物品不足
			return;
		}
		if(confPayMall.type == PayMallTypeKey.PAY_Type_8){// 策划要求此类型不能用999买
			return;
		}
		if(!isPayConditon(humanObj, confPayMall)){
			Log.temp.error("===充值条件不满足，humanId={}, paySn={}, type={}", humanObj.id, bundleId, confPayMall.type);
			return;
		}
		if(confPayMall.recharge_only == 1){// 不能买，必须充值
			return;
		}
		if(confPayMall.buy_times > 0){
			int resetType = confPayMall.reset;
			int buyNum = buyPayMallNum(humanObj, bundleId, resetType);
			if(buyNum >= confPayMall.buy_times){
				return;
			}
		}

		ProduceManager.inst().costItem(humanObj, TokenItemType.Money999, price, MoneyItemLogKey.充值);
		Log.temp.error("===闪钻充值成功，humanId={}, paySn={}, price={}", humanObj.id, bundleId, price);

		Event.fire(EventKey.PAY_NOTIFY, "humanObj", humanObj, "sn", bundleId, "orderId", "", "giftId", "","isFake", true);
	}

	/**
	 * 闪钻购买AI礼包
	 * @param humanObj
	 * @param payId
	 */
	public void fakeRechargePayGift(HumanObject humanObj, String payId) {
		// 充值礼包
		PayGift payGift = humanObj.payGiftMap.get(payId);
		if(payGift == null){
			Log.temp.error("===payId={}, 数据已经清空或礼包不存在.humanId={}", payId, humanObj.id);
			return;
		}
		int price = payGift.getPrice();
		ReasonResult result = MoneyManager.inst().canProduceReduce(humanObj, TokenItemType.Money999, price);
		if(!result.success){
			Inform.sendMsg_error(humanObj, 3);// 物品不足
			return;
		}
		ProduceManager.inst().costItem(humanObj, TokenItemType.Money999, price, MoneyItemLogKey.充值);
		Log.temp.error("===闪钻购买AI礼包成功，humanId={}, payId={}, price={}", humanObj.id, payId, price);
		MallManager.inst().payGiftGiveReward(humanObj, payGift, null);
	}

	/**
	 * 充值礼包给奖励
	 * <AUTHOR>
	 * @Date 2023/11/20
	 * @Param
	 */
	public boolean payGiftGiveReward(HumanObject humanObj, PayGift payGift, JSONObject jo){
		sendMsg_pay_mall_recharge_s2c(humanObj, 0, String.valueOf(payGift.getPayId()), PF_PAY_Manager.payType2, payGift.getGiftCode());
		String payId = payGift.getPayId();
		if(jo != null){
			PayLog log = PF_PAY_Manager.inst().recordPayLog(humanObj.id, jo, "充值成功");
			humanObj.dataPers.payLogs.add(log);
		}
		if(payGift.getBuyNum() >= payGift.getPurchaseLimitAmount()){
			int vipPoint = payGift.getVipPoints();
			addVipPoint(humanObj, vipPoint);
			// 不发货,给补偿
			ProduceManager.inst().produceAdd(humanObj, TokenItemType.GOLD, payGift.getAmountToDiamond(), MoneyItemLogKey.充值礼包);
			sendPayGift(humanObj, payId, 0, payGift.getAmountToDiamond());
			sendSCPayGiftRemove(humanObj, payId);
			//添加充值日志
			payGiftLog(humanObj.getHuman(), payGift.getPrice(), payGift.getAmountToDiamond(), payGift.getGiftCode());
			return false;
		}
		payGiftReward(humanObj, payGift);
		return true;
	}

	private void addVipPoint(HumanObject humanObj, int vipPoint) {
		if(vipPoint <= 0){
			return;
		}
		float pt = getRegionalVipPoint(humanObj.getRegional(), vipPoint);
		// 任务进度
		ActivityManager.inst().addActivityProgress(humanObj,TaskConditionTypeKey.TASK_TYPE_25,0,pt);
		ActivityManager.inst().addActivityProgress(humanObj,TaskConditionTypeKey.TASK_TYPE_24,0,pt);
		// 记录累充记录,策划规定VipPoints点数乘以100为累充金额
		int savePt = (int) (pt * 100);
		updateTotalRMB(humanObj.operation.human3, savePt);
		humanObj.operation.mall.setChargeScore(humanObj.operation.mall.getChargeScore() + savePt);
		humanObj.operation.mall.update();

		//招财猫活动
		List<Integer> actTypeList = ActivityControlTypeFactory.getTypeList(ActivityControlLuckyCat.class);
		for(int actType : actTypeList){
			ActivityControlLuckyCat control = (ActivityControlLuckyCat)ActivityControlTypeFactory.getTypeData(actType);
			control.addPayNum(humanObj, savePt);
		}
		//工会充值活动
		Set<Integer> guildPaySet = ActivityControlTypeFactory.getTypeSet(ActivityControlGuildPay.class);
		for (int actType : guildPaySet) {
			ActivityControlGuildPay control = (ActivityControlGuildPay)ActivityControlTypeFactory.getTypeData(actType);
			control.payRecord(humanObj, 0);
		}
		// 累充
		handleAccumulatedRecharge(humanObj, vipPoint);
		Log.game.info("addVipPoint humanObj={},pt={},regional={}", humanObj.id, pt,humanObj.getRegional());
	}

	private float getRegionalVipPoint(String regional, long vipPoint) {
		if (regional == null || regional.isEmpty()) {
			return vipPoint / 20;
		}

		ConfExchangeRate confExchangeRate = ConfExchangeRate.get((int)vipPoint);
		if (confExchangeRate == null) {
			return vipPoint / 20;
		}

		ConfExchangeRate confExchangeUnit = ConfExchangeRate.get(20);
		if (confExchangeUnit == null) {
			return vipPoint / 20;
		}
		float exchangeValue = confExchangeRate.getFieldValue(regional);
		float unitValue = confExchangeUnit.getFieldValue(regional);
		if (unitValue == 0) {
			return vipPoint / 20;
		}

		return  exchangeValue / unitValue;
	}

	public void updateTotalRMB(Human3 human, int rmb){
		long newRMB = human.getTotalRMB() + rmb;
		human.setTotalRMB(newRMB);
		human.update();
	}

	/**
	 * 领取充值礼包奖励
	 * <AUTHOR>
	 * @Date 2023/11/2
	 * @Param
	 */
	public int payGiftReward(HumanObject humanObj, PayGift payGift){
		payGift.setBuyNum(payGift.getBuyNum() + 1);
		payGift.update();
		addVipPoint(humanObj,payGift.getVipPoints());
		Map<Integer,Integer> itemMap = Utils.jsonToMapIntInt(payGift.getItems());
		int gold = itemMap.getOrDefault(TokenItemType.GOLD, 0);
		if(!itemMap.isEmpty()){
			ProduceManager.inst().produceAdd(humanObj, itemMap, MoneyItemLogKey.充值礼包);
			InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, itemMap);
		}
		Log.temp.error("===充值礼包奖励，humanId={},account={}, payId={}, gold={}, itemMap={}",
				humanObj.id, humanObj.account, payGift.getPayId(), gold, itemMap);
		if(payGift.getBuyNum() >= payGift.getPurchaseLimitAmount()){
			sendSCPayGiftRemove(humanObj, payGift.getPayId());
		}
		payGiftLog(humanObj.getHuman(), payGift.getPrice(), gold, payGift.getGiftCode());
		return gold;
	}


	/**
	 * 充值后台礼包通知前端（约定sn=-1）
	 * <AUTHOR>
	 * @Date 2023/10/25
	 * @Param
	 */
	private void sendPayGift(HumanObject humanObj, String orderId, long gold, int bindGold){
//		MsgPaymoney.SCCharge.Builder msg = MsgPaymoney.SCCharge.newBuilder();
//		msg.setOrderId(orderId);
//		msg.setGold(gold);
//		msg.setBindGold(bindGold);
//		msg.setSn(-1);
//		humanObj.sendMsg(msg);
	}

	public boolean payGiftNew(HumanObject humanObj, String json){
		JSONObject jo = Utils.toJSONObject(json);
		long humanId = Utils.longValue(jo.getString("humanId"));//玩家id
		String id = jo.getString("id");//礼包的唯一id
		int vipPoints = jo.getIntValue("vipPoints");//购买礼包后可获得的vip点数
		int amountToDiamond = jo.getIntValue("amountToDiamond");//此礼包付费但是购买失败后给玩家补单的钻石数量
		int price = jo.getIntValue("price");//礼包的定价
		int priceValueRatio = jo.getIntValue("priceValueRatio");//礼包性价比
		int duration = jo.getIntValue("duration");//弹出时长 （秒）
		int purchaseLimitAmount = jo.getIntValue("purchaseLimitAmount");
		boolean isAISupported = jo.getBooleanValue("isAISupported");
		String currency = jo.getString("currency");
		double currencyPrice = jo.getDouble("currencyPrice");
		String currencyTag = jo.getString("currencyTag");
		String templateId = jo.getString("templateId");
		String giftCode = jo.getString("giftCode");//礼包的标识sn

		String itemJSON = jo.getString("items");
		Map<Integer, Integer> itemMap = Utils.jsonToMapIntInt(itemJSON);// 礼包内包含的物品 {item id: item quantity}
		for(Map.Entry<Integer, Integer> entry : itemMap.entrySet()){
			ConfGoods confItem = ConfGoods.get(entry.getKey());
			if(confItem == null){
				Log.temp.error("===ConfItemData 配表错误。not find sn={}", entry.getKey());
				Port.getCurrent().returns("success", false, "reason", Utils.createStr("{}物品不存在", entry.getKey()));
				return false;
			}
		}
		JSONObject joTemp = Utils.toJSONObject(jo.getString("platformCreative"));// 背景图片
		boolean enabled = joTemp.getBooleanValue("enabled");
		String background = joTemp.getString("background");

		PayGift payGift = new PayGift();
		payGift.setHumanId(humanId);
		payGift.setPayId(id);
		payGift.setVipPoints(vipPoints);
		payGift.setAmountToDiamond(amountToDiamond);
		payGift.setPrice(price);
		payGift.setPriceValueRatio(priceValueRatio);
		payGift.setDuration(duration);
		payGift.setPurchaseLimitAmount(purchaseLimitAmount);
		payGift.setItems(itemJSON);
		payGift.setEnabled(enabled);
		payGift.setBackground(background);
		payGift.setCurrency(currency);
		payGift.setCurrencyPrice(currencyPrice);
		payGift.setCurrencyTag(currencyTag);
		payGift.setTemplateId(PF_GM_Manager.convertTemplateId(templateId));
		payGift.setGiftCode(giftCode);

		payGift.setCreateTime(Port.getTime());
		payGift.setEndTime(payGift.getCreateTime() + duration * Time.SEC);
		Log.temp.error("===记录充值payGift={}, json={}", payGift, json);

		return payGiftNew(humanObj, payGift);
	}

	/**
	 * 充值礼包日志
	 * <AUTHOR>
	 * @Date 2023/11/21
	 * @Param
	 */
	public void payGiftLog(Human human, int price, int gold, String giftCode){
		//添加充值日志
		LogOp.log(LogOpChannel.RECHARGE,
				human.getId(),
				Port.getTime(),
				Utils.formatTime(Port.getTime(), "yyyy-MM-dd"),
				price,
				gold,
				human.getAccount(),
				human.getName(),
				human.getLevel(),
				human.getServerId(),
				PF_PAY_Manager.payType2,
				0,
				"充值礼包",
				giftCode
		);
	}

	public boolean payGiftNew(HumanObject humanObj, PayGift payGift){
		if(humanObj.payGiftMap.containsKey(payGift.getPayId())){
			Log.temp.error("====充值订单已存在，humanid={}, account={}, payId={}", humanObj.id, humanObj.account, payGift.getPayId());
			Port.getCurrent().returns("success", false, "reason", "充值订单已存在");
			return false;
		}
		payGift.setId(Port.applyId());
		payGift.persist();
		humanObj.payGiftMap.put(payGift.getPayId(), payGift);

		// 通知客户端
		MsgPayMall.pay_gift_new_s2c.Builder msg = MsgPayMall.pay_gift_new_s2c.newBuilder();
		msg.addDPayGift(createDPayGift(payGift));
		msg.setInit(true);
		humanObj.sendMsg(msg);
		return true;
	}


	public Define.DPayGift createDPayGift(PayGift payGift){
		Define.DPayGift.Builder dInfo = Define.DPayGift.newBuilder();
		dInfo.setPayId(payGift.getPayId());
		dInfo.setAmountToDiamond(payGift.getAmountToDiamond());
		dInfo.setPrice(payGift.getPrice());
		dInfo.setPriceValueRatio(payGift.getPriceValueRatio());
		dInfo.setCloseTime(payGift.getEndTime());
		dInfo.setPurchaseLimitAmount(payGift.getPurchaseLimitAmount());
		dInfo.setBuyNum(payGift.getBuyNum());
		dInfo.setCurrency(payGift.getCurrency());
		dInfo.setCurrencyPrice(payGift.getCurrencyPrice());
		dInfo.setCurrencyTag(payGift.getCurrencyTag());
		dInfo.setTemplateId(payGift.getTemplateId());
		dInfo.setGiftCode(payGift.getGiftCode());

		Map<Integer, Integer> itemMap = Utils.jsonToMapIntInt(payGift.getItems());
		List<Define.p_reward> rewardList = InstanceManager.inst().to_p_rewardList(itemMap);
		dInfo.addAllReward(rewardList);
		dInfo.setEnabled(payGift.isEnabled());
		dInfo.setBackground(payGift.getBackground());
		dInfo.setCreateTime(payGift.getCreateTime());

		return dInfo.build();
	}



	/**
	 * 领取免费礼包
	 * <AUTHOR>
	 * @Date 2023/11/2
	 * @Param
	 */
	public void _msg_pay_gift_draw_c2s(HumanObject humanObj, String payId){
		int tipsSn = getCanDrawTipsSn(humanObj, payId);
		int buyNum = -1;
		if(humanObj.payGiftMap.containsKey(payId)){
			buyNum = humanObj.payGiftMap.get(payId).getBuyNum();
		}
		MsgPayMall.pay_gift_draw_s2c.Builder msg = MsgPayMall.pay_gift_draw_s2c.newBuilder();
		msg.setPayId(payId);
		msg.setBuyNum(buyNum);
		msg.setCode(tipsSn);
		humanObj.sendMsg(msg);
	}


	/**
	 * 领取免费礼包
	 * <AUTHOR>
	 * @Date 2023/11/2
	 * @Param 返回错误码 0成功
	 */
	private int getCanDrawTipsSn(HumanObject humanObj, String payId){
		if(!humanObj.payGiftMap.containsKey(payId)){
			Inform.sendInform(humanObj.id, 200533);	//礼包已过期
			return 200533;
		}
		PayGift payGift = humanObj.payGiftMap.get(payId);
		if(payGift == null){
			Log.temp.error("====充值礼包不存在，humanid={}, account={}, payId={}", humanObj.id, humanObj.account, payId);
			return 0;
		}
		if(!payGift.getTemplateId().equals(ParamKey.PAYGIFT_TYPE_2) && !payGift.getTemplateId().equals(ParamKey.PAYGIFT_TYPE_3)
				&& !payGift.getTemplateId().equals(ParamKey.PAYGIFT_TYPE_4)){
			Inform.sendInform(humanObj.id, 200532);//未满足领取条件
			return 200532;
		}
		if(payGift.getEndTime() <= Port.getTime() || payGift.getBuyNum() >= payGift.getPurchaseLimitAmount()){
			Inform.sendInform(humanObj.id, 200530);
			sendSCPayGiftRemove(humanObj, payGift.getPayId());
			humanObj.payGiftMap.remove(payId);
			return 200530;
		}
		if(payGift.getBuyNum() >= payGift.getPurchaseLimitAmount()){
			payGift.remove();
			humanObj.payGiftMap.remove(payId);
			return 0;
		}
		payGiftReward(humanObj, payGift);
		// 免费要求自动删除
		if(payGift.getBuyNum() >= payGift.getPurchaseLimitAmount()){
			payGift.remove();
		}
		humanObj.payGiftMap.remove(payId);
		return 0;
	}

	@Listener(EventKey.HUMAN_LOGIN_FINISH)
	public void onHUMAN_LOGIN_FINISH(Param param) {
		HumanObject humanObj = param.get("humanObj");
		pocketLine(humanObj);
		long timeNow = Port.getTime();
		MsgPayMall.pay_gift_new_s2c.Builder msg = MsgPayMall.pay_gift_new_s2c.newBuilder();
		List<PayGift> removeList = new ArrayList<>();
		boolean isPopup = false;
		for(PayGift payGift : humanObj.payGiftMap.values()){
			if(payGift.getEndTime() <= timeNow){
				removeList.add(payGift);
				continue;
			}
			if(payGift.getBuyNum() >= payGift.getPurchaseLimitAmount()){
				// 策划要求不根据次数删，走时间删除
				continue;
			}
			if(!payGift.getTemplateId().equals(ParamKey.PAYGIFT_TYPE_1)){
				isPopup = true;
			}
			msg.addDPayGift(createDPayGift(payGift));
		}
		msg.setInit(isPopup);
		humanObj.sendMsg(msg);

		removePayGift(humanObj, removeList);

		checkPayGift(humanObj);
	}

	public void checkPayGift(HumanObject humanObj){
		List<PayLog>  payList = new ArrayList<>(humanObj.dataPers.payLogs);
		for(PayLog payLog : payList){
			if(payLog.getStatus().equals("代办中")){
				String propId = payLog.getPropId();
				ConfPayMall confPayMall = ConfPayMall.getBy(ConfPayMall.K.ios_pid, propId);
				if(confPayMall == null) {
					// 充值礼包
					String payId = payLog.getPropId();
					PayGift payGift = humanObj.payGiftMap.get(payId);
					if(payGift == null){
						Log.temp.error("===payId={}, 数据已经清空或礼包不存在.humanId={}", payId, humanObj.id);
						continue;
					}
					payLog.setStatus("充值成功");
					MallManager.inst().payGiftGiveReward(humanObj, payGift, null);
					continue;
				}
				// 充值表
				payLog.setStatus("代办已补发");
				Event.fire(EventKey.PAY_NOTIFY, "humanObj", humanObj, "sn", confPayMall.sn, "orderId", "", "giftId", "");
			}
		}
	}


	public void pocketLine(HumanObject humanObj){
		List<PocketLine> pocketLineList = new ArrayList<>(humanObj.dataPers.pocketLine);
		for(PocketLine pocketLine : pocketLineList){
			//待办Key  先转类型 确定类型存在
			String pocketLineKey = pocketLine.getModuleName();
			if(PocketLineEventSubKey.RECEIVE_FLOWER.equals(pocketLineKey)
					|| PocketLineEventSubKey.UPDATE_FRIEND.equals(pocketLineKey)){
				continue;
			}
			if(humanObj.pocketIdMap.containsKey(pocketLine.getId())){
				continue;
			}
			// 先加入，后发布事件，避免重复
			humanObj.pocketIdMap.put(pocketLine.getId(), Port.getTime());
			try{
				//发送事件
				Event.fireEx(EventKey.POCKET_LINE_HANDLE_ONE, pocketLineKey, "humanObj", humanObj, "pocketLine", pocketLine);
				Log.temp.error("===处理待办事件, humanId={}, account={}, pocketLineKey={}, pocketLine={}===", humanObj.id, humanObj.account, pocketLineKey, pocketLine);
			}catch(Exception e){
				Log.common.error("代办事件异常，事件[{}], ID[{}], 玩家[{}], 参数[{}]", pocketLineKey,  pocketLine.getId(), humanObj.getHumanId(), pocketLine.getParam());
				Log.game.error("===e={}", e.getStackTrace());
				//发送处理待办结束
				Event.fire(EventKey.POCKET_LINE_HANDLE_END, humanObj);
			} finally {
				pocketLine.remove();  // 无论成功与否，都移除待办
			}
		}

		//发送处理待办结束
		Event.fire(EventKey.POCKET_LINE_HANDLE_END, humanObj);


		// 异常订单处理
		List<PayLog> payLogs = new ArrayList<>(humanObj.dataPers.payLogs);
		for(PayLog payLog: payLogs){
			if(payLog.getStatus().equals("代办中")){
				String propId = payLog.getPropId();

				ConfPayMall confPayMall = ConfPayMall.getBy(ConfPayMall.K.ios_pid, propId);
				if(confPayMall == null){
					Log.temp.error("===ConfPayMall配表错误， not find ios_pid={}", propId);
					continue;
				}
				int sn = confPayMall.sn;
				payLog.setStatus("代办已补发");
				Event.fire(EventKey.PAY_NOTIFY, "humanObj", humanObj, "sn", sn, "orderId", "", "giftId", "");
			}
		}
	}

	public void removePayGift(HumanObject humanObj, List<PayGift> removeList){
		for(PayGift payGift : removeList){
			Log.temp.error("===记录删除充值礼包。payId={}, id={}, humanId={}, openTime={}, endTime={}," +
							" item={}, buyNum={}, duration={}, canBuyNum={}, type={}", payGift.getPayId(),
					payGift.getId(), payGift.getHumanId(), payGift.getCreateTime(), payGift.getEndTime(),
					payGift.getItems(), payGift.getBuyNum(), payGift.getDuration(), payGift.getAmountToDiamond(),
					payGift.getTemplateId());
			String payId = payGift.getPayId();
			humanObj.payGiftMap.remove(payId);
			sendSCPayGiftRemove(humanObj, payId);
			payGift.remove();
		}
	}

	/**
	 * 删除充值礼包
	 * <AUTHOR>
	 * @Date 2023/11/2
	 * @Param
	 */
	public void sendSCPayGiftRemove(HumanObject humanObj, String payId){
		if(!humanObj.isPayGiftRemove){
			return;
		}
		MsgPayMall.pay_gift_remove_s2c.Builder msg = MsgPayMall.pay_gift_remove_s2c.newBuilder();
		msg.setPayId(payId);
		humanObj.sendMsg(msg);
	}

	@Listener(EventKey.HUMAN_RESET_ZERO)
	public void onHumanResetZero(Param param) {
		HumanObject humanObj = param.get("humanObj");
		Collection<ConfPayMall> confPayMallList = GlobalConfVal.getConfPayMalls(PayMallTypeKey.PAY_Type_20);
		Map<Integer,Integer> firstChargeMap = Utils.jsonToMapIntInt(humanObj.operation.mall.getFirstPayGiftTimeMap());

		boolean hasMatchingKey = false;
		for (ConfPayMall confPayMall : confPayMallList) {
			if (firstChargeMap.containsKey(confPayMall.sn)) {
				hasMatchingKey = true;
				break;
			}
		}

		if (hasMatchingKey) {
			sendMsg_pay_mall_info_s2c(humanObj,PayMallTypeKey.PAY_Type_20);
		}
		reduceChargeScore(humanObj, 1);
	}

	public void reduceChargeScore(HumanObject humanObj, int day) {
		if (humanObj.operation.mall.getChargeScore() <= 0) {
			return;
		}
		if (day <= 0) {
			return;
		}
		int[][] reduceScores = Utils.parseIntArray2(ConfGlobal.get(ConfGlobalKey.limitgift_player_charge_point).strValue);
		int score = humanObj.operation.mall.getChargeScore();
		int startIndex = 0;// 积分扣除的起始档位
		for (; startIndex < reduceScores.length - 1; startIndex++) {
			if (score < reduceScores[startIndex][0]) {
				break;
			}
		}
		while (day > 0) {
			score -= reduceScores[startIndex][1];
			if (score < reduceScores[startIndex][0]) {
				startIndex = Math.max(--startIndex, 0);// 积分扣除的档位要向下调整了
			}
			day--;
		}
		humanObj.operation.mall.setChargeScore(Math.max(0, score));
	}

	/**
	 * 活动关闭处理通知客户端关闭
	 * @param param
	 */
	@Listener(EventKey.ACTIVITY_END_SHOW)
	public void onActivityEndShow(Param param) {
		HumanObject humanObj = param.get("humanObj");
		int activitySn = param.get("activitySn");
		int round = param.get("round");
		ConfActivityControl conf = ConfActivityControl.get(activitySn);
		List<Integer> confSnList = GlobalConfVal.actTypePayMallSnMap.get(conf.type);
		if (confSnList == null) {
			return;
		}
		for (Integer confSn : confSnList) {
			int[] conditions = GlobalConfVal.actPayMallConditionMap.get(confSn);
			if (conditions == null) {
				continue;
			}
			ConfPayMall confPayMall = ConfPayMall.get(confSn);
			if (round < conditions[1] || round > conditions[2]) {
				continue;
			}
			int buyNum = buyPayMallNum(humanObj, confPayMall.sn, confPayMall.reset);
			MsgPayMall.pay_mall_update_s2c.Builder msg = MsgPayMall.pay_mall_update_s2c.newBuilder();
			msg.setPayMall(to_p_pay_mall_info(confPayMall.sn, confPayMall.type, buyNum, confPayMall.reset, rewardType_0));
			humanObj.sendMsg(msg);
		}
	}

	/**
	 * 活动开启后给玩家推送相关礼包
	 */
	@Listener(EventKey.ACTIVITY_OPEN)
	public void onActivityOpen(Param param) {
		HumanObject humanObj = param.get("humanObj");
		ActivityVo activityVo = param.get("activityVo");
		sendAndAddActivityConditionPayMallInfo(humanObj, activityVo.activitySn);
	}

	public void sendAndAddActivityConditionPayMallInfo(HumanObject humanObj, int activitySn) {
		ConfActivityControl conf = ConfActivityControl.get(activitySn);
		if (!humanObj.isModUnlock(conf.newfunctionID)) {
			return;
		}
		List<Integer> confSnList = GlobalConfVal.actTypePayMallSnMap.get(conf.type);
		if (confSnList == null) {
			return;
		}
		Map<Integer, ActivityControlObjectData> dataMap = humanObj.operation.activityControlData.getControlObjectDataMap();
		ActivityControlObjectData data = dataMap.get(conf.type);
		long endTime = ((long) data.getActControlData().getCloseTime()) * Time.SEC;
		Map<Integer, Integer> giftMap = getLimitTimeGifMap(humanObj);
		for (Integer confSn : confSnList) {
			ConfPayMall confPayMall = ConfPayMall.get(confSn);
			// 7类型的礼包有自己单独处理，这边不做处理
			if (confPayMall.type == PayMallTypeKey.PAY_Type_7) {
				continue;
			}
			if (!isPayCondition(humanObj, confPayMall, true)) {
				continue;
			}
			// 限不限时
			if (!giftMap.containsKey(confSn)) {
				addLimitTimeGift(humanObj, confPayMall, endTime);
			}
			int buyNum = buyPayMallNum(humanObj, confPayMall.sn, confPayMall.reset);
			int rewardType = buyNum >= confPayMall.buy_times ? rewardType_2 : rewardType_1;
			MsgPayMall.pay_mall_update_s2c.Builder msg = MsgPayMall.pay_mall_update_s2c.newBuilder();
			msg.setPayMall(to_p_pay_mall_info(confPayMall.sn, confPayMall.type, buyNum, confPayMall.reset, rewardType, (int) (endTime / Time.SEC)));
			humanObj.sendMsg(msg);
		}
	}

	/**
	 * 登陆处理玩家的活动礼包，包含限时礼包生成和推送消息
	 */
	public void sendAndAddActivityConditionPayMallInfo(HumanObject humanObj) {
		// 遍历跟活动关联的礼包，决定哪些礼包要新加，哪些礼包的类型要准备发消息
		Map<Integer, Integer> giftMap = getLimitTimeGifMap(humanObj);
		Set<Integer> sendPayMallTypeSet = new HashSet<>();
		Map<Integer, ActivityControlObjectData> dataMap = humanObj.operation.activityControlData.getControlObjectDataMap();
		Map<Integer, List<Integer>> actTypePayMallSnMap = GlobalConfVal.actTypePayMallSnMap;
		for (Map.Entry<Integer, List<Integer>> entry : actTypePayMallSnMap.entrySet()) {
			int actType = entry.getKey();
			ActivityControlObjectData data = dataMap.get(actType);
			if (data == null) {
				continue;
			}
			ActControlData controlData = data.getActControlData();
			ConfActivityControl confActivity = ConfActivityControl.get(controlData.getActivitySn());
			// 没开的活动不管
			if (!humanObj.openActivitySnList.contains(confActivity.sn)) {
				continue;
			}
			if (!humanObj.isModUnlock(confActivity.newfunctionID)) {
				continue;
			}
			// 取出活动类型对应的充值表sn列表
			List<Integer> confSnList = entry.getValue();
			if (confSnList == null) {
				continue;
			}
			long endTime = ((long)controlData.getCloseTime()) * Time.SEC;
			for (Integer confPayMallSn : confSnList) {
				ConfPayMall confPayMall = ConfPayMall.get(confPayMallSn);
				// 7类型的礼包有自己单独处理，这边不做处理
				if (confPayMall.type == PayMallTypeKey.PAY_Type_7) {
					continue;
				}
				if (!isPayCondition(humanObj, confPayMall, true)) {
					continue;
				}
				if (!giftMap.containsKey(confPayMallSn)) {
					addLimitTimeGift(humanObj, confPayMall, endTime);
				}
				sendPayMallTypeSet.add(confPayMall.type);
			}
		}
		// 根据前面的发送数据
		for (Integer payMallType : sendPayMallTypeSet) {
			sendMsg_pay_mall_info_s2c(humanObj, payMallType);
		}
	}

	/**
	 * 检测礼包推送
	 */
	public void _msg_pay_mall_check_push_c2s(HumanObject humanObj, int payMallType, String conditionType, int args1, String args2) {
		if (payMallType == PayMallTypeKey.PAY_Type_26) {
			switch (conditionType) {
				case PayMallTypeKey.pay_condition_shortage: {
					pushLimitGift26Shortage(humanObj, args1);
				}
				break;
				case PayMallTypeKey.pay_condition_draw: {
					pushLimitGift26Draw(humanObj, args1);
				}
				break;
				case PayMallTypeKey.pay_condition_any_shortage: {
					// 处理多个物品ID的情况
					if (args2 != null && !args2.isEmpty()) {
						pushLimitGift26AnyShortage(humanObj, args2);
					}
				}
				break;
			}
		}
	}

	// region 26类型礼包推送
	/**
	 * 是否包含26类型礼包
	 * @param humanObj
	 * @param loopPaySnList		循环的礼包sn
	 * @return
	 */
	public boolean containGift26(HumanObject humanObj, List<Integer> loopPaySnList) {
		Map<Integer, Integer> limitTimeGifMap = getLimitTimeGifMap(humanObj);
		Set<Integer> paySnSet = limitTimeGifMap.keySet();
		if (loopPaySnList != null && loopPaySnList.size() != 0) {
			for (Integer sn : loopPaySnList) {
				if (paySnSet.contains(sn)) {
					// 本身已经推礼包就不管
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 返回新推的26类型礼包sn
	 */
	public int getNewGift26Sn(HumanObject humanObj, List<Integer> loopPaySnList, String key) {
		int newPaySn = 0;
		if (loopPaySnList == null || loopPaySnList.size() == 0) {
			return newPaySn;
		}
		boolean isOneBuy = false;
		for (Integer sn : loopPaySnList) {
			ConfPayMall conf = ConfPayMall.get(sn);
			int num = buyPayMallNum(humanObj, sn, conf.reset);
			if (num >= conf.buy_times) {
				isOneBuy = true;
				break;
			}
		}
		if (isOneBuy) {
			// 有一个买了就不推其他的
			return newPaySn;
		}
		int index = 0;
		for (int i = 0; i < loopPaySnList.size(); i++) {
			index = i;
			int sn = loopPaySnList.get(i);
			ConfPayMall conf = ConfPayMall.get(sn);
			if (isPayCondition(humanObj, conf, true)) {
				newPaySn = sn;
			}
			if (humanObj.operation.mall.getChargeScore() <= conf.parameter) {
				break;
			}
		}
		if (newPaySn == 0) {
			return newPaySn;
		}
		// 根据购买次数浮动档位
		ConfPayMall conf = ConfPayMall.get(newPaySn);
		int addGearVal = humanObj.operation.getGearRange(key);
		if (addGearVal > 0) {
			addGearVal = Math.min(addGearVal, conf.gear_range[1]);
			index = Math.min(index + addGearVal, loopPaySnList.size() - 1);
		} else if (addGearVal < 0) {
			addGearVal = Math.max(addGearVal, conf.gear_range[0]);
			index = Math.max(index + addGearVal, 0);
		}
		newPaySn = loopPaySnList.get(index);
		conf = ConfPayMall.get(newPaySn);
		if (!isBuyCondition(humanObj, conf)) {
			newPaySn = 0;
		}
		return newPaySn;
	}

	/**
	 * 物品不足时推送26类型礼包
	 */
	public void pushLimitGift26Shortage(HumanObject humanObj, int itemSn) {
		List<Integer> loopPaySnList = GlobalConfVal.shortLoopPaySnMap.get(itemSn);
		if (containGift26(humanObj, loopPaySnList)) {
			// 本身有相关礼包
			return;
		}
		// 今天是否有26类型的该物品不足礼包过期
		String key = PayMallTypeKey.pay_condition_shortage + itemSn;
		if (humanObj.isGift26GroupExpire(key)) {
			return;
		}
		// 准备推终身礼包
		int newPaySn = getNewGift26Sn(humanObj, loopPaySnList, key);
		if (newPaySn == 0) {
			return;
		}
		ConfPayMall conf = ConfPayMall.get(newPaySn);
		addLimitTimeGift(humanObj, conf);
		sendMsg_pay_mall_update_s2c(humanObj, newPaySn, conf);
	}

	/**
	 * 抽卡推送26类型礼包
	 */
	public void pushLimitGift26Draw(HumanObject humanObj, int drawType) {
		List<Integer> loopPaySnList = GlobalConfVal.drawLoopPaySnMap.get(drawType);
		if (containGift26(humanObj, loopPaySnList)) {
			// 本身有相关礼包
			return;
		}
		// 今天是否有26类型的抽卡礼包过期
		String key = PayMallTypeKey.pay_condition_draw + drawType;
		if (humanObj.isGift26GroupExpire(key)) {
			return;
		}
		int newPaySn = getNewGift26Sn(humanObj, loopPaySnList, key);
		if (newPaySn == 0) {
			return;
		}
		ConfPayMall conf = ConfPayMall.get(newPaySn);
		addLimitTimeGift(humanObj, conf);
		sendMsg_pay_mall_update_s2c(humanObj, newPaySn, conf);
		// 抽卡类型的礼包还要设置此时的抽卡数量，为了下次计算
		humanObj.setPushGift26DrawNum(drawType, true);
	}

	/**
	 * 任意物品不足时推送26类型礼包
	 */
	public void pushLimitGift26AnyShortage(HumanObject humanObj, String itemSnStr) {
		// 直接使用物品列表字符串作为key
		List<Integer> loopPaySnList = GlobalConfVal.anyShortageItemsPaySnMap.get(itemSnStr);
		if (loopPaySnList == null || loopPaySnList.isEmpty()) {
			return;
		}

		// 检查是否已经有相关礼包
		if (containGift26(humanObj, loopPaySnList)) {
			// 本身有相关礼包就不管
			return;
		}

		// 今天是否有26类型的该物品不足礼包过期
		String key = PayMallTypeKey.pay_condition_any_shortage + "_" + itemSnStr;
		if (humanObj.isGift26GroupExpire(key)) {
			return;
		}

		// 准备推终身礼包
		int newPaySn = getNewGift26Sn(humanObj, loopPaySnList, key);
		if (newPaySn == 0) {
			return;
		}

		ConfPayMall conf = ConfPayMall.get(newPaySn);
		addLimitTimeGift(humanObj, conf);
		sendMsg_pay_mall_update_s2c(humanObj, newPaySn, conf);
	}

	/**
	 * 通用条件推送26类型礼包
	 */
	public void pushLimitGift26Common(HumanObject humanObj, String conditionStr) {
		// 直接使用条件字符串作为key
		List<Integer> loopPaySnList = GlobalConfVal.commonConditionPaySnMap.get(conditionStr);
		if (loopPaySnList == null || loopPaySnList.isEmpty()) {
			return;
		}

		// 检查是否已经有相关礼包
		if (containGift26(humanObj, loopPaySnList)) {
			// 本身有相关礼包就不管
			return;
		}

		// 今天是否有26类型的该条件礼包过期
		String key = PayMallTypeKey.pay_condition_common + "_" + conditionStr;
		if (humanObj.isGift26GroupExpire(key)) {
			return;
		}

		// 准备推终身礼包
		int newPaySn = getNewGift26Sn(humanObj, loopPaySnList, key);
		if (newPaySn == 0) {
			return;
		}

		ConfPayMall conf = ConfPayMall.get(newPaySn);
		addLimitTimeGift(humanObj, conf);
		sendMsg_pay_mall_update_s2c(humanObj, newPaySn, conf);

	}

	public String getCommonConditionKey(String strType, int level) {
		for (String key : GlobalConfVal.commonConditionPaySnMap.keySet()) {
			String[] keyArray = key.split(",");
			if(keyArray.length != 2){
				continue;
			}
			if (keyArray[0].equals(strType) && level >= Utils.intValue(keyArray[1])) {
				return key;
			}
		}
		return null;
	}

	public String getConditionStr(String strType, String... params) {
		for (String key : params) {
			strType += "," + key;
		}
		return strType;
	}

	public void pushLimitGift26FlyEgg(HumanObject humanObj) {
		List<Integer> loopPaySnList = GlobalConfVal.flyEggLoopPaySnList;
		if (containGift26(humanObj, loopPaySnList)) {
			// 本身有相关礼包
			return;
		}
		// 今天是否有26类型的抽卡礼包过期
		String key = PayMallTypeKey.pay_condition_flyEgg + "0";
		if (humanObj.isGift26GroupExpire(key)) {
			return;
		}
		int newPaySn = getNewGift26Sn(humanObj, loopPaySnList, key);
		if (newPaySn == 0) {
			return;
		}
		ConfPayMall conf = ConfPayMall.get(newPaySn);
		addLimitTimeGift(humanObj, conf);
		sendMsg_pay_mall_update_s2c(humanObj, newPaySn, conf);
	}

	public void pushLimitGift26FashionSkin(HumanObject humanObj, int skinSn, int skinLevel) {
		String snLvStr = skinSn +"_"+ skinLevel;
		List<Integer> loopPaySnList = GlobalConfVal.fashionSkinLvPaySnMap.get(snLvStr);
		if (loopPaySnList == null) {
			return;
		}
		if (containGift26(humanObj, loopPaySnList)) {
			// 本身有相关礼包
			return;
		}
		// 今天是否有26类型的抽卡礼包过期
		String key = PayMallTypeKey.pay_condition_flyEgg + snLvStr;
		if (humanObj.isGift26GroupExpire(key)) {
			return;
		}
		int newPaySn = getNewGift26Sn(humanObj, loopPaySnList, key);
		if (newPaySn == 0) {
			return;
		}
		ConfPayMall conf = ConfPayMall.get(newPaySn);
		addLimitTimeGift(humanObj, conf);
		sendMsg_pay_mall_update_s2c(humanObj, newPaySn, conf);
	}

	public void buyLimitGift26(HumanObject humanObj, int paySn) {
		ConfPayMall conf = ConfPayMall.get(paySn);
		sendMsg_pay_mall_update_s2c(humanObj, paySn, conf);
		Mall mall = humanObj.operation.mall;
		Map<Integer, Integer> limitTimeGifMap = Utils.jsonToMapIntInt(mall.getLimitTimeGiftMap());
		limitTimeGifMap.remove(paySn);
		humanObj.operation.mall.setLimitTimeGiftMap(Utils.mapIntIntToJSON(limitTimeGifMap));

		String[] conditions = GlobalConfVal.payConditionsMap.get(paySn);
		String key = conditions[0] + conditions[1];
		int gear = humanObj.operation.getGearRange(key);
		if (gear + 1 <= conf.gear_range[1]) {
			humanObj.operation.addGearRange(key, 1);
		}

		// 往下推礼包
		humanObj.addGift26BuyNum(key);
		int buyNum = humanObj.getGift26BuyNum(key);
		int value = ConfGlobal.get(ConfGlobalKey.shortagegift_push_gear).value;
		if (buyNum >= value) {
			return;
		}
		List<Integer> loopPaySnList = null;
		if (PayMallTypeKey.pay_condition_shortage.equals(conditions[0])) {
			loopPaySnList = GlobalConfVal.shortLoopPaySnMap.get(Utils.intValue(conditions[1]));
		} else if (PayMallTypeKey.pay_condition_draw.equals(conditions[0])) {
			loopPaySnList = GlobalConfVal.drawLoopPaySnMap.get(Utils.intValue(conditions[1]));
		} else if (PayMallTypeKey.pay_condition_flyEgg.equals(conditions[0])) {
			loopPaySnList = GlobalConfVal.flyEggLoopPaySnList;
		} else if (PayMallTypeKey.pay_condition_fashionSkin.equals(conditions[0])){
			loopPaySnList = GlobalConfVal.fashionSkinLvPaySnMap.get(conditions[1]);
		} else if (PayMallTypeKey.pay_condition_any_shortage.equals(conditions[0])) {
			loopPaySnList = GlobalConfVal.anyShortageItemsPaySnMap.get(conditions[1]);
		} else if (PayMallTypeKey.pay_condition_common.equals(conditions[0])) {
			loopPaySnList = GlobalConfVal.commonConditionPaySnMap.get(conditions[1]);
		}
		if (loopPaySnList == null || loopPaySnList.size() == 0) {
			return;
		}
		int index = -1;
		for (int i = 0; i < loopPaySnList.size(); i++) {
			if (loopPaySnList.get(i) == paySn) {
				index = i;
				break;
			}
		}
		if (index == -1 || index + 1 >= loopPaySnList.size()) {
			return;
		}
		int	nextPaySn = loopPaySnList.get(index + 1);
		if (nextPaySn == 0) {
			return;
		}
		ConfPayMall confPayMallNew = ConfPayMall.get(nextPaySn);
		addLimitTimeGift(humanObj, confPayMallNew);
		sendMsg_pay_mall_update_s2c(humanObj, nextPaySn, confPayMallNew);
	}
	// endregion 26类型礼包推送

	// region 自选礼包设置奖励
	/**
	 * 请求自选礼包相关消息
	 */
	public void _msg_pay_mall_custom_info_c2s(HumanObject humanObj) {
		Map<Integer, Map<Integer, List<Integer>>> payCustomGiftMap = humanObj.operation.getPayCustomGiftMap();
		MsgPayMall.pay_mall_custom_mall_info_s2c.Builder msg = MsgPayMall.pay_mall_custom_mall_info_s2c.newBuilder();
		for (Map.Entry<Integer, Map<Integer, List<Integer>>> entry : payCustomGiftMap.entrySet()) {
			int sn = entry.getKey();
			ConfPayMall conf =  ConfPayMall.get(sn);
			if (!isPayCondition(humanObj, conf, true)) {
				continue;
			}
			Map<Integer, List<Integer>> customGiftMap = entry.getValue();
			if (customGiftMap == null || customGiftMap.isEmpty()) {
				continue;
			}
			Define.p_custom_mall_info.Builder pCustomMallInfo = Define.p_custom_mall_info.newBuilder();
			pCustomMallInfo.setId(sn);
			for (Map.Entry<Integer, List<Integer>> subEntry : customGiftMap.entrySet()) {
				int index = subEntry.getKey();
				int[] items = subEntry.getValue().stream().mapToInt(Integer::intValue).toArray();
				Define.p_key_value.Builder kv = Define.p_key_value.newBuilder();
				kv.setK(index);
				kv.setV(items[0]);
				pCustomMallInfo.addList(kv);
			}
			msg.addList(pCustomMallInfo);
		}
		humanObj.sendMsg(msg);
	}

	/**
	 * 自选礼包设置自选奖励
	 */
	public void _msg_pay_mall_custom_set_c2s(HumanObject humanObj, Define.p_custom_mall_info pInfo) {
		int paySn = pInfo.getId();
		ConfPayMall confPayMall = ConfPayMall.get(paySn);
		if (!isPayCondition(humanObj, confPayMall, true)) {
			Inform.sendMsg_error(humanObj, 1);
			return;
		}
		List<Define.p_key_value> customGiftList = pInfo.getListList();
		humanObj.operation.setPayCustomGift(paySn, customGiftList);
		MsgPayMall.pay_mall_custom_mall_set_s2c.Builder msg = MsgPayMall.pay_mall_custom_mall_set_s2c.newBuilder();
		msg.setUpdateInfo(pInfo);
		humanObj.sendMsg(msg);
	}
	// endregion 自选礼包设置奖励

	// region 折扣礼包
	public void buyDiscountGift27(HumanObject humanObj, int paySn, List<Define.p_reward> rewardList, List<Define.p_key_value> extList) {
		ConfGiftDiscount conf = ConfGiftDiscount.get(paySn);// sn和充值表sn对应
		if (conf == null) {
			return;
		}
		// 随机倍率
		int index = Util.randomByWeight(conf.rateWeight);
		int multi = conf.rate[index];

		// 计算奖励并发奖励
		Map<Integer, Integer> rewardMap = new HashMap<>(conf.gift_reward.length);
		rewardMap = Utils.intArrToIntMap(rewardMap, conf.gift_reward);
		for (Integer sn : rewardMap.keySet()) {
			rewardMap.put(sn, rewardMap.getOrDefault(sn, 0) * multi);
		}
		ProduceManager.inst().produceAdd(humanObj, rewardMap, MoneyItemLogKey.充值);
		rewardList.addAll(InstanceManager.inst().to_p_rewardList(rewardMap));

		// 倍率记起来
		extList.add(HumanManager.inst().to_p_key_value(ParamKey.payExtType_1, multi).build());

		// 处理播报
		if (conf.rateBroadcast == 0 || multi < conf.rateBroadcast) {
			return;
		}
		String[] conditions = GlobalConfVal.payConditionsMap.get(paySn);
		if (conditions == null || conditions.length < 3) {
			return;
		}
		if (!PayMallTypeKey.pay_condition_act_type.equals(conditions[0]) || !isPayConditon(humanObj, ConfPayMall.get(paySn))) {
			return;
		}
		JSONObject json = new JSONObject();
		json.put("id", humanObj.getHumanId());
		json.put("n", humanObj.getHuman().getName());
		json.put("v", multi);
		json.put("s", paySn);
		json.put("t", Port.getTime());
		json.put("hide", Utils.jsonToMapIntLong(humanObj.getHumanExtInfo().getSettingMap()).getOrDefault(HumanSettingConstants.BroadCastHideName, 0L));
		int[] ints = GlobalConfVal.actPayMallConditionMap.get(paySn);
		int actType = ints[0];
		ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(actType);
		ActivityManager.inst().addActivityBroadcast(humanObj, controlData.getActControlData(), json.toJSONString());

		MsgAct.act_broadcast_s2c.Builder msg = MsgAct.act_broadcast_s2c.newBuilder();
		msg.setActType(actType);
		msg.setOperationType(1);
		msg.addReportList(ActivityManager.inst().to_act_broadcast_report(json));
		humanObj.sendMsg(msg);
	}
	// endregion 折扣礼包

	/**
	 * 是否满足充值表的buy_condition
	 */
	public boolean isBuyCondition(HumanObject humanObj, ConfPayMall confPayMall) {
		if (confPayMall.buy_condition == null || confPayMall.buy_condition.length == 0) {
			return true;
		}
		int type = confPayMall.buy_condition[0];
		int param1 = confPayMall.buy_condition[1];
		switch (type) {
			case PayMallTypeKey.BUY_CONDITION_TYPE_1:
				return humanObj.isModUnlock(param1);
		}
		return false;
	}
}
