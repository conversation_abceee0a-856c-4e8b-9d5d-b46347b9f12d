package org.gof.demo.worldsrv.blackMarket;

import com.alibaba.fastjson.JSONObject;
import com.google.protobuf.InvalidProtocolBufferException;
import org.gof.core.Port;
import org.gof.core.Record;
import org.gof.core.dbsrv.DB;
import org.gof.core.dbsrv.redis.CrossRedis;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.core.scheduler.ScheduleTask;
import org.gof.core.support.Config;
import org.gof.core.support.Utils;
import org.gof.demo.distr.DistrKit;
import org.gof.demo.distr.admin.AdminCenterManager;
import org.gof.demo.distr.admin.AdminCenterServiceProxy;
import org.gof.demo.distr.cross.domain.CrossType;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.bridgeEntity.BlackMarket;
import org.gof.demo.worldsrv.common.GamePort;
import org.gof.demo.worldsrv.common.GameServiceBase;
import org.gof.demo.worldsrv.config.ConfActivityControl;
import org.gof.demo.worldsrv.config.ConfBlackMarket;
import org.gof.demo.worldsrv.config.ConfMall;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.Util;

import java.util.*;

@DistrClass(
        servId = D.SERV_BLACK_MARKET,
        importClass = {
                List.class, CrossType.class,
        }
)
public class BlackMarketCrossService extends GameServiceBase {
    public static final String JOB_NAME_REFRESH = "BlackMarketRefresh";
    public static final String JOB_NAME_PRE_REWARD = "BlackMarketPreReward";
    public static final CrossType crossType = CrossType.cross_black_market;

    // Map<玩法分组, 黑市活动sn(为0或者不存在代表活动没开)>
    private Map<Integer, Integer> groupActSnMap = new HashMap<>();
    // Map<玩法分组, Map<黑市表sn, 黑市存储对象>>
    private Map<Integer, Map<Integer, BlackMarket>> groupEntityMap = new HashMap<>();
    // Map<玩法分组, Map<黑市表sn, Map<玩家ID, 购买数量>>>预购
    private Map<Integer, Map<Integer, Map<Long, Integer>>> groupMallJoinMap = new HashMap<>();
    // Map<玩法分组, Map<黑市表sn, Map<玩家ID, 玩家名称>>>预购
    private Map<Integer, Map<Integer, Map<Long, String>>> groupMallJoinNameMap = new HashMap<>();
    // Map<玩法分组, Map<黑市表sn, Map<玩家ID, 玩家名称是否隐藏>>>预购
    private Map<Integer, Map<Integer, Map<Long, Boolean>>> groupMallJoinHideMap = new HashMap<>();
    // Map<玩法分组, 播报>
    private Map<Integer, List<Define.p_act_broadcast_report>> groupBroadCastMap = new HashMap<>();

    /**
     * 构造函数
     */
    public BlackMarketCrossService(GamePort port) {
        super(port);
    }

    /**
     * 初始化
     */
    @Override
    public void init() {
        Log.activity.info("[黑市]Service init");
    }

    /**
     * 刷新黑市
     */
    private void refresh(BlackMarket blackMarket) {
        if (blackMarket.getNextUpdateTime() != 0) {
            long currTime = Port.getTime();
            if (currTime < blackMarket.getNextUpdateTime()) {
                Log.activity.error("[黑市]刷新黑市, 未到刷新时间, actSn={}, sn={}, time={} ", blackMarket.getActivitySn(), blackMarket.getSn(), blackMarket.getNextUpdateTime());
                return;
            }
        }
        ConfBlackMarket conf = ConfBlackMarket.get(blackMarket.getSn());
        if (conf == null) {
            Log.activity.error("[黑市]刷新黑市, 配置不存在, sn={}", blackMarket.getSn());
            return;
        }
        Log.activity.info("[黑市]刷新黑市, actSn={}, sn={}, id={} ", blackMarket.getActivitySn(), blackMarket.getSn(), blackMarket.getId());
        int goodsIndex = Utils.getRandRange(conf.goods);
        blackMarket.setGoodsIndex(goodsIndex);
        blackMarket.setBuyNum(0);
        blackMarket.setOpenPre(false);
        blackMarket.setNextUpdateTime(ActivityManager.inst().getNextFreshTime(blackMarket.getActivitySn()));
        blackMarket.setNextOpenPreTime(ActivityManager.inst().getNextOpenPreTime(blackMarket.getActivitySn()));
        blackMarket.setJoinHumanList(new byte[0]);// 清空玩家购买记录
        groupMallJoinMap.computeIfAbsent(blackMarket.getGroupId(), k -> new HashMap<>()).remove(blackMarket.getSn());
        groupMallJoinNameMap.computeIfAbsent(blackMarket.getGroupId(), k -> new HashMap<>()).remove(blackMarket.getSn());
        groupMallJoinHideMap.computeIfAbsent(blackMarket.getGroupId(), k -> new HashMap<>()).remove(blackMarket.getSn());
    }

    /**
     * 处理预购开奖
     */
    private List<Define.p_act_broadcast_report> openPreReward(BlackMarket blackMarket) {
        ConfBlackMarket conf = ConfBlackMarket.get(blackMarket.getSn());
        if (conf == null) {
            Log.activity.error("[黑市]预购开奖, 配置不存在, sn={}", blackMarket.getSn());
            return null;
        }
        if (conf.cell_type != 2) {
            return null;// 非预购的不管
        }
        long currTime = Port.getTime();
        if (currTime < blackMarket.getNextOpenPreTime()) {
            Log.activity.error("[黑市]预购开奖, 未到开奖时间, actSn={}, id={}, time={} ", blackMarket.getActivitySn(), blackMarket.getId(), blackMarket.getNextOpenPreTime());
            return null;// 未到开奖时间
        }
        if (blackMarket.isOpenPre()) {
            Log.activity.error("[黑市]预购开奖, 已开过奖励, actSn={}, id={}", blackMarket.getActivitySn(), blackMarket.getId());
            return null;
        }
        Log.activity.info("[黑市]预购开奖, actSn={}, id={}, currTime={}", blackMarket.getActivitySn(), blackMarket.getId(), currTime);
        blackMarket.setOpenPre(true);
        Map<Integer, Map<Long, Integer>> mallJoinMap = groupMallJoinMap.getOrDefault(blackMarket.getGroupId(), new HashMap<>());
        Map<Long, Integer> joinMap = mallJoinMap.getOrDefault(blackMarket.getSn(), new HashMap<>());
        if (joinMap.isEmpty()) {
            Log.activity.error("[黑市]预购开奖, 无人参与, actSn={}, id={}", blackMarket.getActivitySn(), blackMarket.getId());
            return null;
        }
        // 开奖, 构造玩家和权重列表
        List<Long> humanIdList = new ArrayList<>();
        List<Integer> humanWeightList = new ArrayList<>();
        for (Map.Entry<Long, Integer> entry : joinMap.entrySet()) {
            humanIdList.add(entry.getKey());
            humanWeightList.add(entry.getValue());
        }
        List<Define.p_act_broadcast_report> reportList = new ArrayList<>();
        try {
            Map<Integer, Map<Long, Integer>> serverRewardHumanMap = new HashMap<>();// 发奖Map<服务器id, Map<玩家id, 奖励次数>>
            for (int i = 0; i < conf.pre_order; i++) {
                int index = Utils.randomByWeight(humanWeightList);
                long humanId = humanIdList.get(index);
                int serverId = Util.getServerIdByHumanId(humanId);
                Map<Long, Integer> rewardHumanMap = serverRewardHumanMap.computeIfAbsent(serverId, k -> new HashMap<>());
                rewardHumanMap.put(humanId, rewardHumanMap.getOrDefault(humanId, 0) + 1);
                // 玩家购买次数减一, 玩家购买次数为0, 从列表中移除
                humanWeightList.set(index, humanWeightList.get(index) - 1);
                if (humanWeightList.get(index) == 0) {
                    humanIdList.remove(index);
                    humanWeightList.remove(index);
                }
            }
            Log.activity.info("[黑市]预购开奖, actSn={}, sn={}, 结果={}", blackMarket.getActivitySn(), blackMarket.getSn(), Utils.mapIntMapLongToJSON(serverRewardHumanMap));
            // 根据服务器id分组处理
            Map<Integer, Map<Long, Integer>> serverReturnHumanMap = new HashMap<>();// 退还Map<服务器id, Map<玩家id, 购买次数>>
            for (int i = 0; i < humanIdList.size(); i++) {
                long humanId = humanIdList.get(i);
                int num = humanWeightList.get(i);
                int serverId = Util.getServerIdByHumanId(humanId);
                serverReturnHumanMap.computeIfAbsent(serverId, k -> new HashMap<>()).put(humanId, num);
            }
            // 收集全部的服务器id
            Set<Integer> serverIdSet = new HashSet<>();
            serverIdSet.addAll(serverRewardHumanMap.keySet());
            serverIdSet.addAll(serverReturnHumanMap.keySet());
            List<Integer> serverIdList = new ArrayList<>(serverIdSet);
            // 遍历每个服务器，发送奖励或者退还消耗
            for (int i = 0; i < serverIdList.size(); i++) {
                int serverId = serverIdList.get(i);
                Log.activity.info("[黑市]预购开奖, 开始给各个服务器发送消息, serverId={}, actSn={}, sn={}", serverId, blackMarket.getActivitySn(), blackMarket.getSn());
                int rewardIndex = 0;
                int returnIndex = 0;
                Map<Long, Integer> rewardHumanNumMap = serverRewardHumanMap.getOrDefault(serverId, new HashMap<>());
                List<Long> rewardHumanList = new ArrayList<>(rewardHumanNumMap.keySet());
                List<Long> rewardList = new ArrayList<>();
                Map<Long, Integer> returnHumanNumMap = serverReturnHumanMap.getOrDefault(serverId, new HashMap<>());
                List<Long> returnHumanList = new ArrayList<>(returnHumanNumMap.keySet());
                List<Long> returnList = new ArrayList<>();
                // 每500个分页处理一次
                int j = 0, k = 0;
                while (rewardIndex < rewardHumanList.size() || returnIndex < returnHumanList.size()) {
                    if (rewardIndex < rewardHumanList.size()) {
                        rewardList.add(rewardHumanList.get(rewardIndex));
                        rewardIndex++;
                    }
                    if (returnIndex < returnHumanList.size()) {
                        returnList.add(returnHumanList.get(returnIndex));
                        returnIndex++;
                    }
                    j++;
                    if (j == 500) {
                        j = 0;
                        List<Long> rewardTempList = new ArrayList<>(rewardList);
                        List<Long> returnTempList = new ArrayList<>(returnList);
                        rewardList.clear();
                        returnList.clear();
                        scheduleOnce(new ScheduleTask() {
                            @Override
                            public void execute() {
                                HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(serverId));
                                if (!rewardTempList.isEmpty()) {
                                    Map<Long, Integer> rewardHumanMap = new HashMap<>();
                                    for (long humanId : rewardTempList) {
                                        rewardHumanMap.put(humanId, rewardHumanNumMap.get(humanId));
                                    }
                                    prx.sendBlackMarketPreReward(rewardHumanMap, blackMarket.getSn(), blackMarket.getGoodsIndex(), true);
                                }
                                if (!returnTempList.isEmpty()) {
                                    Map<Long, Integer> returnHumanMap = new HashMap<>();
                                    for (long humanId : returnTempList) {
                                        returnHumanMap.put(humanId, returnHumanNumMap.get(humanId));
                                    }
                                    prx.sendBlackMarketPreReward(returnHumanMap, blackMarket.getSn(), blackMarket.getGoodsIndex(), false);
                                }
                            }
                        }, i * 200L + k * 10L);
                        k++;
                    }
                }
                if (j != 0) {
                    List<Long> rewardTempList = new ArrayList<>(rewardList);
                    List<Long> returnTempList = new ArrayList<>(returnList);
                    rewardList.clear();
                    returnList.clear();
                    scheduleOnce(new ScheduleTask() {
                        @Override
                        public void execute() {
                            HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(serverId));
                            if (!rewardTempList.isEmpty()) {
                                Map<Long, Integer> rewardHumanMap = new HashMap<>();
                                for (long humanId : rewardTempList) {
                                    rewardHumanMap.put(humanId, rewardHumanNumMap.get(humanId));
                                }
                                prx.sendBlackMarketPreReward(rewardHumanMap, blackMarket.getSn(), blackMarket.getGoodsIndex(), true);
                            }
                            if (!returnTempList.isEmpty()) {
                                Map<Long, Integer> returnHumanMap = new HashMap<>();
                                for (long humanId : returnTempList) {
                                    returnHumanMap.put(humanId, returnHumanNumMap.get(humanId));
                                }
                                prx.sendBlackMarketPreReward(returnHumanMap, blackMarket.getSn(), blackMarket.getGoodsIndex(), false);
                            }
                        }
                    }, i * 200L + k * 10L);
                }
            }
            // 构造播报
            Map<Long, String> joinNameMap = groupMallJoinNameMap.getOrDefault(blackMarket.getGroupId(), new HashMap<>()).getOrDefault(blackMarket.getSn(), new HashMap<>());
            Map<Long, Boolean> joinHideMap = groupMallJoinHideMap.getOrDefault(blackMarket.getGroupId(), new HashMap<>()).getOrDefault(blackMarket.getSn(), new HashMap<>());
            for (Map.Entry<Integer, Map<Long, Integer>> entrySet : serverRewardHumanMap.entrySet()) {
                for (Map.Entry<Long, Integer> entry : entrySet.getValue().entrySet()) {
                    long humanId = entry.getKey();
                    int num = entry.getValue();
                    String humanName = joinNameMap.getOrDefault(humanId, "");
                    boolean isHide = joinHideMap.getOrDefault(humanId, false);
                    reportList.add(addNewBroadCast(blackMarket.getGroupId(), blackMarket.getSn(), num, blackMarket.getGoodsIndex(), humanId, humanName, isHide));
                }
            }
        } catch (Exception e) {
            Log.activity.error("[黑市]预购开奖异常, actSn={}, sn={}, e={}", blackMarket.getActivitySn(), blackMarket.getSn(), e.getMessage(), e);
        }
        return reportList;
    }

    private void refreshAll() {
        // 刷新黑市商品
        int actSn = 0;
        for (Map.Entry<Integer, Map<Integer, BlackMarket>> entry : groupEntityMap.entrySet()) {
            Map<Integer, BlackMarket> entityMap = entry.getValue();
            for (BlackMarket blackMarket : entityMap.values()) {
                actSn = blackMarket.getActivitySn();
                refresh(blackMarket);
            }
        }
        // 通知所有服务器刷新黑市商品
        ConfActivityControl conf = ConfActivityControl.get(actSn);
        int type = conf.type;
        for (Integer group : groupEntityMap.keySet()) {
            AdminCenterServiceProxy prx = AdminCenterManager.createAdminProxy();
            prx.getCrossServers(crossType.getType(), 0, group);
            prx.listenResult((result, context) -> {
                List<Integer> serverIdList = result.get("servers");
                List<Integer> mainServerIdList = new ArrayList<>();
                for (Integer serverId : serverIdList) {
                    int mainServerId = Util.getServerIdReal(serverId);
                    if (!mainServerIdList.contains(mainServerId)) {
                        mainServerIdList.add(mainServerId);
                    }
                }
                for (int mainServerId : mainServerIdList) {
                    int serverId = Utils.intValue(Config.GAME_SERVER_PREFIX) + mainServerId;
                    HumanGlobalServiceProxy humanGlobalPrx = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(serverId));
                    humanGlobalPrx.sendMsgToAll(null, MsgAct.act_black_market_refresh_s2c.newBuilder().setActType(type).build());
                }
            });
        }
        // 设置下次刷新时间
        long currTime = Port.getTime();
        long nextTime = ActivityManager.inst().getNextFreshTime(actSn);
        if (nextTime != 0) {
            scheduleOnce(new ScheduleTask() {
                @Override
                public String getJobName() {
                    return JOB_NAME_REFRESH;
                }

                @Override
                public void execute() {
                    refreshAll();
                }
            }, nextTime - currTime);
        }
    }

    private void openPreRewardAll() {
        int actSn = 0;
        Map<Integer, List<Define.p_act_broadcast_report>> reportMap = new HashMap<>();
        for (Map.Entry<Integer, Map<Integer, BlackMarket>> entry : groupEntityMap.entrySet()) {
            int group = entry.getKey();
            Map<Integer, BlackMarket> entityMap = entry.getValue();
            for (BlackMarket blackMarket : entityMap.values()) {
                actSn = blackMarket.getActivitySn();
                List<Define.p_act_broadcast_report> reportTempList = openPreReward(blackMarket);
                if (reportTempList == null || reportTempList.isEmpty())
                    continue;
                reportMap.computeIfAbsent(group, k -> new ArrayList<>()).addAll(reportTempList);
            }
        }
        for (Map.Entry<Integer, List<Define.p_act_broadcast_report>> entry : reportMap.entrySet()) {
            int group = entry.getKey();
            List<Define.p_act_broadcast_report> reportList = entry.getValue();
            sendNewBroadCast(group, reportList);
            saveBroadCast(group);
        }
        long currTime = Port.getTime();
        long nextTime = ActivityManager.inst().getNextOpenPreTime(actSn);
        if (nextTime != 0) {
            scheduleOnce(new ScheduleTask() {
                @Override
                public String getJobName() {
                    return JOB_NAME_PRE_REWARD;
                }

                @Override
                public void execute() {
                    openPreRewardAll();
                }
            }, nextTime - currTime);
        }
    }

    /**
     * 添加新的播报记录
     */
    private Define.p_act_broadcast_report addNewBroadCast(int group, int sn, int num, int goodsIndex, long humanId, String name, boolean isHide) {
        List<Define.p_act_broadcast_report> broadCastList = groupBroadCastMap.computeIfAbsent(group, k -> new ArrayList<>());
        if (broadCastList.size() >= 30) {
            int delNum = broadCastList.size() - 30 + 1;
            for (int i = 0; i < delNum; i++) {
                broadCastList.remove(0);
            }
        }
        Define.p_act_broadcast_report.Builder pBroadcast = Define.p_act_broadcast_report.newBuilder();
        pBroadcast.setRoleId(humanId);
        pBroadcast.setTargetName(name);
        pBroadcast.setPaySn(sn);
        pBroadcast.setTime(Port.getTime());
        pBroadcast.setValue(num);
        pBroadcast.setIsHideName(isHide ? 1 : 0);
        pBroadcast.addExtList(Define.p_key_string3.newBuilder().setK("index").setS(String.valueOf(goodsIndex)));
        Define.p_act_broadcast_report broadcast = pBroadcast.build();
        broadCastList.add(broadcast);
        return broadcast;
    }

    /**
     * 发送新的播报记录
     */
    private void sendNewBroadCast(int group, List<Define.p_act_broadcast_report> newBroadCastList) {
        int actSn = groupActSnMap.get(group);
        ConfActivityControl conf = ConfActivityControl.get(actSn);
        MsgAct.act_broadcast_s2c.Builder msg = MsgAct.act_broadcast_s2c.newBuilder();
        msg.setOperationType(1);
        msg.setActType(conf.type);
        msg.addAllReportList(newBroadCastList);
        // 通知所有服务器刷新黑市商品
        AdminCenterServiceProxy prx = AdminCenterManager.createAdminProxy();
        prx.getCrossServers(crossType.getType(), 0, group);
        prx.listenResult((result, context) -> {
            List<Integer> serverIdList = result.get("servers");
            List<Integer> mainServerIdList = new ArrayList<>();
            for (Integer serverId : serverIdList) {
                int mainServerId = Util.getServerIdReal(serverId);
                if (!mainServerIdList.contains(mainServerId)) {
                    mainServerIdList.add(mainServerId);
                }
            }
            for (int mainServerId : mainServerIdList) {
                int serverId = Utils.intValue(Config.GAME_SERVER_PREFIX) + mainServerId;
                HumanGlobalServiceProxy humanGlobalPrx = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(serverId));
                humanGlobalPrx.sendMsgToAll(null, msg.build());
            }
        });
    }

    /**
     * 保存播报记录
     */
    private void saveBroadCast(int group) {
        List<Define.p_act_broadcast_report> broadCastList = groupBroadCastMap.computeIfAbsent(group, k -> new ArrayList<>());
        String key = getBroadCastRedisKey(group);
        RedisTools.del(CrossRedis.getClient(), key, (result) -> {
            if (result.failed()) {
                Log.activity.error("[黑市]保存广播失败, group={}, e={}", group, result.cause().getMessage(), result.cause());
            }
            List<String> strList = new ArrayList<>();
            for (Define.p_act_broadcast_report report : broadCastList) {
                JSONObject jo = new JSONObject();
                jo.put("id", report.getRoleId());
                jo.put("n", report.getTargetName());
                jo.put("v", report.getValue());
                jo.put("t", report.getTime());
                jo.put("s", report.getPaySn());
                jo.put("hide", report.getIsHideName());
                if (!report.getExtListList().isEmpty()) {
                    JSONObject extJO = new JSONObject();
                    for (Define.p_key_string3 ext : report.getExtListList()) {
                        extJO.put(ext.getK(), ext.getS());
                    }
                    jo.put("ext", extJO.toJSONString());
                }
                strList.add(jo.toJSONString());
            }
            RedisTools.pushToList(CrossRedis.getClient(), key, strList);
        });
    }

    private String getBroadCastRedisKey(int group) {
        int actSn = groupActSnMap.get(group);
        ConfActivityControl conf = ConfActivityControl.get(actSn);
        return Utils.createStr("{}.{}.{}.{}", RedisKeys.actBroadcast, group, conf.type, actSn);
    }

    @DistrMethod
    public void getBlackMarketBroadCast(int serverId, int actType) {
        int group = GlobalConfVal.getCrossServerIdGroup(crossType.getType(), serverId);
        if (group == 0) {
            Log.activity.error("[黑市]购买物品失败, 跨服分组不存在, serverId={}", serverId);
            port.returns("result", new ReasonResult(false, ErrorTip.SystemDefault));
            return;
        }
        MsgAct.act_broadcast_s2c.Builder msg = MsgAct.act_broadcast_s2c.newBuilder();
        msg.setOperationType(0);
        msg.setActType(actType);
        msg.addAllReportList(groupBroadCastMap.getOrDefault(group, new ArrayList<>()));
        port.returns("result", new ReasonResult(true), "msg", msg.build());
    }

    /**
     * 跨服黑市启动
     */
    @DistrMethod
    public void blackMarketOpen(int activitySn) {
        Log.activity.info("[黑市]跨服启动, activitySn={}", activitySn);
        AdminCenterServiceProxy prx = AdminCenterManager.createAdminProxy();
        prx.getCrossGroup(crossType.getType(), Config.SERVER_ID);
        prx.listenResult((result, context) -> {
            Set<Integer> groupSet = result.get("groupSet");
            if (groupSet.isEmpty()) {
                Log.activity.error("[黑市]跨服启动失败, 取不到跨服的组!");
                return;
            }
            // 收集所有本区的黑市配置
            List<ConfBlackMarket> confList = new ArrayList<>();
            for (ConfBlackMarket conf : ConfBlackMarket.findAll()) {
                if (conf.mall_tab == 1)
                    confList.add(conf);
            }
            // 遍历跨服分组
            for (Integer group : groupSet) {
                Log.activity.info("[黑市]跨服分组开始查询entity, activitySn={}, group={}", activitySn, group);
                groupActSnMap.remove(group);
                String whereSql = Utils.createStr(" where activitySn={} and groupId={} ", activitySn, group);
                DB db = DB.newInstance(BlackMarket.tableName);
                db.findByQuery(false, whereSql);
                db.listenResult((result1, context1) -> {
                    // 加载黑市商品数据
                    groupEntityMap.remove(group);
                    groupMallJoinMap.remove(group);
                    groupMallJoinNameMap.remove(group);
                    groupMallJoinHideMap.remove(group);
                    Map<Integer, BlackMarket> entityMap = groupEntityMap.computeIfAbsent(group, k -> new HashMap<>());
                    Map<Integer, Map<Long, Integer>> mallJoinMap = groupMallJoinMap.computeIfAbsent(group, k -> new HashMap<>());
                    Map<Integer, Map<Long, String>> mallJoinNamMap = groupMallJoinNameMap.computeIfAbsent(group, k -> new HashMap<>());
                    Map<Integer, Map<Long, Boolean>> mallJoinHideMap = groupMallJoinHideMap.computeIfAbsent(group, k -> new HashMap<>());
                    List<Record> records = result1.get();
                    for (Record record : records) {
                        BlackMarket blackMarket = new BlackMarket(record);
                        entityMap.put(blackMarket.getSn(), blackMarket);
                        try {
                            Define.p_key_key_string_list pkList = Define.p_key_key_string_list.parseFrom(blackMarket.getJoinHumanList());
                            for (Define.p_key_string pks : pkList.getListList()) {
                                long humanId = pks.getK();
                                String[] parts = pks.getS().split(",");
                                mallJoinMap.computeIfAbsent(blackMarket.getSn(), k -> new HashMap<>()).put(humanId, Integer.parseInt(parts[0]));
                                mallJoinNamMap.computeIfAbsent(blackMarket.getSn(), k -> new HashMap<>()).put(humanId, parts[1]);
                                mallJoinHideMap.computeIfAbsent(blackMarket.getSn(), k -> new HashMap<>()).put(humanId, Integer.parseInt(parts[0]) == 1);
                            }
                        } catch (InvalidProtocolBufferException e) {
                            Log.activity.error("[黑市]跨服分组解析购买玩家列表失败, activitySn={}, group={}, sn={}, e={}", activitySn, group, blackMarket.getSn(), e.getMessage(), e);
                        }
                    }

                    List<Define.p_act_broadcast_report> newBroadcastList = new ArrayList<>();
                    for (ConfBlackMarket conf : confList) {
                        BlackMarket blackMarket = entityMap.get(conf.sn);
                        if (blackMarket == null) {
                            blackMarket = new BlackMarket();
                            blackMarket.setId(Port.applyId());
                            blackMarket.setActivitySn(activitySn);
                            blackMarket.setGroupId(group);
                            blackMarket.setSn(conf.sn);
                            blackMarket.setGoodsIndex(0);
                            blackMarket.setBuyNum(0);
                            blackMarket.setNextUpdateTime(0);
                            blackMarket.persist();
                            entityMap.put(conf.sn, blackMarket);
                        } else {
                            List<Define.p_act_broadcast_report> list = openPreReward(blackMarket);// 处理预购开奖
                            if (list != null && !list.isEmpty()) {
                                newBroadcastList.addAll(list);
                            }
                        }
                        refresh(blackMarket);// 处理刷新
                    }
                    groupActSnMap.put(group, activitySn);
                    // 加载黑市广播数据
                    RedisTools.getListRange(CrossRedis.getClient(), getBroadCastRedisKey(group), 0, -1, (result2) -> {
                        if (result2.failed()) {
                            Log.activity.error("[黑市]跨服分组加载广播数据失败, activitySn={}, group={}, e={}", activitySn, group, result2.cause().getMessage(), result2.cause());
                            return;
                        }
                        groupBroadCastMap.remove(group);
                        List<Define.p_act_broadcast_report> broadcastList = new ArrayList<>();
                        for (Object obj : result2.result()) {
                            JSONObject jo = JSONObject.parseObject(obj.toString());
                            Define.p_act_broadcast_report.Builder report = Define.p_act_broadcast_report.newBuilder();
                            report.setRoleId(jo.getLongValue("id"));
                            report.setPaySn(jo.getIntValue("s"));
                            report.setTargetName(jo.getString("n"));
                            report.setValue(jo.getIntValue("v"));
                            report.setTime(jo.getLongValue("t"));
                            if (jo.containsKey("hide")) {
                                report.setIsHideName(jo.getIntValue("hide"));
                            }
                            if (jo.containsKey("ext")) {
                                JSONObject extJO = JSONObject.parseObject(jo.getString("ext"));
                                for (String key : extJO.keySet()) {
                                    report.addExtList(Define.p_key_string3.newBuilder().setK(key).setS(extJO.getString(key)));
                                }
                            }
                            broadcastList.add(report.build());
                        }
                        if (!newBroadcastList.isEmpty()) {
                            broadcastList.addAll(newBroadcastList);
                            if (broadcastList.size() > 30) {
                                broadcastList = broadcastList.subList(broadcastList.size() - 30, broadcastList.size() - 1);
                            }
                            saveBroadCast(group);
                        }
                        groupBroadCastMap.put(group, broadcastList);
                    });
                });
            }
        });
        long currTime = Port.getTime();
        long nextFreshTime = ActivityManager.inst().getNextFreshTime(activitySn);
        if (nextFreshTime != 0) {
            scheduleOnce(new ScheduleTask() {
                @Override
                public String getJobName() {
                    return JOB_NAME_REFRESH;
                }

                @Override
                public void execute() {
                    refreshAll();
                }
            }, nextFreshTime - currTime);
        }
        long nextOpenPreTime = ActivityManager.inst().getNextOpenPreTime(activitySn);
        if (nextOpenPreTime != 0) {
            scheduleOnce(new ScheduleTask() {
                @Override
                public String getJobName() {
                    return JOB_NAME_PRE_REWARD;
                }

                @Override
                public void execute() {
                    openPreRewardAll();
                }
            }, nextOpenPreTime - currTime);
        }
    }

    /**
     * 跨服黑市关闭
     */
    @DistrMethod
    public void blackMarketClose(int actSn) {
        Log.activity.info("[黑市]跨服活动关闭, actSn={}", actSn);
        AdminCenterServiceProxy prx = AdminCenterManager.createAdminProxy();
        prx.getCrossGroup(CrossType.cross_black_market.getType(), Config.SERVER_ID);
        prx.listenResult((result, context) -> {
            Set<Integer> groupSet = groupEntityMap.keySet();
            for (Integer group : groupSet) {
                Map<Integer, BlackMarket> blackMarketMap = groupEntityMap.get(group);
                for (BlackMarket blackMarket : blackMarketMap.values()) {
                    openPreReward(blackMarket);// 活动结束的结算不需要播报
                }
                RedisTools.del(CrossRedis.getClient(), getBroadCastRedisKey(group));
            }
            groupActSnMap.clear();
            groupEntityMap.clear();
            groupMallJoinMap.clear();
            groupMallJoinNameMap.clear();
            groupMallJoinHideMap.clear();
            groupBroadCastMap.clear();
            try {
                deleteSchedulerJob(JOB_NAME_REFRESH);
                deleteSchedulerJob(JOB_NAME_PRE_REWARD);
            } catch (Exception e) {
                Log.activity.error("[黑市]跨服活动关闭, 关闭定时器失败, e={}", e.getMessage(), e);
            }
        });
    }

    @DistrMethod
    public void getItemInfo(int serverId, long humanId, int sn) {
        int group = GlobalConfVal.getCrossServerIdGroup(crossType.getType(), serverId);
        if (group == 0) {
            Log.activity.error("[黑市]购买物品失败, 跨服分组不存在, serverId={}", serverId);
            port.returns("result", new ReasonResult(false, ErrorTip.SystemDefault));
            return;
        }
        if (!groupActSnMap.containsKey(group) || groupActSnMap.get(group) == 0) {
            Log.activity.error("[黑市]购买物品失败, 跨服分组还未启动, serverId={}, group={}", serverId, group);
            port.returns("result", new ReasonResult(false, ErrorTip.SystemDefault));
            return;
        }
        Map<Integer, BlackMarket> entityMap = groupEntityMap.getOrDefault(group, new HashMap<>());
        BlackMarket blackMarket = entityMap.get(sn);
        if (blackMarket == null) {
            Log.activity.error("[黑市]购买物品失败, 跨服分组对应的物品不存在, serverId={}, group={}, sn={}", serverId, group, sn);
            port.returns("result", new ReasonResult(false, ErrorTip.SystemDefault));
            return;
        }
        ConfBlackMarket conf = ConfBlackMarket.get(sn);
        if (conf == null) {
            Log.activity.error("[黑市]购买物品失败, 没有配置, serverId={}, group={}, sn={}", serverId, group, sn);
            port.returns("result", new ReasonResult(false, ErrorTip.SystemDefault));
            return;
        }
        BlackMarketVO vo = new BlackMarketVO();
        vo.sn = sn;
        vo.goodsIndex = blackMarket.getGoodsIndex();
        vo.buyNum = blackMarket.getBuyNum();
        if (conf.cell_type == 2) {
            Map<Integer, Map<Long, Integer>> mallJoinMap = groupMallJoinMap.getOrDefault(group, new HashMap<>());
            Map<Long, Integer> joinMap = mallJoinMap.getOrDefault(sn, new HashMap<>());
            vo.buyNum = joinMap.getOrDefault(humanId, 0);
        }
        port.returns("result", new ReasonResult(true), "vo", vo);
    }


    @DistrMethod
    public void buyItem(int serverId, long humanId, int sn, int num, String name, boolean isHide) {
        int group = GlobalConfVal.getCrossServerIdGroup(crossType.getType(), serverId);
        if (group == 0) {
            Log.activity.error("[黑市]购买物品失败, 跨服分组不存在, serverId={}", serverId);
            port.returns("result", new ReasonResult(false, ErrorTip.SystemDefault));
            return;
        }
        if (!groupActSnMap.containsKey(group) || groupActSnMap.get(group) == 0) {
            Log.activity.error("[黑市]购买物品失败, 跨服分组还未启动, serverId={}, group={}", serverId, group);
            port.returns("result", new ReasonResult(false, ErrorTip.SystemDefault));
            return;
        }
        Map<Integer, BlackMarket> entityMap = groupEntityMap.getOrDefault(group, new HashMap<>());
        BlackMarket blackMarket = entityMap.get(sn);
        if (blackMarket == null) {
            Log.activity.error("[黑市]购买物品失败, 跨服分组对应的物品不存在, serverId={}, group={}, sn={}", serverId, group, sn);
            port.returns("result", new ReasonResult(false, ErrorTip.SystemDefault));
            return;
        }
        ConfBlackMarket conf = ConfBlackMarket.get(sn);
        if (conf == null) {
            Log.activity.error("[黑市]购买物品失败, 没有配置, serverId={}, group={}, sn={}", serverId, group, sn);
            port.returns("result", new ReasonResult(false, ErrorTip.SystemDefault));
            return;
        }
        List<BlackMarketVO> voList = new ArrayList<>();
        ReasonResult result = new ReasonResult(false, ErrorTip.SystemDefault);
        if (conf.cell_type == 2) {
            // 预购
            if (blackMarket.isOpenPre()) {
                Log.activity.error("[黑市]购买物品失败, 该预约已开奖, serverId={}, group={}, sn={}", serverId, group, sn);
                port.returns("result", new ReasonResult(false, ErrorTip.SystemDefault));
                return;
            }
            Map<Integer, Map<Long, Integer>> mallJoinMap = groupMallJoinMap.computeIfAbsent(group, k -> new HashMap<>());
            Map<Long, Integer> joinMap = mallJoinMap.computeIfAbsent(sn, k -> new HashMap<>());
            int buyNum = joinMap.getOrDefault(humanId, 0);
            ConfMall confMall = ConfMall.get(conf.goods[blackMarket.getGoodsIndex()][0]);
            BlackMarketVO vo = new BlackMarketVO();
            vo.sn = sn;
            vo.goodsIndex = blackMarket.getGoodsIndex();
            vo.buyNum = buyNum;
            if (buyNum + num > confMall.limit) {
                result = new ReasonResult(false, ErrorTip.CountNotEnough);
            } else {
                result = new ReasonResult(true);
                joinMap.put(humanId, buyNum + num);
                vo.buyNum = buyNum + num;
                Map<Integer, Map<Long, String>> mallJoinNameMap = groupMallJoinNameMap.computeIfAbsent(group, k -> new HashMap<>());
                Map<Long, String> joinNameMap = mallJoinNameMap.computeIfAbsent(sn, k -> new HashMap<>());
                joinNameMap.put(humanId, name);
                Map<Integer, Map<Long, Boolean>> mallJoinHideMap = groupMallJoinHideMap.computeIfAbsent(group, k -> new HashMap<>());
                Map<Long, Boolean> joinHideMap = mallJoinHideMap.computeIfAbsent(sn, k -> new HashMap<>());
                joinHideMap.put(humanId, isHide);
                // 更新回entity
                Define.p_key_key_string_list.Builder byteData = Define.p_key_key_string_list.newBuilder();
                byteData.setK(sn);
                for (Map.Entry<Long, Integer> entry : joinMap.entrySet()) {
                    long id = entry.getKey();
                    Define.p_key_string.Builder byteItem = Define.p_key_string.newBuilder();
                    byteItem.setK(id);
                    byteItem.setS(vo.buyNum + "," + joinNameMap.getOrDefault(id, "") + "," + (joinHideMap.getOrDefault(id, false) ? 1 : 0));
                    byteData.addList(byteItem);
                }
                blackMarket.setJoinHumanList(byteData.build().toByteArray());
            }
            voList.add(vo);
            if (conf.is_show == 1) {
                groupMallJoinNameMap.computeIfAbsent(group, k -> new HashMap<>()).computeIfAbsent(sn, k -> new HashMap<>()).put(humanId, name);
                groupMallJoinHideMap.computeIfAbsent(group, k -> new HashMap<>()).computeIfAbsent(sn, k -> new HashMap<>()).put(humanId, isHide);
            }
        } else if (conf.cell_type == 1) {
            // 限购
            ConfMall confMall = ConfMall.get(conf.goods[blackMarket.getGoodsIndex()][0]);
            BlackMarketVO vo = new BlackMarketVO();
            vo.sn = sn;
            vo.goodsIndex = blackMarket.getGoodsIndex();
            vo.buyNum = blackMarket.getBuyNum();
            if (blackMarket.getBuyNum() >= confMall.limit) {
                result = new ReasonResult(false, ErrorTip.CountNotEnough);
            } else {
                blackMarket.setBuyNum(blackMarket.getBuyNum() + 1);
                vo.buyNum = blackMarket.getBuyNum();
                result = new ReasonResult(true);
            }
            voList.add(vo);
            if (conf.is_show == 1) {
                Define.p_act_broadcast_report broadcast = addNewBroadCast(group, sn, num, blackMarket.getGoodsIndex(), humanId, name, isHide);
                sendNewBroadCast(group, Collections.singletonList(broadcast));
                saveBroadCast(group);
            }
        }
        for (Map.Entry<Integer, BlackMarket> entry : entityMap.entrySet()) {
            int confSn = entry.getKey();
            if (confSn == sn) {
                continue;
            }
            ConfBlackMarket conf1 = ConfBlackMarket.get(confSn);
            if (conf1 == null || conf1.cell_type != 2) {
                continue;
            }
            BlackMarketVO blackMarketVO = new BlackMarketVO();
            blackMarketVO.sn = confSn;
            blackMarketVO.goodsIndex = entry.getValue().getGoodsIndex();
            blackMarketVO.buyNum = entry.getValue().getBuyNum();
            voList.add(blackMarketVO);
        }
        port.returns("result", result, "voList", voList);
    }

    @DistrMethod
    public void getBlackMarketInfo(int serverId, long humanId) {
        int group = GlobalConfVal.getCrossServerIdGroup(crossType.getType(), serverId);
        if (group == 0) {
            Log.activity.error("[黑市]购买限购物品, 跨服分组不存在, crossType={}, serverId={}", crossType, serverId);
            port.returns("result", new ReasonResult(false, ErrorTip.SystemDefault));
            return;
        }
        if (!groupActSnMap.containsKey(group) || groupActSnMap.get(group) == 0) {
            Log.activity.error("[黑市]购买限购物品, 跨服分组还未启动, crossType={}, serverId={}, group={}", crossType, serverId, group);
            port.returns("result", new ReasonResult(false, ErrorTip.SystemDefault));
            return;
        }
        Map<Integer, BlackMarket> entityMap = groupEntityMap.get(group);
        if (entityMap == null) {
            Log.activity.error("[黑市]购买限购物品, 跨服分组对应的物品不存在, crossType={}, serverId={}, group={}", crossType, serverId, group);
            port.returns("result", new ReasonResult(false, ErrorTip.SystemDefault));
            return;
        }
        Map<Integer, Map<Long, Integer>> mallJoinMap = groupMallJoinMap.getOrDefault(group, new HashMap<>());
        Map<Integer, BlackMarketVO> blackMarketVOMap = new HashMap<>();
        for (BlackMarket blackMarket : entityMap.values()) {
            BlackMarketVO blackMarketVO = new BlackMarketVO();
            blackMarketVO.sn = blackMarket.getSn();
            blackMarketVO.goodsIndex = blackMarket.getGoodsIndex();
            ConfBlackMarket conf = ConfBlackMarket.get(blackMarketVO.sn);
            if (conf.cell_type == 2) {
                blackMarketVO.buyNum = mallJoinMap.getOrDefault(blackMarketVO.sn, new HashMap<>()).getOrDefault(humanId, 0);
            } else {
                blackMarketVO.buyNum = blackMarket.getBuyNum();
            }
            blackMarketVOMap.put(blackMarketVO.sn, blackMarketVO);
        }
        port.returns("result", new ReasonResult(true), "voMap", blackMarketVOMap);
    }
}
