package org.gof.demo.distr.cross;

import io.vertx.core.AsyncResult;
import io.vertx.core.Future;
import io.vertx.core.Handler;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;
import io.vertx.redis.client.Command;
import io.vertx.redis.client.Redis;
import io.vertx.redis.client.Request;
import io.vertx.redis.client.Response;
import org.gof.core.Record;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.S;
import org.gof.core.support.Time;
import org.gof.demo.worldsrv.entity.HumanBrief;
import org.gof.demo.worldsrv.human.HumanBriefLoadType;
import org.gof.demo.worldsrv.support.Log;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CrossHumanLoader {
    //private HumanBrief humanBrief;

    public Long id;	//id
    public String name;	//名字1
    public Integer level;	//等级
    public Integer serverId;	//服务器id
    public Integer headSn;	//当前头像
    public Integer currentHeadFrameSn;	//当前头像框
    public Integer jobSn;	//职业sn
    public Integer currentTitleSn;	//当前头衔
    public Long guildId;	//公会ID
    public String guildName;	//帮会名称
    public Long topCombat;	//最高战斗力
    public byte[] battleRole;	//角色战斗数据
    public byte[] roleFigure;	//角色外观数据

    private void loadFromLocalDB(){
        if(!S.isGameServer){
            return;
        }
    }

    private final static String[] _baseInfoKeys = new String[]{HumanBrief.K.id,HumanBrief.K.name,
            HumanBrief.K.level, HumanBrief.K.serverId, HumanBrief.K.headSn, HumanBrief.K.currentHeadFrameSn,
            HumanBrief.K.jobSn, HumanBrief.K.currentTitleSn, HumanBrief.K.guildId, HumanBrief.K.guildName, HumanBrief.K.topCombat};

    private final static String[] _figureInfoKeys = new String[]{HumanBrief.K.id,HumanBrief.K.name,
            HumanBrief.K.level, HumanBrief.K.serverId, HumanBrief.K.headSn, HumanBrief.K.currentHeadFrameSn,
            HumanBrief.K.jobSn, HumanBrief.K.currentTitleSn, HumanBrief.K.guildId, HumanBrief.K.guildName,
            HumanBrief.K.topCombat,HumanBrief.K.roleFigure, HumanBrief.K.charmValue, HumanBrief.K.showMedalList};

    private final static String[] _battleInfoKeys = new String[]{HumanBrief.K.id,HumanBrief.K.name,
            HumanBrief.K.level, HumanBrief.K.serverId, HumanBrief.K.headSn, HumanBrief.K.currentHeadFrameSn,
            HumanBrief.K.jobSn, HumanBrief.K.currentTitleSn, HumanBrief.K.guildId, HumanBrief.K.guildName,HumanBrief.K.angelStar, HumanBrief.K.flyPetId, HumanBrief.K.flyPetSn,
            HumanBrief.K.topCombat,HumanBrief.K.roleFigure, HumanBrief.K.charmValue, HumanBrief.K.showMedalList, HumanBrief.K.currentPlanId,
            HumanBrief.K.battleRole, HumanBrief.K.planArrStr, HumanBrief.K.battleRole2, HumanBrief.K.battleRole3, HumanBrief.K.battleRole4, HumanBrief.K.battleRole5};

    private static void loadBaseInfoFromRedis(List<Long> humanIds, String[] fieldKeys, Handler<AsyncResult<List<HumanBrief>>> handler){
        List<Request> reqs = new ArrayList<>();
        Class<HumanBrief> cls = HumanBrief.class;
        String simpleName = cls.getSimpleName();
        for(Long humanId : humanIds) {
            Object[] args = new Object[fieldKeys.length+1];
            args[0]= simpleName +"."+humanId;
            System.arraycopy(fieldKeys,0,args,1,fieldKeys.length);
            Request request = Request.cmd(Command.HMGET, args);
            reqs.add(request);
        }
        RedisTools.doBatch(EntityManager.redis, reqs, batchRes->{
            if(batchRes.failed()){
                handler.handle(Future.failedFuture(batchRes.cause()));
                return;
            }
            //转换为对象
            List<Response> result = batchRes.result();
            List<HumanBrief> jsonResult = new ArrayList<>(result.size());
            for (Response resMulti: result) {
                if (resMulti == null) {
                    jsonResult.add(null);
                } else {
                    jsonResult.add(toHumanBrief(resMulti,fieldKeys));
                }
            }
            handler.handle(Future.succeededFuture(jsonResult));
        });
    }

    public static HumanBrief toHumanBrief(Response resMulti, String[] fieldKeys) {
        Class<HumanBrief> cls = HumanBrief.class;
        Method[] methods = cls.getDeclaredMethods();
        Map<String,Class<?>> methodMap = new HashMap<>();
        for(Method method : methods){
            String methodName = method.getName();
            if(methodName.startsWith("get")){
                String lowerCase = methodName.substring(3, methodName.length()).toLowerCase();
                methodMap.put(lowerCase,method.getReturnType());
            }else if(methodName.startsWith("is")){
                String lowerCase = methodName.substring(2, methodName.length()).toLowerCase();
                methodMap.put(lowerCase,method.getReturnType());
            }
        }
        JsonObject jsonObj = new JsonObject();
        for(int i=0;i<fieldKeys.length;i++){
            String field = fieldKeys[i];
            Object value = null;
            Response res = resMulti.get(i);
            if(res == null){
                if(field.equals(HumanBrief.K.roleFigure) || field.equals(HumanBrief.K.battleRole)){
                    // 如果查询的外观或战斗数据为空，则该humanBrief数据有问题，直接返回null
                    Log.game.error("查询humanBrief的外观或战斗数据为空, field={}, {}",field, resMulti);
                    return null;
                }
                Log.game.debug("redis response is null, field={}, {}",field, resMulti);
                continue;
            }
            switch (res.type()) {
                case NUMBER:
                    Class<?> returnType = methodMap.get(field.toLowerCase());
                    if(returnType==int.class||returnType==Integer.class){
                        value=res.toInteger();
                    }else if(returnType==long.class||returnType==Long.class){
                        value=res.toLong();
                    }
                    break;
                case BOOLEAN:
                    value= res.toBoolean();
                    break;
                case SIMPLE:
                    value= res.toString();
                    break;
                case BULK:
                    //原始字节方式
                    Buffer buffer = res.toBuffer();
                    int length = RedisTools.flagBytes.length;
                    if(buffer.length()>= length){
                        if(RedisTools.BYTE_STR_FLAG.equals(buffer.getString(0,length))){
                            value= buffer.getBytes(length,buffer.length());
                            break;
                        }
                    }
                    value= res.toString();
                    break;
                case MULTI:
                    //doNothing
            }
            jsonObj.put(field,value);
        }
        if(jsonObj.isEmpty()){
            // 如果数据全空，则该humanBrief数据有问题，直接返回null
            Log.game.error("查询humanBrief的数据全空, fieldKeys={}", (Object) fieldKeys);
            return null;
        }
        HumanBrief humanBrief = jsonObj.mapTo(HumanBrief.class);
        humanBrief.reset();
        return humanBrief;
    }

    public static void getHumanBrief(Long humanId, HumanBriefLoadType type, Handler<AsyncResult<HumanBrief>> handler) {
        String[] fieldKeys = null;
        if(type==HumanBriefLoadType.MAIN_INFO){
            fieldKeys = _baseInfoKeys;
        }else if(type==HumanBriefLoadType.SHOW_INFO){
            fieldKeys = _figureInfoKeys;
        }else if(type==HumanBriefLoadType.BATTLE_INFO){
            fieldKeys = _battleInfoKeys;
        }else{
            handler.handle(Future.failedFuture("未配置该类型的预设字段，HumanBriefLoadType="+type));
            return;
        }
        ArrayList<Long> ids = new ArrayList<>();
        ids.add(humanId);
        loadBaseInfoFromRedis(ids, fieldKeys, res->{
            if(res.failed()){
                handler.handle(Future.failedFuture(res.cause()));
                return;
            }
            List<HumanBrief> result = res.result();
            if(result==null||result.isEmpty()){
                handler.handle(Future.succeededFuture(null));
            }else{
                handler.handle(Future.succeededFuture(result.get(0)));
            }
        });
    }


    private static int batchSize = 100;

    public static void getList(List<Long> humanIds, HumanBriefLoadType type, Handler<AsyncResult<List<HumanBrief>>> handler) {
        String[] fieldKeys = null;
        if(type==HumanBriefLoadType.MAIN_INFO){
            fieldKeys = _baseInfoKeys;
        }else if(type==HumanBriefLoadType.SHOW_INFO){
            fieldKeys = _figureInfoKeys;
        }else if(type==HumanBriefLoadType.BATTLE_INFO){
            fieldKeys = _battleInfoKeys;
        }else{
            handler.handle(Future.failedFuture("未配置该类型的预设字段，HumanBriefLoadType="+type));
            return;
        }
        getList(humanIds,fieldKeys,handler);
    }

    public static void getList(List<Long> humanIds, String[] fieldKeys, Handler<AsyncResult<List<HumanBrief>>> handler) {
        if (humanIds.size() > batchSize) {
            List<Long> allIds = new ArrayList<>(humanIds);
            List<HumanBrief> dataList = new ArrayList<>();
            getListByPage(allIds, fieldKeys, dataList, handler);
        } else {
            loadBaseInfoFromRedis(humanIds, fieldKeys, handler);
        }
    }

    private static void getListByPage(List<Long> humanIds, String[] fieldKeys, List<HumanBrief> dataList, Handler<AsyncResult<List<HumanBrief>>> handler) {
        List<Long> batchIds = new ArrayList<>();
        int len = Math.min(batchSize, humanIds.size());
        for (int i = 0; i < len; i++) {
            batchIds.add(humanIds.remove(0));
        }
        loadBaseInfoFromRedis(batchIds, fieldKeys, res -> {
            if (res.failed()) {
                Log.game.error("load humanData failed! batchIds={}", batchIds, res.cause());
            } else {
                List<HumanBrief> result = res.result();
                dataList.addAll(result);
            }
            //执行下一批次
            if (humanIds.isEmpty()) {
                //终止轮询，返回分页查询结果
                handler.handle(Future.succeededFuture(dataList));
                return;
            }
            getListByPage(humanIds, fieldKeys, dataList, handler);
        });
    }

    public static String[] getKeys(HumanBriefLoadType type){
        String[] fieldKeys = null;
        if(type==HumanBriefLoadType.MAIN_INFO){
            fieldKeys = _baseInfoKeys;
        }else if(type==HumanBriefLoadType.SHOW_INFO){
            fieldKeys = _figureInfoKeys;
        }else if(type==HumanBriefLoadType.BATTLE_INFO){
            fieldKeys = _battleInfoKeys;
        }else{
            Log.temp.error("未配置该类型的预设字段，HumanBriefLoadType="+type);
            return fieldKeys;
        }
        return fieldKeys;
    }
}
